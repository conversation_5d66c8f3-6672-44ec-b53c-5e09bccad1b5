# 仓库系统Feign接口使用说明

## 概述

售后系统为仓库系统提供了专门的Feign接口 `WarehouseAftersaleFacade`，仓库系统可以通过这个接口与售后系统进行交互。

## Feign接口位置

```
cashop-aftersale-facade/src/main/java/com/cashop/aftersale/facade/client/WarehouseAftersaleFacade.java
```

## 接口列表

### 1. 仓库创建次品售后申请

**接口方法**: `createDefectAftersale`
**请求路径**: `POST /api/warehouse/aftersale/create`
**功能**: 仓库发现次品时创建售后申请

```java
Result<WarehouseAftersaleCreateVO> createDefectAftersale(WarehouseAftersaleCreateRequest request);
```

**使用示例**:
```java
@Service
public class WarehouseService {
    
    @Autowired
    private WarehouseAftersaleFacade warehouseAftersaleFacade;
    
    public void handleDefectProduct(String subOrderNo, Long skuId, Integer defectQuantity, String reason) {
        WarehouseAftersaleCreateRequest request = new WarehouseAftersaleCreateRequest();
        request.setSubOrderNo(subOrderNo);
        request.setSkuId(skuId);
        request.setDefectQuantity(defectQuantity);
        request.setDefectReason(reason);
        request.setOperatorId(getCurrentOperatorId());
        request.setOperatorName(getCurrentOperatorName());
        request.setWarehouseCode(getCurrentWarehouseCode());
        
        Result<WarehouseAftersaleCreateVO> result = warehouseAftersaleFacade.createDefectAftersale(request);
        
        if (result.isSuccess()) {
            log.info("次品售后申请创建成功: {}", result.getData().getServiceNumber());
            // 处理成功逻辑
        } else {
            log.error("次品售后申请创建失败: {}", result.getMessage());
            // 处理失败逻辑
        }
    }
}
```

### 2. 仓库提交物流信息

**接口方法**: `submitLogisticsInfo`
**请求路径**: `POST /api/warehouse/aftersale/logistics`
**功能**: 仓库发货后提交物流信息

```java
Result<String> submitLogisticsInfo(WarehouseLogisticsInfoRequest request);
```

**使用示例**:
```java
public void submitShippingInfo(String serviceNumber, String logisticsCompany, String trackingNumber) {
    WarehouseLogisticsInfoRequest request = new WarehouseLogisticsInfoRequest();
    request.setServiceNumber(serviceNumber);
    request.setLogisticsCompany(logisticsCompany);
    request.setTrackingNumber(trackingNumber);
    request.setShippingTime(LocalDateTime.now());
    request.setOperatorId(getCurrentOperatorId());
    request.setOperatorName(getCurrentOperatorName());
    
    // 设置物流详细信息
    Map<String, Object> logisticsInfo = new HashMap<>();
    logisticsInfo.put("weight", "2.5kg");
    logisticsInfo.put("volume", "0.01m³");
    logisticsInfo.put("packageType", "纸箱");
    request.setLogisticsInfo(logisticsInfo);
    
    Result<String> result = warehouseAftersaleFacade.submitLogisticsInfo(request);
    
    if (result.isSuccess()) {
        log.info("物流信息提交成功: {}", serviceNumber);
        // 处理成功逻辑
    } else {
        log.error("物流信息提交失败: {}", result.getMessage());
        // 处理失败逻辑
    }
}
```

### 3. 仓库售后状态查询

**接口方法**: `checkOutboundAftersale`
**请求路径**: `POST /api/warehouse/outbound/check`
**功能**: 查询商品售后状态供出库参考

```java
Result<WarehouseOutboundCheckVO> checkOutboundAftersale(WarehouseOutboundCheckRequest request);
```

**使用示例**:
```java
public boolean checkCanOutbound(String outboundOrderNo, List<OutboundItem> items) {
    WarehouseOutboundCheckRequest request = new WarehouseOutboundCheckRequest();
    request.setOutboundOrderNo(outboundOrderNo);
    request.setOperatorId(getCurrentOperatorId());
    request.setOperatorName(getCurrentOperatorName());
    
    List<WarehouseOutboundCheckRequest.OutboundCheckItem> checkItems = new ArrayList<>();
    for (OutboundItem item : items) {
        WarehouseOutboundCheckRequest.OutboundCheckItem checkItem = 
            new WarehouseOutboundCheckRequest.OutboundCheckItem();
        checkItem.setSubOrderNo(item.getSubOrderNo());
        checkItem.setSkuId(item.getSkuId());
        checkItem.setRequestQuantity(item.getQuantity());
        checkItems.add(checkItem);
    }
    request.setItems(checkItems);
    
    Result<WarehouseOutboundCheckVO> result = warehouseAftersaleFacade.checkOutboundAftersale(request);
    
    if (result.isSuccess()) {
        WarehouseOutboundCheckVO checkResult = result.getData();
        
        // 根据售后状态信息自行决定是否出库
        for (WarehouseOutboundCheckVO.OutboundCheckResult itemResult : checkResult.getItems()) {
            if (itemResult.getOngoingAftersaleQuantity() > 0) {
                log.warn("商品 {} 有在途售后 {} 件，请谨慎出库", 
                    itemResult.getSkuId(), itemResult.getOngoingAftersaleQuantity());
                
                // 仓库根据自己的业务规则决定是否允许出库
                // 例如：如果在途售后数量超过可出库数量的50%，则不允许出库
                if (itemResult.getOngoingAftersaleQuantity() > itemResult.getAvailableQuantity() * 0.5) {
                    return false;
                }
            }
        }
        return true;
    } else {
        log.error("售后状态查询失败: {}", result.getMessage());
        // 查询失败时的处理策略，可以选择允许出库或不允许出库
        return false;
    }
}
```

## 配置说明

### 1. Maven依赖

在仓库系统的 `pom.xml` 中添加售后系统facade模块的依赖：

```xml
<dependency>
    <groupId>com.cashop</groupId>
    <artifactId>cashop-aftersale-facade</artifactId>
    <version>${project.version}</version>
</dependency>
```

### 2. Feign配置

确保仓库系统启用了Feign客户端：

```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.cashop.aftersale.facade.client")
public class WarehouseApplication {
    public static void main(String[] args) {
        SpringApplication.run(WarehouseApplication.class, args);
    }
}
```

### 3. 服务发现配置

在 `application.yml` 中配置售后服务的地址：

```yaml
# 如果使用服务发现
eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/eureka/

# 如果直接配置服务地址
cashop-aftersale-service:
  ribbon:
    listOfServers: http://localhost:8080
```

## 错误处理

接口提供了降级处理机制，当售后服务不可用时会返回友好的错误信息：

- "仓库创建次品售后申请失败，请稍后重试"
- "仓库提交物流信息失败，请稍后重试"  
- "仓库售后状态查询失败，请稍后重试"

仓库系统应该根据这些错误信息进行相应的处理，比如重试、记录日志、通知运维等。

## 注意事项

1. **职责边界**: 售后系统只提供接口和通知功能，不关心仓库的具体业务逻辑
2. **出库决策**: 售后状态查询接口只提供信息，仓库系统根据返回信息自行决定是否出库
3. **异常处理**: 建议对所有接口调用都进行异常处理，确保仓库系统的稳定性
4. **日志记录**: 建议记录所有接口调用的日志，便于问题排查和监控

## 联系方式

如有问题，请联系售后系统开发团队。
