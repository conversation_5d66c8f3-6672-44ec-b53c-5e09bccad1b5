-- 售后系统退款科目和退款条件调整
-- 版本: V1.2.0
-- 描述: 调整退款金额字段结构，支持新的退款科目

-- =====================================================
-- 1. 修改 as_order 表 - 添加新的退款金额字段
-- =====================================================

-- 添加货款退款金额字段
ALTER TABLE `as_order` 
ADD COLUMN `product_refund_amount` decimal(10,2) DEFAULT NULL COMMENT '货款退款金额（按件退）' AFTER `actual_refund_amount`;

-- 添加增值服务费退款金额字段
ALTER TABLE `as_order` 
ADD COLUMN `value_added_service_refund_amount` decimal(10,2) DEFAULT NULL COMMENT '增值服务费退款金额（按件退）' AFTER `product_refund_amount`;

-- 添加退货服务费字段
ALTER TABLE `as_order` 
ADD COLUMN `return_service_fee` decimal(10,2) DEFAULT NULL COMMENT '退货服务费（仓库作业费 + 仓库退商家仓运费）' AFTER `value_added_service_refund_amount`;

-- 添加退货服务费退款状态字段
ALTER TABLE `as_order` 
ADD COLUMN `return_service_fee_refund_status` tinyint(1) DEFAULT 0 COMMENT '退货服务费退款状态:0-未退款,1-已退款' AFTER `return_service_fee`;

-- =====================================================
-- 2. 修改 as_refund_detail 表 - 支持国内段运费记录
-- =====================================================

-- 添加国内段运费信息字段
ALTER TABLE `as_refund_detail` 
ADD COLUMN `domestic_shipping_info` text DEFAULT NULL COMMENT '国内段运费信息（仅退款类型为3时使用）' AFTER `refund_time`;

-- =====================================================
-- 3. 为新增字段添加索引
-- =====================================================

CREATE INDEX `idx_as_order_return_service_fee_refund_status` ON `as_order` (`return_service_fee_refund_status`);
CREATE INDEX `idx_as_refund_detail_refund_type` ON `as_refund_detail` (`refund_type`);

-- =====================================================
-- 4. 更新表注释
-- =====================================================

-- 更新 as_order 表注释
ALTER TABLE `as_order` COMMENT = '售后订单表 - 包含用户角色、订单关联、金额信息和退款科目';

-- 更新 as_refund_detail 表注释
ALTER TABLE `as_refund_detail` COMMENT = '售后退款明细表 - 支持多种退款类型包括国内段运费';

-- =====================================================
-- 5. 数据初始化（可选）
-- =====================================================

-- 将现有数据的退货服务费退款状态默认设置为未退款
UPDATE `as_order` SET `return_service_fee_refund_status` = 0 WHERE `return_service_fee_refund_status` IS NULL;

-- =====================================================
-- 6. 验证脚本
-- =====================================================

-- 验证 as_order 表新增字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'as_order' 
  AND COLUMN_NAME IN ('product_refund_amount', 'value_added_service_refund_amount', 'return_service_fee', 'return_service_fee_refund_status')
ORDER BY ORDINAL_POSITION;

-- 验证 as_refund_detail 表新增字段
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'as_refund_detail' 
  AND COLUMN_NAME = 'domestic_shipping_info'
ORDER BY ORDINAL_POSITION;

-- 验证索引创建
SHOW INDEX FROM `as_order` WHERE Key_name LIKE '%return_service_fee%';
SHOW INDEX FROM `as_refund_detail` WHERE Key_name LIKE '%refund_type%';
