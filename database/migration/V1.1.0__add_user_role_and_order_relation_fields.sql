-- 售后系统数据库表结构升级脚本
-- 版本: V1.1.0
-- 描述: 添加用户角色、订单关联和金额信息字段

-- =====================================================
-- 1. 修改 as_order 表
-- =====================================================

-- 添加用户角色字段
ALTER TABLE `as_order` 
ADD COLUMN `user_role` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户角色:1-购买者,2-店主' AFTER `user_id`;

-- 添加订单关联字段
ALTER TABLE `as_order` 
ADD COLUMN `cashop_order_no` varchar(64) DEFAULT NULL COMMENT 'Cashop二级单号' AFTER `sub_order_no`,
ADD COLUMN `sku_order_no` varchar(64) DEFAULT NULL COMMENT 'SKU单号' AFTER `cashop_order_no`;

-- 添加金额信息字段
ALTER TABLE `as_order` 
ADD COLUMN `actual_paid_amount` decimal(10,2) DEFAULT NULL COMMENT '实付金额' AFTER `apply_amount`,
ADD COLUMN `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠金额' AFTER `actual_paid_amount`;

-- 为新增字段添加索引
CREATE INDEX `idx_as_order_user_role` ON `as_order` (`user_role`);
CREATE INDEX `idx_as_order_cashop_order_no` ON `as_order` (`cashop_order_no`);
CREATE INDEX `idx_as_order_sku_order_no` ON `as_order` (`sku_order_no`);

-- =====================================================
-- 2. 修改 as_product 表
-- =====================================================

-- 添加商品售价字段
ALTER TABLE `as_product` 
ADD COLUMN `sale_price` decimal(10,2) DEFAULT NULL COMMENT '商品售价' AFTER `unit_price`;

-- =====================================================
-- 3. 更新表注释
-- =====================================================

-- 更新 as_order 表注释
ALTER TABLE `as_order` COMMENT = '售后订单表 - 包含用户角色、订单关联和金额信息';

-- 更新 as_product 表注释
ALTER TABLE `as_product` COMMENT = '售后商品表 - 包含商品售价信息';

-- =====================================================
-- 4. 数据初始化（可选）
-- =====================================================

-- 将现有数据的用户角色默认设置为购买者
UPDATE `as_order` SET `user_role` = 1 WHERE `user_role` IS NULL;

-- =====================================================
-- 5. 验证脚本
-- =====================================================

-- 验证 as_order 表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'as_order' 
  AND COLUMN_NAME IN ('user_role', 'cashop_order_no', 'sku_order_no', 'actual_paid_amount', 'discount_amount')
ORDER BY ORDINAL_POSITION;

-- 验证 as_product 表结构
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'as_product' 
  AND COLUMN_NAME = 'sale_price'
ORDER BY ORDINAL_POSITION;
