<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cashop</groupId>
    <artifactId>cashop-aftersale</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>cashop-aftersale</name>
    <description>售后管理系统</description>

    <modules>
        <module>cashop-aftersale-common</module>
        <module>cashop-aftersale-dao</module>
        <module>cashop-aftersale-facade</module>
        <module>cashop-aftersale-service</module>
        <module>cashop-aftersale-web</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>true</skipTests>

        <!-- Spring Boot 版本 -->
        <spring-boot.version>2.7.18</spring-boot.version>
        <spring-cloud.version>2021.0.8</spring-cloud.version>

        <!-- 数据库相关 -->
        <mysql.version>8.0.33</mysql.version>
        <mybatis.version>3.5.14</mybatis.version>
        <hikaricp.version>5.0.1</hikaricp.version>

        <!-- 日志相关 - 使用与Spring Boot 2.7.18兼容的SLF4J 1.x -->
        <log4j2.version>2.21.1</log4j2.version>
        <slf4j.version>1.7.36</slf4j.version>
        <logback.version>1.2.12</logback.version>

        <!-- 缓存相关 -->
        <jedis.version>4.4.3</jedis.version>

        <!-- 配置中心 -->
        <apollo.version>2.1.0</apollo.version>

        <!-- API文档 -->
        <springdoc.version>1.7.0</springdoc.version>
        <swagger-annotations.version>2.2.8</swagger-annotations.version>

        <!-- 验证 -->
        <hibernate-validator.version>6.2.5.Final</hibernate-validator.version>

        <!-- 工具库 -->
        <commons-lang3.version>3.14.0</commons-lang3.version>
        <dependencies>
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 事务一致性框架 -->
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>transaction-framework</artifactId>
                <version>${transaction-framework.version}</version>
            </dependency>
            <!-- 基础 DTO 包 -->
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>common-model</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- 事务一致性框架 -->
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>transaction-framework</artifactId>
                <version>${transaction-framework.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.2.2</version>
                    <configuration>
                        <skipTests>${skipTests}</skipTests>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project> 