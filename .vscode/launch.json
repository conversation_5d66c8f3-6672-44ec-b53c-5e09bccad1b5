{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "type": "java",
            "name": "Current File",
            "request": "launch",
            "mainClass": "${file}"
        },
        {
            "type": "java",
            "name": "AftersaleApplication",
            "request": "launch",
            "mainClass": "com.cashop.aftersale.web.AftersaleApplication",
            "projectName": "cashop-aftersale-web",
            "args": "--spring.profiles.active=local",
            "vmArgs": "-Dhttp.nonProxyHosts=* -Denv=stable -Dapollo.meta=http://apollo.castable.hk:8080 -Dmybatis-plus.global-config.enable-sql-runner=false"
        },
        {
            "type": "java",
            "name": "Application",
            "request": "launch",
            "mainClass": "com.cashop.aftersale.web.Application",
            "projectName": "cashop-aftersale-web",
            "args": "--spring.profiles.active=local",
            "vmArgs": "-Dhttp.nonProxyHosts=* -Denv=stable -Dapollo.meta=http://apollo.castable.hk:8080 -Dmybatis-plus.global-config.enable-sql-runner=false"
        }
    ]
}