# 售后系统 Facade 模块

## 概述

本模块提供售后系统的对外接口定义，包括 Feign 客户端、DTO、VO 等，供其他系统调用。

## 主要功能

### 1. SKU 单售后支持批量检查接口

批量检查指定 SKU 单是否支持申请售后，用于前端判断显示申请售后按钮还是售后进度按钮。

#### 接口信息
- **路径**: `POST /api/third-party/aftersale/support/check`
- **Feign客户端**: `AftersaleThirdPartyClient.checkSkuAftersaleSupport()`

#### 请求参数 (SkuAftersaleSupportRequest)
```json
{
  "subOrderNos": [                 // 子订单号列表 (必填)
    "SO202312010001",
    "SO202312010002",
    "SO202312010003"
  ],
  "userId": 789                    // 用户ID (可选，用于权限验证)
}
```

#### 响应结果 (SkuAftersaleSupportVO)
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [                     // SKU单检查结果列表
      {
        "subOrderNo": "SO202312010001",
        "supportAftersale": true,  // 是否支持申请售后
        "supportStatus": 1,        // 售后支持状态码
        "statusMessage": "支持申请售后", // 状态描述
        "unsupportedReason": null, // 不支持原因(当supportAftersale=false时)
        "productInfo": {           // 商品信息
          "skuId": 123456,
          "productName": "商品名称",
          "skuName": "SKU名称",
          "purchaseQuantity": 2,
          "unitPrice": 99.99,
          "currencyCode": "USD",
          "totalAmount": 199.98
        },
        "aftersaleStatistics": {   // 售后统计信息
          "appliedQuantity": 0,    // 已申请售后数量
          "refundedQuantity": 0,   // 已退款数量
          "availableQuantity": 2,  // 可申请数量
          "totalRefundedAmount": 0.00, // 已退款总金额
          "availableRefundAmount": 199.98 // 可退款金额
        },
        "ongoingAftersales": []    // 在途售后单列表
      },
      {
        "subOrderNo": "SO202312010002",
        "supportAftersale": false,
        "supportStatus": 3,
        "statusMessage": "有在途售后申请，暂不支持继续申请",
        "unsupportedReason": "该SKU单当前有在途的售后申请，请等待处理完成后再申请",
        "productInfo": { /* ... */ },
        "aftersaleStatistics": { /* ... */ },
        "ongoingAftersales": [
          {
            "serviceNumber": "AS202312010001",
            "asType": 2,
            "asTypeDesc": "发货后退款",
            "asStatus": 1,
            "asStatusDesc": "申请中",
            "applyQuantity": 1,
            "applyAmount": 99.99
          }
        ]
      }
    ]
  }
}
```

#### 状态码说明

| 状态码 | 含义 | 前端处理建议 |
|--------|------|-------------|
| 1 | 支持申请售后 | 显示"申请售后"按钮 |
| 2 | 已有售后记录 | 显示"售后进度"按钮 |
| 3 | 有在途售后申请 | 显示"售后进度"按钮，提示不能继续申请 |
| 4 | 已全部退完 | 不显示售后相关按钮 |
| 5 | 系统异常 | 显示错误信息或隐藏按钮 |

## 使用示例

### Spring Boot 项目中使用

1. **添加依赖**
```xml
<dependency>
    <groupId>com.cashop</groupId>
    <artifactId>cashop-aftersale-facade</artifactId>
    <version>1.0.0</version>
</dependency>
```

2. **启用 Feign 客户端**
```java
@SpringBootApplication
@EnableFeignClients(basePackages = "com.cashop.aftersale.facade.client")
public class OrderApplication {
    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
    }
}
```

3. **注入并使用**
```java
@Service
public class OrderAftersaleService {
    
    @Autowired
    private AftersaleThirdPartyClient aftersaleClient;
    
    public Map<String, Boolean> batchCheckAftersaleSupport(List<String> subOrderNos, Long userId) {
        SkuAftersaleSupportRequest request = new SkuAftersaleSupportRequest();
        request.setSubOrderNos(subOrderNos);
        request.setUserId(userId);
        
        Result<SkuAftersaleSupportVO> result = aftersaleClient.checkSkuAftersaleSupport(request);
        
        Map<String, Boolean> supportMap = new HashMap<>();
        
        if (result.isSuccess() && result.getData() != null && result.getData().getItems() != null) {
            for (SkuAftersaleSupportVO.SkuAftersaleSupportItem item : result.getData().getItems()) {
                supportMap.put(item.getSubOrderNo(), item.getSupportAftersale());
            }
        } else {
            // 调用失败，默认不支持
            for (String subOrderNo : subOrderNos) {
                supportMap.put(subOrderNo, false);
            }
        }
        
        return supportMap;
    }
}
```

### 前端使用示例

```javascript
// 批量检查售后支持状态
async function batchCheckAftersaleSupport(subOrderNos) {
  try {
    const response = await fetch('/api/third-party/aftersale/support/check', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        subOrderNos: subOrderNos,
        userId: getCurrentUserId()
      })
    });
    
    const result = await response.json();
    
    if (result.code === 200 && result.data && result.data.items) {
      result.data.items.forEach(item => {
        const subOrderNo = item.subOrderNo;
        
        switch (item.supportStatus) {
          case 1: // 支持申请售后
            showAftersaleButton(subOrderNo, true);
            break;
          case 2: // 已有售后记录
          case 3: // 有在途售后
            showAftersaleProgressButton(subOrderNo, item.ongoingAftersales);
            break;
          case 4: // 已全部退完
            hideAftersaleButton(subOrderNo);
            break;
          case 5: // 系统异常
            showErrorMessage(subOrderNo, item.statusMessage, item.unsupportedReason);
            break;
        }
      });
    }
  } catch (error) {
    console.error('批量检查售后支持状态失败:', error);
    // 异常时隐藏所有按钮
    subOrderNos.forEach(subOrderNo => hideAftersaleButton(subOrderNo));
  }
}

// 单个SKU单处理函数示例
function showAftersaleButton(subOrderNo, show) {
  const button = document.querySelector(`[data-sub-order="${subOrderNo}"] .aftersale-btn`);
  if (button) {
    button.style.display = show ? 'block' : 'none';
    button.textContent = '申请售后';
  }
}

function showAftersaleProgressButton(subOrderNo, ongoingAftersales) {
  const button = document.querySelector(`[data-sub-order="${subOrderNo}"] .aftersale-btn`);
  if (button) {
    button.style.display = 'block';
    button.textContent = '售后进度';
    button.onclick = () => showAftersaleProgress(ongoingAftersales);
  }
}

function hideAftersaleButton(subOrderNo) {
  const button = document.querySelector(`[data-sub-order="${subOrderNo}"] .aftersale-btn`);
  if (button) {
    button.style.display = 'none';
  }
}

function showErrorMessage(subOrderNo, message, reason) {
  const container = document.querySelector(`[data-sub-order="${subOrderNo}"] .aftersale-container`);
  if (container) {
    container.innerHTML = `<span class="error-msg">${reason || message}</span>`;
  }
}
```

## 降级处理

当售后系统不可用时，Feign 客户端会自动降级，返回系统异常状态，确保调用方不会因为售后系统故障而受到影响。

## 注意事项

1. **性能考虑**: 该接口会查询数据库，建议在必要时才调用，可考虑添加缓存
2. **权限验证**: userId 参数可用于权限验证，确保用户只能查询自己的订单
3. **错误处理**: 调用方应妥善处理各种状态码和异常情况
4. **监控告警**: 建议对接口调用进行监控，及时发现问题

## 版本历史

- v1.0.0: 初始版本，提供 SKU 售后支持检查功能