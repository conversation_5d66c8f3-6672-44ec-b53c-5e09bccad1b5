package com.cashop.aftersale.facade.constants;

/**
 * 售后系统API常量定义
 * Facade层 - API接口常量
 */
public class AftersaleApiConstants {

    /**
     * 第三方接口路径
     */
    public static class ThirdPartyApi {
        
        /**
         * 基础路径
         */
        public static final String BASE_PATH = "/api/third-party";
        
        /**
         * 检查SKU售后支持状态
         */
        public static final String CHECK_SKU_AFTERSALE_SUPPORT = "/aftersale/support/check";
        
        /**
         * 接收第三方消息
         */
        public static final String RECEIVE_MESSAGE = "/message/receive";
    }

    /**
     * 售后支持状态码
     */
    public static class SupportStatus {
        
        /**
         * 支持申请售后
         */
        public static final Integer SUPPORT_APPLY = 1;
        
        /**
         * 已有售后记录（显示售后进度）
         */
        public static final Integer HAS_AFTERSALE_RECORD = 2;
        
        /**
         * 有在途售后，不能继续申请
         */
        public static final Integer HAS_ONGOING_AFTERSALE = 3;
        
        /**
         * 已全部退完，不支持申请
         */
        public static final Integer FULLY_REFUNDED = 4;
        
        /**
         * 商品信息异常或系统异常
         */
        public static final Integer SYSTEM_ERROR = 5;
    }

    /**
     * 售后支持状态描述
     */
    public static class SupportStatusMessage {
        
        public static final String SUPPORT_APPLY = "支持申请售后";
        public static final String HAS_AFTERSALE_RECORD = "已有售后记录，可查看售后进度";
        public static final String HAS_ONGOING_AFTERSALE = "有在途售后申请，暂不支持继续申请";
        public static final String FULLY_REFUNDED = "商品已全部退完，不支持申请售后";
        public static final String SYSTEM_ERROR = "系统异常";
        public static final String PRODUCT_NOT_FOUND = "商品不存在";
        public static final String SERVICE_UNAVAILABLE = "售后系统暂时不可用，请稍后重试";
    }

    /**
     * 不支持申请售后的原因说明
     */
    public static class UnsupportedReason {
        
        /**
         * 有在途售后申请
         */
        public static final String HAS_ONGOING_AFTERSALE = "该SKU单当前有在途的售后申请，请等待处理完成后再申请";
        
        /**
         * 商品已全部退完
         */
        public static final String FULLY_REFUNDED = "该SKU单的商品已全部退款完成，无法再次申请售后";
        
        /**
         * 商品不存在
         */
        public static final String PRODUCT_NOT_FOUND = "未找到该子订单的商品信息，请确认订单号是否正确";
        
        /**
         * 系统异常
         */
        public static final String SYSTEM_ERROR = "系统处理异常，请稍后重试或联系客服";
        
        /**
         * 订单状态不符合
         */
        public static final String ORDER_STATUS_INVALID = "订单状态不符合售后申请条件";
        
        /**
         * 超过售后期限
         */
        public static final String AFTERSALE_EXPIRED = "已超过售后申请期限";
        
        /**
         * 商品已下架或删除
         */
        public static final String PRODUCT_UNAVAILABLE = "商品已下架或删除，无法申请售后";
    }
}
