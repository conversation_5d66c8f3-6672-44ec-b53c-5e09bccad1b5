package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后流程记录DTO
 */
@Data
@Schema(description = "售后流程记录DTO")
public class AftersaleProcessLogDTO {

    @Schema(
        description = "流程记录ID",
        example = "1"
    )
    private Long id;

    @Schema(
        description = "售后订单ID",
        example = "1001"
    )
    private Long asOrderId;

    @Schema(
        description = "原状态",
        example = "1"
    )
    private Integer fromStatus;

    @Schema(
        description = "目标状态",
        example = "2"
    )
    private Integer toStatus;

    @Schema(
        description = "操作人ID",
        example = "12345"
    )
    private Long operatorId;

    @Schema(
        description = "操作人姓名",
        example = "张三"
    )
    private String operatorName;

    @Schema(
        description = "备注",
        example = "客服审核通过"
    )
    private String remark;

    @Schema(
        description = "创建时间",
        example = "2025-01-01T10:00:00"
    )
    private LocalDateTime createdTime;
}
