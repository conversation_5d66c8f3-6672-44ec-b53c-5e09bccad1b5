package com.cashop.aftersale.facade.client;

import com.cashop.aftersale.facade.constants.AftersaleApiConstants;
import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;
import com.cashop.common.base.response.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 售后系统第三方接口Feign客户端降级处理工厂
 * Facade层 - Feign降级处理
 */
@Slf4j
@Component
public class AftersaleThirdPartyClientFallbackFactory implements FallbackFactory<AftersaleThirdPartyClient> {

    @Override
    public AftersaleThirdPartyClient create(Throwable cause) {
        return new AftersaleThirdPartyClient() {
            @Override
            public Result<SkuAftersaleSupportVO> checkSkuAftersaleSupport(SkuAftersaleSupportRequest request) {
                log.error("调用售后系统批量检查SKU支持状态失败: subOrderNos={}", 
                        request.getSubOrderNos(), cause);
                
                // 降级响应：为每个子订单返回系统异常状态
                List<SkuAftersaleSupportVO.SkuAftersaleSupportItem> fallbackItems = new ArrayList<>();
                
                if (request.getSubOrderNos() != null) {
                    for (String subOrderNo : request.getSubOrderNos()) {
                        SkuAftersaleSupportVO.SkuAftersaleSupportItem item = SkuAftersaleSupportVO.SkuAftersaleSupportItem.builder()
                                .subOrderNo(subOrderNo)
                                .supportAftersale(false)
                                .supportStatus(AftersaleApiConstants.SupportStatus.SYSTEM_ERROR)
                                .statusMessage(AftersaleApiConstants.SupportStatusMessage.SERVICE_UNAVAILABLE)
                                .unsupportedReason(AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR)
                                .build();
                        fallbackItems.add(item);
                    }
                }
                
                SkuAftersaleSupportVO fallbackResult = SkuAftersaleSupportVO.builder()
                        .items(fallbackItems)
                        .build();
                
                return Result.success(fallbackResult);
            }
        };
    }
}
