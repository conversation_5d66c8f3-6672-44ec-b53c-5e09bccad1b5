package com.cashop.aftersale.facade.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款明细VO
 * Facade层 - 退款明细视图对象
 */
@Data
public class RefundDetailVO {

    /**
     * 明细ID
     */
    private Long id;

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 退款类型:1-商品退款,2-服务费退款,3-运费退款
     */
    private Integer refundType;

    /**
     * 退款类型文本
     */
    private String refundTypeText;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 格式化金额显示
     */
    private String displayAmount;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款明细描述
     */
    private String refundDescription;

    /**
     * 商品相关信息(仅商品退款时显示)
     */
    private Long productId;
    private Long skuId;
    private String productName;
    private Integer refundQuantity;
    private BigDecimal unitPrice;
    private String displayUnitPrice;

    /**
     * 服务费相关信息(仅服务费退款时显示)
     */
    private Integer serviceFeeQuantity;
    private BigDecimal unitServiceFee;
    private String displayUnitServiceFee;

    /**
     * 退款状态:1-待退款,2-退款中,3-退款成功,4-退款失败
     */
    private Integer refundStatus;

    /**
     * 退款状态文本
     */
    private String refundStatusText;

    /**
     * 退款交易ID
     */
    private String refundTransactionId;

    /**
     * 退款完成时间
     */
    private LocalDateTime refundTime;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
