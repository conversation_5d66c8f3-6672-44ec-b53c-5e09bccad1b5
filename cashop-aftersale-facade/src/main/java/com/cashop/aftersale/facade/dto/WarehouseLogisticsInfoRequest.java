package com.cashop.aftersale.facade.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 仓库填写物流信息请求DTO
 * Facade层 - 仓库系统接口请求对象
 */
@Data
public class WarehouseLogisticsInfoRequest {

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 物流公司
     */
    private String logisticsCompany;

    /**
     * 物流单号
     */
    private String trackingNumber;

    /**
     * 发货时间
     */
    private LocalDateTime shippingTime;

    /**
     * 物流详细信息
     */
    private Map<String, Object> logisticsInfo;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;
}
