package com.cashop.aftersale.facade.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 运费明细VO
 * 接口层 - 运费展示对象
 */
@Data
public class ShippingFeeDetailVO {

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 申请件数
     */
    private Integer applyQuantity;

    /**
     * 退货体积
     */
    private BigDecimal returnVolume;

    /**
     * 退货重量
     */
    private BigDecimal returnWeight;

    /**
     * 国际运费
     */
    private BigDecimal shippingFee;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 总物流费用
     */
    private BigDecimal totalLogisticsFee;

    /**
     * 币种
     */
    private String currencyCode;

    /**
     * 支付截止时间
     */
    private LocalDateTime expireTime;

    /**
     * 剩余支付时间(分钟)
     */
    private Integer remainingMinutes;

    /**
     * 支付状态
     */
    private Integer logisticsPaid;

    /**
     * 支付状态文本
     */
    private String paidStatusText;
}
