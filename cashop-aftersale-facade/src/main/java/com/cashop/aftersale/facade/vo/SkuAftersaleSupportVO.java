package com.cashop.aftersale.facade.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * SKU售后支持批量检查响应VO
 * Facade层 - 第三方接口响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SkuAftersaleSupportVO {

    /**
     * SKU单售后支持检查结果列表
     */
    private List<SkuAftersaleSupportItem> items;

    /**
     * SKU单售后支持检查结果项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuAftersaleSupportItem {

        /**
         * 子订单号
         */
        private String subOrderNo;

        /**
         * 是否支持申请售后
         */
        private Boolean supportAftersale;

        /**
         * 售后支持状态码
         * 1: 支持申请售后
         * 2: 已有售后申请（显示售后进度）
         * 3: 有在途售后，不能继续申请
         * 4: 已全部退完，不支持申请
         * 5: 商品不存在或其他异常
         */
        private Integer supportStatus;

        /**
         * 状态描述
         */
        private String statusMessage;

        /**
         * 不支持申请售后的原因说明（当supportAftersale=false时）
         */
        private String unsupportedReason;

        /**
         * 商品信息
         */
        private ProductInfo productInfo;

        /**
         * 售后统计信息
         */
        private AftersaleStatistics aftersaleStatistics;

        /**
         * 当前在途售后单列表（如果有）
         */
        private List<OngoingAftersaleInfo> ongoingAftersales;
    }

    /**
     * 商品信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProductInfo {
        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 商品名称
         */
        private String productName;

        /**
         * SKU名称
         */
        private String skuName;

        /**
         * 购买数量
         */
        private Integer purchaseQuantity;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 币种
         */
        private String currencyCode;

        /**
         * 总金额
         */
        private BigDecimal totalAmount;
    }

    /**
     * 售后统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AftersaleStatistics {
        /**
         * 已申请售后数量
         */
        private Integer appliedQuantity;

        /**
         * 已退款数量
         */
        private Integer refundedQuantity;

        /**
         * 可申请数量
         */
        private Integer availableQuantity;

        /**
         * 已退款总金额
         */
        private BigDecimal totalRefundedAmount;

        /**
         * 可退款金额
         */
        private BigDecimal availableRefundAmount;
    }

    /**
     * 在途售后信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OngoingAftersaleInfo {
        /**
         * 售后单号
         */
        private String serviceNumber;

        /**
         * 售后类型
         */
        private Integer asType;

        /**
         * 售后类型描述
         */
        private String asTypeDesc;

        /**
         * 售后状态
         */
        private Integer asStatus;

        /**
         * 售后状态描述
         */
        private String asStatusDesc;

        /**
         * 申请数量
         */
        private Integer applyQuantity;

        /**
         * 申请金额
         */
        private BigDecimal applyAmount;
    }
}

