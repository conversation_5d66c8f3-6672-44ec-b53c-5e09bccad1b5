package com.cashop.aftersale.facade.client.fallback;

import com.cashop.aftersale.facade.client.WarehouseAftersaleFacade;
import com.cashop.aftersale.facade.dto.WarehouseAftersaleCreateRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsInfoRequest;
import com.cashop.aftersale.facade.dto.WarehouseOutboundCheckRequest;
import com.cashop.aftersale.facade.vo.WarehouseAftersaleCreateVO;
import com.cashop.aftersale.facade.vo.WarehouseOutboundCheckVO;
import com.cashop.common.base.response.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 仓库系统售后接口降级处理工厂
 */
@Component
public class WarehouseAftersaleFallbackFactory implements FallbackFactory<WarehouseAftersaleFacade> {

    private static final Logger logger = LoggerFactory.getLogger(WarehouseAftersaleFallbackFactory.class);

    @Override
    public WarehouseAftersaleFacade create(Throwable cause) {
        return new WarehouseAftersaleFacade() {

            @Override
            public Result<WarehouseAftersaleCreateVO> createDefectAftersale(WarehouseAftersaleCreateRequest request) {
                logger.error("Feign call to createDefectAftersale failed, request: {}", request, cause);
                return Result.error("仓库创建次品售后申请失败，服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<String> submitLogisticsInfo(WarehouseLogisticsInfoRequest request) {
                logger.error("Feign call to submitLogisticsInfo failed, request: {}", request, cause);
                return Result.error("仓库提交物流信息失败，服务暂时不可用，请稍后重试");
            }

            @Override
            public Result<WarehouseOutboundCheckVO> checkOutboundAftersale(WarehouseOutboundCheckRequest request) {
                logger.error("Feign call to checkOutboundAftersale failed, request: {}", request, cause);
                // For a query, it's often better to return a specific VO indicating the failure state.
                WarehouseOutboundCheckVO fallbackResult = new WarehouseOutboundCheckVO();
                fallbackResult.setCanOutbound(false); // Default to not allowing outbound on failure.
                fallbackResult.setReason("查询售后状态失败，服务降级，请稍后重试或联系技术支持");
                return Result.success(fallbackResult);
            }
        };
    }
}
