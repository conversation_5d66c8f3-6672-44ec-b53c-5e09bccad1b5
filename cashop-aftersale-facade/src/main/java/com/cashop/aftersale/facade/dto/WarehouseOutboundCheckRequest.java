package com.cashop.aftersale.facade.dto;

import lombok.Data;

import java.util.List;

/**
 * 仓库出库售后检查请求DTO
 * Facade层 - 仓库系统接口请求对象
 */
@Data
public class WarehouseOutboundCheckRequest {

    /**
     * 出库单号
     */
    private String outboundOrderNo;

    /**
     * 检查项目列表
     */
    private List<OutboundCheckItem> items;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 出库检查项目
     */
    @Data
    public static class OutboundCheckItem {
        /**
         * 子订单号
         */
        private String subOrderNo;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 请求出库数量
         */
        private Integer requestQuantity;
    }
}
