package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 仓库物流信息填写请求DTO
 * 供仓库系统调用，填写物流信息和增值服务费
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description = "仓库物流信息填写请求")
public class WarehouseLogisticsRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "售后单号", example = "AS202501220001", required = true)
    @NotBlank(message = "售后单号不能为空")
    private String serviceNumber;

    @Schema(description = "仓库物流公司", example = "顺丰速运", required = true)
    @NotBlank(message = "物流公司不能为空")
    private String warehouseLogisticsCompany;

    @Schema(description = "仓库物流单号", example = "SF1234567890", required = true)
    @NotBlank(message = "物流单号不能为空")
    private String warehouseTrackingNumber;

    @Schema(description = "发货时间", example = "2025-01-22T10:30:00", required = true)
    @NotNull(message = "发货时间不能为空")
    private LocalDateTime warehouseShippingTime;

    @Schema(description = "物流信息备注", example = "已发货，预计3天内到达")
    private String warehouseLogisticsInfo;

    @Schema(description = "增值服务费", example = "25.50", required = true)
    @NotNull(message = "增值服务费不能为空")
    @Positive(message = "增值服务费必须大于0")
    private BigDecimal valueAddedServiceFee;

    @Schema(description = "增值服务费币种", example = "USD", required = true)
    @NotBlank(message = "币种不能为空")
    private String currencyCode;

    @Schema(description = "增值服务费说明", example = "包装费 + 质检费")
    private String valueAddedServiceDescription;

    @Schema(description = "操作人员", example = "warehouse_system", required = true)
    @NotBlank(message = "操作人员不能为空")
    private String operatorId;
}
