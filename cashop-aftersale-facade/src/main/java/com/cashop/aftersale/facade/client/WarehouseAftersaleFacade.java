package com.cashop.aftersale.facade.client;

import com.cashop.aftersale.facade.dto.WarehouseAftersaleCreateRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsInfoRequest;
import com.cashop.aftersale.facade.dto.WarehouseOutboundCheckRequest;
import com.cashop.aftersale.facade.vo.WarehouseAftersaleCreateVO;
import com.cashop.aftersale.facade.vo.WarehouseOutboundCheckVO;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 仓库系统售后接口
 * 提供给仓库系统调用的Feign接口
 */
@Tag(
    name = "仓库售后接口",
    description = "提供给仓库系统的售后相关接口，包括次品申请、物流信息提交、售后状态查询等功能。售后系统作为接口提供方，负责接收仓库请求并通知相关系统，不关心仓库的具体业务逻辑。"
)
@FeignClient(
    name = "cashop-aftersale-service",
    path = "/api/warehouse",
    fallbackFactory = WarehouseAftersaleFacade.WarehouseAftersaleFallbackFactory.class
)
public interface WarehouseAftersaleFacade {

    @Operation(
        summary = "仓库创建次品售后申请",
        description = "仓库发现次品时创建售后申请。售后系统接收申请并通知第三方供货平台和相关系统。调用时机：仓库操作员发现商品次品时。",
        operationId = "createDefectAftersale"
    )
    @PostMapping("/aftersale/create")
    Result<WarehouseAftersaleCreateVO> createDefectAftersale(@RequestBody WarehouseAftersaleCreateRequest request);

    @Operation(
        summary = "仓库提交物流信息",
        description = "仓库填写并提交售后商品的物流信息。售后系统接收物流信息并通知第三方供货平台和相关系统。调用时机：仓库发货后填写物流信息时。",
        operationId = "submitLogisticsInfo"
    )
    @PostMapping("/aftersale/logistics")
    Result<String> submitLogisticsInfo(@RequestBody WarehouseLogisticsInfoRequest request);

    @Operation(
        summary = "仓库售后状态查询",
        description = "查询商品的售后状态信息供仓库参考。售后系统只提供信息，不做出库决策，不记录检查结果。调用时机：仓库出库前查询商品售后状态时。",
        operationId = "checkOutboundAftersale"
    )
    @PostMapping("/outbound/check")
    Result<WarehouseOutboundCheckVO> checkOutboundAftersale(@RequestBody WarehouseOutboundCheckRequest request);

    /**
     * 降级处理工厂类
     */
    @Component
    class WarehouseAftersaleFallbackFactory implements FallbackFactory<WarehouseAftersaleFacade> {
        
        private static final Logger logger = LoggerFactory.getLogger(WarehouseAftersaleFallbackFactory.class);

        @Override
        public WarehouseAftersaleFacade create(Throwable cause) {
            return new WarehouseAftersaleFacade() {
                
                @Override
                public Result<WarehouseAftersaleCreateVO> createDefectAftersale(WarehouseAftersaleCreateRequest request) {
                    logger.error("Feign call createDefectAftersale failed, request: {}", request, cause);
                    return Result.error("仓库创建次品售后申请失败，请稍后重试");
                }

                @Override
                public Result<String> submitLogisticsInfo(WarehouseLogisticsInfoRequest request) {
                    logger.error("Feign call submitLogisticsInfo failed, request: {}", request, cause);
                    return Result.error("仓库提交物流信息失败，请稍后重试");
                }

                @Override
                public Result<WarehouseOutboundCheckVO> checkOutboundAftersale(WarehouseOutboundCheckRequest request) {
                    logger.error("Feign call checkOutboundAftersale failed, request: {}", request, cause);
                    return Result.error("仓库售后状态查询失败，请稍后重试");
                }
            };
        }
    }
}
