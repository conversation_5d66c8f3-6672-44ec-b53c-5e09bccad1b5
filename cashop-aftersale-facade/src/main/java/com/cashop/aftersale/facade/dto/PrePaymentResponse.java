package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 预支付响应DTO
 * 返回给前端用于唤起支付
 */
@Data
@Schema(description = "预支付响应DTO")
public class PrePaymentResponse {

    @Schema(description = "预支付订单号", example = "PP202501010001")
    private String prepayId;

    @Schema(description = "支付交易ID", example = "TXN202501010001")
    private String transactionId;

    @Schema(description = "支付参数(JSON格式)，前端根据支付方式解析此参数唤起支付", 
            example = "{\"appId\":\"wx123\",\"timeStamp\":\"1640995200\",\"nonceStr\":\"abc123\"}")
    private String paymentParams;

    @Schema(description = "支付二维码(如果是扫码支付)", example = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...")
    private String qrCode;

    @Schema(description = "支付链接(如果是H5支付)", example = "https://pay.alipay.com/h5pay?token=abc123")
    private String paymentUrl;

    @Schema(description = "过期时间(秒)", example = "1800")
    private Long expireTime;

    @Schema(description = "支付方式", example = "ALIPAY", allowableValues = {"ALIPAY", "WECHAT", "CREDIT_CARD", "PAYPAL"})
    private String paymentMethod;

    @Schema(description = "订单状态", example = "PENDING", allowableValues = {"PENDING", "PAID", "FAILED", "EXPIRED"})
    private String orderStatus;
}
