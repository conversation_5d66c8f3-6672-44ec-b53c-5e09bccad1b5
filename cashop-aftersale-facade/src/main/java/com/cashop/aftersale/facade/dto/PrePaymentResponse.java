package com.cashop.aftersale.facade.dto;

import lombok.Data;

/**
 * 预支付响应DTO
 * 返回给前端用于唤起支付
 */
@Data
public class PrePaymentResponse {

    /**
     * 预支付订单号
     */
    private String prepayId;

    /**
     * 支付交易ID
     */
    private String transactionId;

    /**
     * 支付参数(JSON格式)
     * 前端根据支付方式解析此参数唤起支付
     */
    private String paymentParams;

    /**
     * 支付二维码(如果是扫码支付)
     */
    private String qrCode;

    /**
     * 支付链接(如果是H5支付)
     */
    private String paymentUrl;

    /**
     * 过期时间(秒)
     */
    private Long expireTime;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 订单状态
     */
    private String orderStatus;
}
