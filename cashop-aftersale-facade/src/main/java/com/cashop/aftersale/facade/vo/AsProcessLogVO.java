package com.cashop.aftersale.facade.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 售后流程记录VO
 * 接口层 - 流程记录展示对象
 */
@Data
public class AsProcessLogVO {

    /**
     * 操作类型
     */
    private Integer operationType;

    /**
     * 操作类型文本
     */
    private String operationTypeText;

    /**
     * 操作描述
     */
    private String operationDesc;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 操作时间
     */
    private LocalDateTime createdTime;
}
