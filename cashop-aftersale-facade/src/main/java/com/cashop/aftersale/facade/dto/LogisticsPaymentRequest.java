package com.cashop.aftersale.facade.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 物流费用支付请求DTO
 * 接口层 - 支付请求参数定义
 */
@Data
public class LogisticsPaymentRequest {

    /**
     * 售后单号
     */
    @NotBlank(message = "售后单号不能为空")
    private String serviceNumber;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal paymentAmount;

    /**
     * 支付方式
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    /**
     * 支付流水号
     */
    private String paymentTransactionId;
}
