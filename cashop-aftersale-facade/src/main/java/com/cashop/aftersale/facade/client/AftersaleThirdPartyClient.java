package com.cashop.aftersale.facade.client;

import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;
import com.cashop.common.base.response.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 售后系统第三方接口Feign客户端
 * Facade层 - 供其他系统调用的Feign接口
 */
@FeignClient(
    name = "cashop-aftersale-service",
    path = "/api/third-party",
    fallbackFactory = AftersaleThirdPartyClientFallbackFactory.class
)
public interface AftersaleThirdPartyClient {

    /**
     * 批量检查SKU单是否支持申请售后
     * 供订单系统调用，用于前端判断显示申请售后按钮还是售后进度按钮
     * 
     * @param request 批量检查请求参数，包含子订单号列表
     * @return 批量售后支持检查结果，每个SKU单对应一个结果项
     */
    @PostMapping("/aftersale/support/check")
    Result<SkuAftersaleSupportVO> checkSkuAftersaleSupport(@RequestBody SkuAftersaleSupportRequest request);
}
