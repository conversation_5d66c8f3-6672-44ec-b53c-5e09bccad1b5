package com.cashop.aftersale.facade.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 可退款商品VO
 * 接口层 - 商品展示对象
 */
@Data
public class RefundableProductVO {

    /**
     * 商品ID(as_product表的id)
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDescription;

    /**
     * 商品图片URL
     */
    private String productImageUrl;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 规格属性
     */
    private Map<String, String> skuAttributes;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 格式化价格显示
     */
    private String displayPrice;

    /**
     * 购买数量
     */
    private Integer purchaseQuantity;

    /**
     * 已申请数量
     */
    private Integer appliedQuantity;

    /**
     * 可申请数量
     */
    private Integer availableQuantity;

    /**
     * 单件体积
     */
    private BigDecimal unitVolume;

    /**
     * 单件重量
     */
    private BigDecimal unitWeight;

    /**
     * 商品状态
     */
    private Integer productStatus;

    /**
     * 状态文本
     */
    private String statusText;
}
