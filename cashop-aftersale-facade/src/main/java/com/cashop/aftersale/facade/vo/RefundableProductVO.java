package com.cashop.aftersale.facade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 可退款商品VO
 * 接口层 - 商品展示对象
 */
@Data
@Schema(description = "可退款商品信息")
public class RefundableProductVO {

    @Schema(description = "商品ID(as_product表的id)", example = "12345")
    private Long productId;

    @Schema(description = "SKU ID", example = "67890")
    private Long skuId;

    @Schema(description = "商品名称", example = "iPhone 15 Pro Max")
    private String productName;

    @Schema(description = "商品描述", example = "苹果iPhone 15 Pro Max 256GB 深空黑色")
    private String productDescription;

    @Schema(description = "商品图片URL", example = "https://img.cashop.com/iphone15.jpg")
    private String productImageUrl;

    @Schema(description = "SKU名称", example = "iPhone 15 Pro Max 256GB 深空黑色")
    private String skuName;

    @Schema(description = "规格属性", example = "{\"颜色\": \"深空黑色\", \"容量\": \"256GB\"}")
    private Map<String, String> skuAttributes;

    @Schema(description = "单价", example = "1199.99")
    private BigDecimal unitPrice;

    @Schema(description = "币种代码", example = "USD")
    private String currencyCode;

    @Schema(description = "格式化价格显示", example = "$1,199.99")
    private String displayPrice;

    @Schema(description = "购买数量", example = "3")
    private Integer purchaseQuantity;

    @Schema(description = "已申请数量", example = "1")
    private Integer appliedQuantity;

    @Schema(description = "可申请数量", example = "2")
    private Integer availableQuantity;

    @Schema(description = "单件体积(立方米)", example = "0.002")
    private BigDecimal unitVolume;

    @Schema(description = "单件重量(公斤)", example = "0.240")
    private BigDecimal unitWeight;

    @Schema(description = "商品状态", example = "1", allowableValues = {"1", "2", "3", "4"})
    private Integer productStatus;

    @Schema(description = "状态文本", example = "可申请售后")
    private String statusText;
}
