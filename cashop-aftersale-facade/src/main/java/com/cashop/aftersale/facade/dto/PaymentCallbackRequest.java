package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付回调请求DTO
 * 内部使用，用于接收支付系统回调数据
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description = "支付回调请求")
public class PaymentCallbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "支付交易流水号", example = "PAY20240726001", required = true)
    @NotBlank(message = "支付交易流水号不能为空")
    private String paymentTradeNo;
    
    @Schema(description = "业务订单号(售后单号)", example = "AS202501220001", required = true)
    @NotBlank(message = "业务订单号不能为空")
    private String businessOrderNo;
    
    @Schema(description = "支付状态", example = "SUCCESS", required = true)
    @NotBlank(message = "支付状态不能为空")
    private String status;
    
    @Schema(description = "实际支付金额", example = "100.50")
    private BigDecimal paidAmount;
    
    @Schema(description = "基础币种支付金额", example = "780.00")
    private BigDecimal basePaidAmount;
    
    @Schema(description = "支付币种", example = "USD", required = true)
    @NotBlank(message = "支付币种不能为空")
    private String currency;
    
    @Schema(description = "基础币种", example = "HKD")
    private String baseCurrency;
    
    @Schema(description = "支付完成时间", example = "2024-07-26T10:35:00")
    private LocalDateTime finishTime;
    
    @Schema(description = "第三方支付流水号", example = "TP20240726001")
    private String thirdPartyTradeNo;
    
    @Schema(description = "扩展信息", example = "{\"channel\":\"AEON_PAY\",\"method\":\"QR_CODE\"}")
    private String extInfo;

    @Schema(description = "用户ID", example = "12345")
    @NotNull(message = "用户ID不能为空")
    private Long userId;
}
