package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 售后申请请求DTO
 */
@Data
@Schema(description = "售后申请请求DTO")
public class AftersaleApplyRequestDTO {

    @Schema(
        description = "订单号，必填",
        example = "ORD202501010001",
        required = true
    )
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    @Schema(
        description = "子订单号，必填",
        example = "SUB202501010001",
        required = true
    )
    @NotBlank(message = "子订单号不能为空")
    private String subOrderNo;

    @Schema(
        description = "售后类型：1-退款，2-退货退款",
        example = "1",
        required = true
    )
    @NotNull(message = "售后类型不能为空")
    private Integer asType;

    @Schema(
        description = "申请原因",
        example = "商品质量问题"
    )
    private String applyReason;

    @Schema(
        description = "申请退款金额",
        example = "100.00"
    )
    private BigDecimal refundAmount;

    @Schema(
        description = "申请退货件数",
        example = "3",
        required = true
    )
    @NotNull(message = "申请退货件数不能为空")
    @Positive(message = "申请退货件数必须大于0")
    private Integer applyQuantity;
}
