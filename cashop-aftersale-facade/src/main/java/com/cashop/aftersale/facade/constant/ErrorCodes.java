package com.cashop.aftersale.facade.constant;

/**
 * 售后系统错误码定义
 */
public enum ErrorCodes {

    // 通用错误码
    SUCCESS("00000", "操作成功"),
    ARGUMENT_ERROR("A0001", "参数错误"),
    SYSTEM_ERROR("B0001", "系统异常"),
    UNDEFINED_ERROR("99999", "未知异常"),

    // 售后业务错误码
    AFTERSALE_ORDER_NOT_FOUND("A1001", "售后订单不存在"),
    AFTERSALE_ORDER_ALREADY_EXISTS("A1002", "售后订单已存在"),
    AFTERSALE_ORDER_STATUS_INVALID("A1003", "售后订单状态无效"),
    AFTERSALE_APPLY_QUANTITY_EXCEED("A1004", "申请退货件数超过购买件数"),
    AFTERSALE_ORDER_ALREADY_SHIPPED("A1005", "订单已发货，无法申请发货前退款"),
    AFTERSALE_LOGISTICS_FEE_NOT_PAID("A1006", "物流费用未支付"),
    AFTERSALE_LOGISTICS_FEE_EXPIRED("A1007", "物流费用支付已过期"),
    AFTERSALE_SUPPLIER_CALL_FAILED("A1008", "供货平台调用失败"),
    AFTERSALE_REFUND_FAILED("A1009", "退款失败"),
    AFTERSALE_PRODUCT_NOT_FOUND("A1010", "商品信息不存在");

    private final String code;
    private final String message;

    ErrorCodes(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static ErrorCodes fromCode(String code) {
        for (ErrorCodes errorCode : values()) {
            if (errorCode.code.equals(code)) {
                return errorCode;
            }
        }
        return UNDEFINED_ERROR;
    }
}
