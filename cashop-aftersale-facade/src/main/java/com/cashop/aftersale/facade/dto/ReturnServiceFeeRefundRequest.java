package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 退货服务费退款请求DTO
 * 用于用户撤销售后或商家拒绝时的服务费退还
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description = "退货服务费退款请求")
public class ReturnServiceFeeRefundRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "售后单号", example = "AS202501220001", required = true)
    @NotBlank(message = "售后单号不能为空")
    private String serviceNumber;

    @Schema(description = "用户ID", example = "12345", required = true)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "退款原因", example = "用户撤销售后申请", required = true)
    @NotBlank(message = "退款原因不能为空")
    private String refundReason;

    @Schema(description = "退款类型:1-用户撤销,2-商家拒绝", example = "1", required = true)
    @NotNull(message = "退款类型不能为空")
    private Integer refundReasonType;

    @Schema(description = "操作人员", example = "system", required = true)
    @NotBlank(message = "操作人员不能为空")
    private String operatorId;

    @Schema(description = "备注信息", example = "用户主动撤销售后申请，退还已支付的退货服务费")
    private String remark;
}
