package com.cashop.aftersale.facade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "售后申请页面渲染数据VO")
public class AftersaleApplyPageVO {

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "品牌Logo URL")
    private String brandLogo;

    @Schema(description = "实付货款")
    private BigDecimal actualPayment;

    @Schema(description = "增值服务费")
    private BigDecimal valueAddedServiceFee;

    @Schema(description = "售后类型描述 (e.g., '仅退款（发货前）')")
    private String afterSaleType;

    @Schema(description = "售后类型代码 (1:仅退款（发货前）, 2:仅退款（发货后）, 3:退货退款)")
    private Integer afterSaleTypeCode;

    @Schema(description = "商品列表")
    private List<ProductItem> productList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "售后申请页面商品信息")
    public static class ProductItem {

        @Schema(description = "商品备注")
        private String productRemark;

        @Schema(description = "商品规格")
        private String specifications;

        @Schema(description = "商品件数")
        private Integer quantity;

        @Schema(description = "商品实付金额")
        private BigDecimal actualAmount;
    }
}
