package com.cashop.aftersale.facade.client;

import com.cashop.aftersale.facade.dto.AftersaleApplyRequestDTO;
import com.cashop.aftersale.facade.dto.AftersaleDetailResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import com.cashop.common.base.response.PageResult;
import com.cashop.common.base.response.Result;

/**
 * 售后系统对外接口
 */
@Tag(
    name = "售后系统接口",
    description = "提供售后管理相关的所有功能，包括售后申请、查询、审核等操作。支持退款、退货退款等多种售后类型，提供完整的售后流程管理和状态跟踪。使用DTO进行数据传输，确保类型安全和接口规范。调用方式：通过FeignClient自动注入使用，支持服务降级和异常处理。"
)
@FeignClient(
    name = "cashop-aftersale-service",
    path = "/api/aftersale",
    fallbackFactory = AftersaleFacade.AftersaleFallbackFactory.class
)
public interface AftersaleFacade {

    @Operation(
        summary = "申请售后",
        description = "用户提交售后申请，支持退款和退货退款两种类型。调用时机：用户在订单详情页面点击申请售后按钮后。成功后返回售后单号，失败时根据具体错误类型返回相应错误码。",
        operationId = "applyAftersale"
    )
    @PostMapping("/apply")
    Result<String> applyAftersale(@RequestBody AftersaleApplyRequestDTO request);

    @Operation(
        summary = "查询售后详情",
        description = "根据售后单号查询售后订单的详细信息，包括订单信息、商品信息、流程记录等。调用时机：用户查看售后详情、客服处理售后工单等场景。成功后返回完整的售后详情信息。",
        operationId = "getAftersaleDetail"
    )
    @GetMapping("/detail/{serviceNumber}")
    Result<AftersaleDetailResponseDTO> getAftersaleDetail(@PathVariable("serviceNumber") String serviceNumber);

    @Operation(
        summary = "查询用户售后列表",
        description = "分页查询指定用户的售后订单列表，支持按状态筛选。调用时机：用户查看我的售后列表、客服查询用户售后历史等场景。成功后返回分页的售后订单列表。",
        operationId = "getUserAftersaleList"
    )
    @GetMapping("/list")
    PageResult<AftersaleDetailResponseDTO> getUserAftersaleList(
        @RequestParam("userId") Long userId,
        @RequestParam(value = "page", defaultValue = "1") Integer page,
        @RequestParam(value = "size", defaultValue = "10") Integer size,
        @RequestParam(value = "asStatus", required = false) Integer asStatus
    );

    @Operation(
        summary = "审核售后申请",
        description = "客服或管理员审核售后申请，可以批准或拒绝。调用时机：客服在管理后台处理售后工单时。成功后更新售后订单状态，失败时返回相应错误信息。",
        operationId = "auditAftersale"
    )
    @PostMapping("/audit")
    Result<Void> auditAftersale(
        @RequestParam("serviceNumber") String serviceNumber,
        @RequestParam("auditResult") Integer auditResult,
        @RequestParam("auditRemark") String auditRemark,
        @RequestParam("operatorId") Long operatorId,
        @RequestParam("operatorName") String operatorName
    );

    @Operation(
        summary = "支付物流费用",
        description = "用户支付退货退款的物流费用。调用时机：用户确认支付物流费用时。成功后更新物流费用支付状态，失败时返回支付失败原因。",
        operationId = "payLogisticsFee"
    )
    @PostMapping("/pay-logistics-fee")
    Result<Void> payLogisticsFee(
        @RequestParam("serviceNumber") String serviceNumber,
        @RequestParam("paymentTransactionId") String paymentTransactionId
    );

    @Operation(
        summary = "同步供货平台状态",
        description = "手动同步供货平台的售后处理状态。调用时机：管理员发现状态不同步时手动触发。成功后更新本地状态，失败时返回同步失败原因。",
        operationId = "syncSupplierStatus"
    )
    @PostMapping("/sync-supplier-status")
    Result<Void> syncSupplierStatus(@RequestParam("serviceNumber") String serviceNumber);

    /**
     * 降级处理工厂类
     */
    @Component
    class AftersaleFallbackFactory implements FallbackFactory<AftersaleFacade> {
        
        private static final Logger logger = LoggerFactory.getLogger(AftersaleFallbackFactory.class);

        @Override
        public AftersaleFacade create(Throwable cause) {
            return new AftersaleFacade() {
                @Override
                public Result<String> applyAftersale(AftersaleApplyRequestDTO request) {
                    logger.error("Feign call applyAftersale failed, request: {}", request, cause);
                    throw new RuntimeException("申请售后失败", cause);
                }

                @Override
                public Result<AftersaleDetailResponseDTO> getAftersaleDetail(String serviceNumber) {
                    logger.error("Feign call getAftersaleDetail failed, serviceNumber: {}", serviceNumber, cause);
                    throw new RuntimeException("查询售后详情失败", cause);
                }

                @Override
                public PageResult<AftersaleDetailResponseDTO> getUserAftersaleList(Long userId, Integer page, Integer size, Integer asStatus) {
                    logger.error("Feign call getUserAftersaleList failed, userId: {}, page: {}, size: {}, asStatus: {}", 
                        userId, page, size, asStatus, cause);
                    throw new RuntimeException("查询用户售后列表失败", cause);
                }

                @Override
                public Result<Void> auditAftersale(String serviceNumber, Integer auditResult, String auditRemark, Long operatorId, String operatorName) {
                    logger.error("Feign call auditAftersale failed, serviceNumber: {}, auditResult: {}, operatorId: {}", 
                        serviceNumber, auditResult, operatorId, cause);
                    throw new RuntimeException("审核售后申请失败", cause);
                }

                @Override
                public Result<Void> payLogisticsFee(String serviceNumber, String paymentTransactionId) {
                    logger.error("Feign call payLogisticsFee failed, serviceNumber: {}, paymentTransactionId: {}", 
                        serviceNumber, paymentTransactionId, cause);
                    throw new RuntimeException("支付物流费用失败", cause);
                }

                @Override
                public Result<Void> syncSupplierStatus(String serviceNumber) {
                    logger.error("Feign call syncSupplierStatus failed, serviceNumber: {}", serviceNumber, cause);
                    throw new RuntimeException("同步供货平台状态失败", cause);
                }
            };
        }
    }
}
