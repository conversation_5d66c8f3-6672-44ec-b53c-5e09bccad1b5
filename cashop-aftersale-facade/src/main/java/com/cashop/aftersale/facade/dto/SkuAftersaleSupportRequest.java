package com.cashop.aftersale.facade.dto;

import lombok.Data;

import java.util.List;

// import javax.validation.constraints.NotEmpty;
// import javax.validation.constraints.NotNull;

/**
 * SKU售后支持批量检查请求DTO
 * Facade层 - 第三方接口请求对象
 */
@Data
public class SkuAftersaleSupportRequest {

    /**
     * 子订单号列表（批量查询）
     */
    // @NotEmpty(message = "子订单号列表不能为空")
    private List<String> subOrderNos;

    /**
     * 用户ID（可选，用于权限验证）
     */
    private Long userId;
}
