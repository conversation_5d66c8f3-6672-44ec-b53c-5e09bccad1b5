package com.cashop.aftersale.facade.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 仓库出库售后检查响应VO
 * Facade层 - 仓库系统接口响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "仓库出库售后检查响应VO")
public class WarehouseOutboundCheckVO {

    @Schema(description = "整体是否允许出库")
    private Boolean canOutbound;

    @Schema(description = "不允许出库的原因，当canOutbound为false时提供")
    private String reason;

    @Schema(description = "检查结果列表")
    private List<OutboundCheckResult> items;

    /**
     * 出库检查结果项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "出库检查结果项")
    public static class OutboundCheckResult {

        @Schema(description = "子订单号")
        private String subOrderNo;

        @Schema(description = "SKU ID")
        private Long skuId;

        @Schema(description = "是否允许出库")
        private Boolean canOutbound;

        @Schema(description = "原始数量")
        private Integer originalQuantity;

        @Schema(description = "在途售后数量")
        private Integer ongoingAftersaleQuantity;

        @Schema(description = "已退款数量")
        private Integer refundedQuantity;

        @Schema(description = "可出库数量")
        private Integer availableQuantity;

        @Schema(description = "在途售后单列表")
        private List<OngoingAftersaleInfo> ongoingAftersales;
    }

    /**
     * 在途售后信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "在途售后信息")
    public static class OngoingAftersaleInfo {

        @Schema(description = "售后单号")
        private String serviceNumber;

        @Schema(description = "售后类型")
        private Integer asType;

        @Schema(description = "售后状态")
        private Integer asStatus;

        @Schema(description = "申请数量")
        private Integer applyQuantity;

        @Schema(description = "申请金额")
        private BigDecimal applyAmount;

        @Schema(description = "不允许出库的原因")
        private String reason;
    }
}
