package com.cashop.aftersale.facade.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 仓库出库售后检查响应VO
 * Facade层 - 仓库系统接口响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseOutboundCheckVO {

    /**
     * 整体是否允许出库
     */
    private Boolean canOutbound;

    /**
     * 检查结果列表
     */
    private List<OutboundCheckResult> items;

    /**
     * 出库检查结果项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OutboundCheckResult {

        /**
         * 子订单号
         */
        private String subOrderNo;

        /**
         * SKU ID
         */
        private Long skuId;

        /**
         * 是否允许出库
         */
        private Boolean canOutbound;

        /**
         * 原始数量
         */
        private Integer originalQuantity;

        /**
         * 在途售后数量
         */
        private Integer ongoingAftersaleQuantity;

        /**
         * 已退款数量
         */
        private Integer refundedQuantity;

        /**
         * 可出库数量
         */
        private Integer availableQuantity;

        /**
         * 在途售后单列表
         */
        private List<OngoingAftersaleInfo> ongoingAftersales;
    }

    /**
     * 在途售后信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OngoingAftersaleInfo {

        /**
         * 售后单号
         */
        private String serviceNumber;

        /**
         * 售后类型
         */
        private Integer asType;

        /**
         * 售后状态
         */
        private Integer asStatus;

        /**
         * 申请数量
         */
        private Integer applyQuantity;

        /**
         * 申请金额
         */
        private BigDecimal applyAmount;

        /**
         * 不允许出库的原因
         */
        private String reason;
    }
}
