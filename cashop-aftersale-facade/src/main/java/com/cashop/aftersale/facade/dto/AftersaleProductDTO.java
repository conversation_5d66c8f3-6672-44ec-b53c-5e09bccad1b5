package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 售后商品DTO
 */
@Data
@Schema(description = "售后商品DTO")
public class AftersaleProductDTO {

    @Schema(
        description = "商品ID",
        example = "1001"
    )
    private Long productId;

    @Schema(
        description = "SKU ID",
        example = "2001"
    )
    private Long skuId;

    @Schema(
        description = "商品名称",
        example = "iPhone 15 Pro"
    )
    private String productName;

    @Schema(
        description = "商品描述",
        example = "苹果最新款手机"
    )
    private String productDescription;

    @Schema(
        description = "商品图片URL",
        example = "https://example.com/image.jpg"
    )
    private String productImageUrl;

    @Schema(
        description = "SKU名称",
        example = "iPhone 15 Pro 256GB 深空黑色"
    )
    private String skuName;

    @Schema(
        description = "规格属性",
        example = "{\"color\":\"深空黑色\",\"storage\":\"256GB\"}"
    )
    private String skuAttributes;

    @Schema(
        description = "购买时单价",
        example = "999.00"
    )
    private BigDecimal unitPrice;

    @Schema(
        description = "币种代码",
        example = "USD"
    )
    private String currencyCode;

    @Schema(
        description = "购买数量",
        example = "1"
    )
    private Integer purchaseQuantity;

    @Schema(
        description = "商品总金额",
        example = "999.00"
    )
    private BigDecimal totalAmount;

    @Schema(
        description = "已申请售后数量",
        example = "1"
    )
    private Integer appliedQuantity;

    @Schema(
        description = "已退款数量",
        example = "0"
    )
    private Integer refundedQuantity;
}
