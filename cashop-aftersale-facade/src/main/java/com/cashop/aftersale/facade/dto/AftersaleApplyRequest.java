package com.cashop.aftersale.facade.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;

/**
 * 售后申请请求DTO
 * 接口层 - 对外接口参数定义
 */
@Data
public class AftersaleApplyRequest {

    /**
     * 主订单号
     */
    @NotBlank(message = "订单号不能为空")
    private String orderNo;

    /**
     * 子订单号
     */
    @NotBlank(message = "子订单号不能为空")
    private String subOrderNo;

    /**
     * Cashop二级单号
     */
    private String cashopOrderNo;

    /**
     * SKU单号
     */
    private String skuOrderNo;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 用户角色:1-购买者,2-店主
     */
    @NotNull(message = "用户角色不能为空")
    private Integer userRole;

    /**
     * 售后类型:1-发货前退款,2-发货后退款,3-发货后退货退款
     */
    @NotNull(message = "售后类型不能为空")
    private Integer asType;

    /**
     * 申请原因
     */
    @NotBlank(message = "申请原因不能为空")
    private String applyReason;

    /**
     * 申请退货总件数
     */
    @NotNull(message = "申请数量不能为空")
    @Positive(message = "申请数量必须大于0")
    private Integer applyQuantity;
}
