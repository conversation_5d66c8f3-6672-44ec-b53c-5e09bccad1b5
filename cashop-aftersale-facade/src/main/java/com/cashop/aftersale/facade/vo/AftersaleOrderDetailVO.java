package com.cashop.aftersale.facade.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后订单详情VO
 * 接口层 - 订单详情展示对象
 */
@Data
public class AftersaleOrderDetailVO {

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 主订单号
     */
    private String orderNo;

    /**
     * 子订单号
     */
    private String skuOrderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 售后类型
     */
    private Integer asType;

    /**
     * 售后类型文本
     */
    private String asTypeText;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 售后状态
     */
    private Integer asStatus;

    /**
     * 售后状态文本
     */
    private String asStatusText;

    /**
     * 申请信息
     */
    private Integer applyQuantity;
    private BigDecimal applyAmount;
    private String currencyCode;
    private BigDecimal returnVolume;
    private BigDecimal returnWeight;

    /**
     * 运费信息
     */
    private BigDecimal shippingFee;
    private BigDecimal serviceFee;
    private BigDecimal totalLogisticsFee;
    private Integer logisticsPaid;
    private LocalDateTime logisticsPayTime;
    private LocalDateTime logisticsExpireTime;

    /**
     * 处理结果
     */
    private Integer approvedQuantity;
    private Integer actualRefundQuantity;
    private BigDecimal actualRefundAmount;

    /**
     * 第三方信息
     */
    private String supplierOrderNo;
    private String supplierAsNo;

    /**
     * 时间信息
     */
    private LocalDateTime createdTime;
    private LocalDateTime lastModifiedTime;

    /**
     * 流程记录
     */
    private List<AsProcessLogVO> processLogs;

    /**
     * 商品信息
     */
    private List<RefundableProductVO> products;
}
