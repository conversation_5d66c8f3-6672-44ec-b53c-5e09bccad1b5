package com.cashop.aftersale.facade.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 仓库创建售后单响应VO
 * Facade层 - 仓库系统接口响应对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarehouseAftersaleCreateVO {

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 状态描述
     */
    private String status;

    /**
     * 申请结果
     */
    private Boolean success;

    /**
     * 错误信息（如果有）
     */
    private String errorMessage;
}
