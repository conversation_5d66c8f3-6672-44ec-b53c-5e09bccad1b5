package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 仓库创建售后单请求DTO
 * Facade层 - 仓库系统接口请求对象
 */
@Data
@Schema(description = "仓库创建售后单请求DTO")
public class WarehouseAftersaleCreateRequest {

    @Schema(
        description = "子订单号",
        example = "SUB202501010001",
        required = true
    )
    private String subOrderNo;

    @Schema(
        description = "SKU ID",
        example = "123456",
        required = true
    )
    private Long skuId;

    @Schema(
        description = "次品数量",
        example = "2",
        required = true
    )
    private Integer defectQuantity;

    @Schema(
        description = "次品原因",
        example = "包装破损"
    )
    private String defectReason;

    @Schema(
        description = "次品描述",
        example = "商品外包装有明显破损，影响销售"
    )
    private String defectDescription;

    @Schema(
        description = "次品图片URLs",
        example = "[\"https://img.cashop.com/defect1.jpg\", \"https://img.cashop.com/defect2.jpg\"]"
    )
    private List<String> defectImages;

    @Schema(
        description = "操作人ID",
        example = "789",
        required = true
    )
    private Long operatorId;

    @Schema(
        description = "操作人姓名",
        example = "张三"
    )
    private String operatorName;

    @Schema(
        description = "仓库代码",
        example = "WH001"
    )
    private String warehouseCode;
}
