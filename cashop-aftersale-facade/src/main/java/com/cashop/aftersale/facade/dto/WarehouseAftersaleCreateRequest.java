package com.cashop.aftersale.facade.dto;

import lombok.Data;

import java.util.List;

/**
 * 仓库创建售后单请求DTO
 * Facade层 - 仓库系统接口请求对象
 */
@Data
public class WarehouseAftersaleCreateRequest {

    /**
     * 子订单号
     */
    private String subOrderNo;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 次品数量
     */
    private Integer defectQuantity;

    /**
     * 次品原因
     */
    private String defectReason;

    /**
     * 次品描述
     */
    private String defectDescription;

    /**
     * 次品图片URLs
     */
    private List<String> defectImages;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人姓名
     */
    private String operatorName;

    /**
     * 仓库代码
     */
    private String warehouseCode;
}
