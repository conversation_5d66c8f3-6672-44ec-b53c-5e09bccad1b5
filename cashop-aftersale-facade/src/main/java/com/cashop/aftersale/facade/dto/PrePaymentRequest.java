package com.cashop.aftersale.facade.dto;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 预支付请求DTO
 * 用于售后退货运费支付
 */
@Data
public class PrePaymentRequest {

    /**
     * 售后单号
     */
    @NotBlank(message = "售后单号不能为空")
    private String serviceNumber;

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /**
     * 支付金额
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    /**
     * 币种代码
     */
    @NotBlank(message = "币种代码不能为空")
    private String currencyCode;

    /**
     * 支付方式 (ALIPAY, WECHAT, CREDIT_CARD等)
     */
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    /**
     * 支付描述
     */
    private String description;

    /**
     * 回调地址
     */
    private String callbackUrl;

    /**
     * 前端返回地址
     */
    private String returnUrl;
}
