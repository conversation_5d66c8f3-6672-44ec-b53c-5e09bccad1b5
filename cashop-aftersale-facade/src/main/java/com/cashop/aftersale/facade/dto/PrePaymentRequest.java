package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 预支付请求DTO
 * 用于售后退货运费支付
 */
@Data
@Schema(description = "预支付请求DTO")
public class PrePaymentRequest {

    @Schema(
        description = "售后单号",
        example = "AS202501010001",
        required = true
    )
    @NotBlank(message = "售后单号不能为空")
    private String serviceNumber;

    @Schema(
        description = "用户ID",
        example = "12345",
        required = true
    )
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(
        description = "支付金额",
        example = "29.99",
        required = true
    )
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    @Schema(
        description = "币种代码",
        example = "USD",
        required = true
    )
    @NotBlank(message = "币种代码不能为空")
    private String currencyCode;

    @Schema(
        description = "支付方式",
        example = "ALIPAY",
        allowableValues = {"ALIPAY", "WECHAT", "CREDIT_CARD", "PAYPAL"},
        required = true
    )
    @NotBlank(message = "支付方式不能为空")
    private String paymentMethod;

    @Schema(
        description = "支付描述",
        example = "售后退货运费支付"
    )
    private String description;

    @Schema(
        description = "支付回调地址",
        example = "https://api.cashop.com/api/aftersale/payment/callback"
    )
    private String callbackUrl;

    @Schema(
        description = "前端返回地址",
        example = "https://app.cashop.com/aftersale/payment/result"
    )
    private String returnUrl;
}
