package com.cashop.aftersale.facade.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后详情响应DTO
 */
@Data
@Schema(description = "售后详情响应DTO")
public class AftersaleDetailResponseDTO {

    @Schema(
        description = "售后单号",
        example = "AS202501010001"
    )
    private String serviceNumber;

    @Schema(
        description = "订单号",
        example = "ORD202501010001"
    )
    private String orderNo;

    @Schema(
        description = "子订单号",
        example = "SUB202501010001"
    )
    private String subOrderNo;

    @Schema(
        description = "用户ID",
        example = "12345"
    )
    private Long userId;

    @Schema(
        description = "售后类型：1-退款，2-退货退款",
        example = "1"
    )
    private Integer asType;

    @Schema(
        description = "售后状态：1-申请中，2-待支付运费，3-已提交供货平台，4-供货平台处理中，5-已完成，6-已拒绝，7-超时撤销",
        example = "1"
    )
    private Integer asStatus;

    @Schema(
        description = "申请原因",
        example = "商品质量问题"
    )
    private String applyReason;

    @Schema(
        description = "申请退货件数",
        example = "3"
    )
    private Integer applyQuantity;

    @Schema(
        description = "申请总金额",
        example = "100.00"
    )
    private BigDecimal applyAmount;

    @Schema(
        description = "币种代码",
        example = "USD"
    )
    private String currencyCode;

    @Schema(
        description = "国际运费",
        example = "20.00"
    )
    private BigDecimal shippingFee;

    @Schema(
        description = "服务费",
        example = "5.00"
    )
    private BigDecimal serviceFee;

    @Schema(
        description = "物流总费用",
        example = "25.00"
    )
    private BigDecimal totalLogisticsFee;

    @Schema(
        description = "物流费用支付状态：0-未支付，1-已支付，2-已退还",
        example = "0"
    )
    private Integer logisticsPaid;

    @Schema(
        description = "物流费用支付截止时间",
        example = "2025-01-01T12:00:00"
    )
    private LocalDateTime logisticsExpireTime;

    @Schema(
        description = "供货平台订单号",
        example = "SUP202501010001"
    )
    private String supplierOrderNo;

    @Schema(
        description = "供货平台售后单号",
        example = "SUP_AS202501010001"
    )
    private String supplierAsNo;

    @Schema(
        description = "供货平台批准退货件数",
        example = "3"
    )
    private Integer approvedQuantity;

    @Schema(
        description = "实际退款件数",
        example = "3"
    )
    private Integer actualRefundQuantity;

    @Schema(
        description = "实际退款金额",
        example = "100.00"
    )
    private BigDecimal actualRefundAmount;

    @Schema(
        description = "创建时间",
        example = "2025-01-01T10:00:00"
    )
    private LocalDateTime createdTime;

    @Schema(
        description = "商品列表"
    )
    private List<AftersaleProductDTO> products;

    @Schema(
        description = "流程记录列表"
    )
    private List<AftersaleProcessLogDTO> processLogs;
}
