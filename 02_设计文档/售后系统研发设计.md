## 售后系统研发设计文档

## 1\. 系统概述

### 1.1 项目基本信息

*   **项目名称**: cashop-aftersale (售后管理系统)
*   **项目版本**: 1.0.0-SNAPSHOT
*   **技术栈**: Spring Boot 3.2.6 + OpenJDK 17 + Maven多模块
*   **架构模式**: 微服务架构
*   **包名规范**: com.cashop.cashop.aftersale

### 1.2 系统目标

构建一个完整的电商售后管理系统，支持退款、退货退款等多种售后场景，提供标准化的售后流程管理和用户服务体验。

### 1.3 核心业务场景

基于需求文档中的流程图，系统主要支持以下业务场景：

*   发货前申请退款流程
*   发货后申请退款流程
*   发货后申请退货退款流程
*   售后端上交互处理
*   **仓库次品自动申请售后流程**
*   **仓库物流信息管理流程**
*   **仓库出库售后状态检查流程**

## 2\. 技术架构设计

### 2.1 整体架构

```plaintext
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   移动端应用     │    │   第三方系统     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    API网关层     │                                 │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│              cashop-aftersale-web (Web层)                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Controller    │  │   配置管理       │  │   健康检查       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│           cashop-aftersale-facade (接口层)                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   对外接口       │  │   参数验证       │  │   异常处理       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│          cashop-aftersale-service (业务层)                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   业务逻辑       │  │   流程控制       │  │   规则引擎       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│            cashop-aftersale-dao (数据层)                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   MyBatis配置   │  │   数据访问       │  │   SQL映射        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────┼─────────────────────────────────┘
                                 │
┌─────────────────────────────────┼─────────────────────────────────┐
│                    数据库层      │                                 │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   MySQL主库     │  │   MySQL从库     │  │   Redis缓存     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────┼─────────────────────────────────┘
```

### 2.2 技术选型

*   **框架**: Spring Boot 3.2.6 + Spring Cloud 2023.0.4
*   **数据库**: MySQL 8 + HikariCP连接池
*   **ORM**: 原生MyBatis 3.5.14
*   **服务发现**: Eureka
*   **配置中心**: Apollo 2.1.0
*   **缓存**: Redis (Jedis 4.4.3)
*   **日志**: Log4j2 2.21.1 + SLF4J 2.0.13
*   **API文档**: OpenAPI 3.0 (SpringDoc 2.3.0)
*   **服务调用**: Spring Cloud OpenFeign 4.1.4

## 3\. 模块设计

### 3.1 模块职责划分

#### cashop-aftersale-common (公共模块)

*   **职责**: 工具类、共享组件、常量定义
*   **内容**:
    *   基础工具类
    *   自定义异常
    *   请求码常量
    *   共享配置

#### cashop-aftersale-dao (数据访问层)

*   **职责**: 数据库操作、MyBatis配置
*   **内容**:
    *   Mapper接口定义
    *   SQL映射文件
    *   数据库实体类
    *   数据访问对象

#### cashop-aftersale-facade (接口层)

*   **职责**: 对外接口实现、参数验证
*   **内容**:
    *   对外暴露接口实现
    *   参数校验
    *   异常处理
    *   接口适配

#### cashop-aftersale-service (业务层)

*   **职责**: 业务逻辑实现、流程控制
*   **内容**:
    *   核心业务逻辑
    *   售后流程控制
    *   规则引擎
    *   第三方服务调用

#### cashop-aftersale-web (Web层)

*   **职责**: HTTP接口、配置管理
*   **内容**:
    *   REST Controller
    *   应用配置
    *   健康检查
    *   启动类

### 3.2 模块依赖关系

```plaintext
cashop-aftersale-web
    ↓
cashop-aftersale-facade
    ↓
cashop-aftersale-service
    ↓
cashop-aftersale-dao
    ↓
cashop-aftersale-common
```

## 4\. 核心功能设计

### 4.1 售后申请功能

基于需求文档中的流程图，系统支持以下售后类型：

#### 4.1.1 发货前退款

*   **触发条件**: 订单已支付但未发货
*   **处理流程**:
    1.  用户申请退款(指定退货件数)
    2.  通知第三方供货平台取消对应数量订单
    3.  等待供货平台确认取消结果(确认件数)
    4.  按确认件数计算退款金额并退款给用户

#### 4.1.2 发货后退款

*   **触发条件**: 订单已发货但用户不满意
*   **处理流程**:
    1.  用户申请退款(指定退货件数)
    2.  提交给第三方供货平台审核
    3.  供货平台返回批准退货件数
    4.  按批准件数计算退款金额并退款

#### 4.1.3 发货后退货退款

*   **触发条件**: 订单已发货且需要退回商品
*   **处理流程**:
    1.  用户申请退货退款(指定退货件数)
    2.  系统计算国际运费并要求用户支付
    3.  用户支付运费后，系统提交给第三方供货平台
    4.  供货平台审核并提供国内转运仓地址
    5.  用户寄回商品到国内转运仓
    6.  转运仓验收并转交给供货平台
    7.  供货平台最终验收并确认实际退货件数
    8.  按确认件数计算退款金额并退款

#### 4.1.4 仓库次品申请售后

*   **触发条件**: 商家货物寄到转运仓，仓库发现次品
*   **申请人**: 仓库操作人员
*   **处理流程**:
    1.  仓库操作人员发现次品，记录次品信息
    2.  系统自动创建售后申请(申请人为仓库操作员)
    3.  直接提交给第三方供货平台审核
    4.  供货平台审核通过后，系统自动处理退款
    5.  发送MQ消息通知仓库售后创建和处理结果

#### 4.1.5 仓库物流信息管理

*   **触发条件**: 第三方供货平台一审同意退货
*   **处理流程**:
    1.  供货平台一审同意退货后，系统推送信息到仓库
    2.  仓库按要求将对应货物寄给商家
    3.  仓库在7天内填写物流信息到售后系统
    4.  系统将物流信息推送给第三方供货平台
    5.  如仓库超时(7天)未回传物流信息，售后单自动关闭
    6.  同步第三方供货平台售后关单状态

#### 4.1.6 仓库出库售后检查

*   **触发条件**: 仓库执行出库到国外的流程
*   **检查逻辑**:
    1.  查询是否有在途售后，如有则不允许出库
    2.  查询已售后退款件数，出库时排除这些件数
    3.  返回可出库件数和售后状态信息

### 4.2 核心流程时序图

基于需求文档中的流程图，以下是三个核心售后流程的时序图设计：

#### 4.2.1 发货前申请退款流程时序图

**参与者：**

*   用户
*   Web层
*   Service层
*   DAO层
*   订单系统
*   支付系统
*   通知系统

**流程步骤：**

1.  **用户** → **Web层**: 申请退款请求
2.  **Web层** → **Web层**: 参数验证
3.  **Web层** → **Service层**: 调用申请退款服务
4.  **Service层** → **订单系统**: 查询订单状态
5.  **订单系统** → **Service层**: 返回订单信息

**分支处理：**

**情况A：订单未发货**  
6\. **Service层** → **Service层**: 校验退款条件  
7\. **Service层** → **DAO层**: 创建售后订单  
8\. **DAO层** → **Service层**: 返回售后单号  
9\. **Service层** → **Service层**: 更新状态为"审核通过"  
10\. **Service层** → **支付系统**: 调用退款接口  
11\. **支付系统** → **Service层**: 返回退款结果  
12\. **Service层** → **DAO层**: 更新退款状态  
13\. **Service层** → **通知系统**: 发送退款通知  
14\. **Service层** → **Web层**: 返回成功结果

**情况B：订单已发货**  
15\. **Service层** → **Web层**: 返回失败(订单已发货)

**最终步骤：**  
16\. **Web层** → **用户**: 返回申请结果

**时序图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant O as 订单系统
    participant SP as 第三方供货平台
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退款请求(指定件数)
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请退款服务
    S->>O: 4. 查询订单状态
    O-->>S: 5. 返回订单信息

    alt 订单未发货
        S->>S: 6. 校验退款条件
        S->>S: 7. 计算申请金额和服务费
        S->>D: 8. 创建售后订单(包含服务费信息)
        D-->>S: 9. 返回售后单号
        S->>SP: 10. 通知供货平台取消订单(指定件数)
        S-->>W: 11. 返回申请成功
        W-->>U: 12. 返回申请结果

        Note over SP: 第三方供货平台处理
        SP->>SP: 13. 供货平台处理取消
        SP->>S: 14. 消息通知取消结果(确认件数)
        S->>D: 15. 更新取消状态

        alt 供货平台确认取消
            S->>S: 16. 计算商品退款金额(确认件数×单价)
            S->>S: 17. 计算服务费退款金额(确认件数×单件服务费)
            S->>D: 18. 创建商品退款明细
            S->>D: 19. 创建服务费退款明细
            S->>P: 20. 调用商品退款接口
            P-->>S: 21. 返回商品退款结果
            S->>P: 22. 调用服务费退款接口
            P-->>S: 23. 返回服务费退款结果
            S->>D: 24. 更新退款明细状态为成功
            S->>D: 25. 更新售后订单为退款完成
            S->>N: 26. 发送完成通知给用户
        else 供货平台拒绝取消(已发货)
            S->>D: 27. 更新状态为取消失败
            S->>N: 28. 通知用户订单已发货无法取消
        end
    else 订单已发货
        S-->>W: 29. 返回失败(订单已发货)
        W-->>U: 30. 返回失败结果
    end
```

\> 说明：该时序图展示了发货前退款的完整流程，包含订单状态校验和自动化处理逻辑。

#### 4.2.2 发货后申请退款流程时序图

**参与者：**

*   用户
*   Web层
*   Service层
*   DAO层
*   客服系统
*   商家系统
*   支付系统
*   通知系统

**主流程步骤：**

1.  **用户** → **Web层**: 申请退款请求
2.  **Web层** → **Web层**: 参数验证
3.  **Web层** → **Service层**: 调用申请退款服务
4.  **Service层** → **Service层**: 校验申请条件
5.  **Service层** → **DAO层**: 创建售后订单
6.  **DAO层** → **Service层**: 返回售后单号
7.  **Service层** → **客服系统**: 推送客服审核任务
8.  **Service层** → **Web层**: 返回申请成功
9.  **Web层** → **用户**: 返回申请结果

**客服审核阶段：**  
10\. **客服系统** → **客服系统**: 客服审核  
11\. **客服系统** → **Service层**: 提交审核结果  
12\. **Service层** → **DAO层**: 更新审核状态

**分支处理：**

**情况A：客服审核通过**  
13\. **Service层** → **商家系统**: 推送商家确认任务  
14\. **商家系统** → **商家系统**: 商家确认  
15\. **商家系统** → **Service层**: 提交确认结果

**子分支A1：商家确认通过**  
16\. **Service层** → **DAO层**: 更新状态为"退款中"  
17\. **Service层** → **支付系统**: 调用退款接口  
18\. **支付系统** → **Service层**: 返回退款结果  
19\. **Service层** → **DAO层**: 更新为"退款完成"  
20\. **Service层** → **通知系统**: 发送完成通知

**子分支A2：商家拒绝**  
21\. **Service层** → **DAO层**: 更新状态为"申请被拒"  
22\. **Service层** → **通知系统**: 发送拒绝通知

**情况B：客服审核拒绝**  
23\. **Service层** → **DAO层**: 更新状态为"申请被拒"  
24\. **Service层** → **通知系统**: 发送拒绝通知

**时序图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant SP as 第三方供货平台
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退款请求(指定件数)
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请退款服务
    S->>S: 4. 校验申请条件
    S->>S: 5. 计算申请金额和服务费
    S->>D: 6. 创建售后订单(包含服务费信息)
    D-->>S: 7. 返回售后单号
    S->>SP: 8. 提交售后申请到供货平台
    S-->>W: 9. 返回申请成功
    W-->>U: 10. 返回申请结果

    Note over SP: 第三方供货平台处理
    SP->>SP: 11. 供货平台审核
    SP->>S: 12. 消息通知审核结果(批准件数)
    S->>D: 13. 更新审核状态和批准件数

    alt 供货平台批准退货
        S->>S: 14. 计算商品退款金额(批准件数×单价)
        S->>S: 15. 计算服务费退款金额(批准件数×单件服务费)
        S->>D: 16. 创建商品退款明细
        S->>D: 17. 创建服务费退款明细
        S->>P: 18. 调用商品退款接口
        P-->>S: 19. 返回商品退款结果
        S->>P: 20. 调用服务费退款接口
        P-->>S: 21. 返回服务费退款结果
        S->>D: 22. 更新退款明细状态为成功
        S->>D: 23. 更新售后订单为退款完成
        S->>N: 24. 发送完成通知给用户
    else 供货平台拒绝
        S->>D: 25. 更新状态为申请被拒
        S->>N: 26. 发送拒绝通知给用户
    end
```

\> 说明：该时序图展示了发货后退款的完整流程，包含客服审核和商家确认的多级审核机制。

#### 4.2.3 发货后申请退货退款流程时序图

**参与者：**

*   用户
*   Web层
*   Service层
*   DAO层
*   客服系统
*   物流系统
*   商家系统
*   支付系统
*   通知系统

**主流程步骤：**

1.  **用户** → **Web层**: 申请退货退款
2.  **Web层** → **Web层**: 参数验证
3.  **Web层** → **Service层**: 调用申请服务
4.  **Service层** → **Service层**: 校验申请条件
5.  **Service层** → **DAO层**: 创建售后订单
6.  **DAO层** → **Service层**: 返回售后单号
7.  **Service层** → **客服系统**: 推送客服审核任务
8.  **Service层** → **Web层**: 返回申请成功
9.  **Web层** → **用户**: 返回申请结果

**客服审核阶段：**  
10\. **客服系统** → **客服系统**: 客服审核  
11\. **客服系统** → **Service层**: 提交审核结果

**分支处理：**

**情况A：客服审核通过**  
12\. **Service层** → **DAO层**: 更新状态为"审核通过"  
13\. **Service层** → **Service层**: 生成退货地址  
14\. **Service层** → **通知系统**: 发送退货地址通知  
15\. **Service层** → **DAO层**: 更新状态为"待寄回"

**用户寄回商品阶段：**  
16\. **用户** → **物流系统**: 寄回商品  
17\. **物流系统** → **Service层**: 物流状态回调  
18\. **Service层** → **DAO层**: 更新状态为"商品寄回中"

**商品验收阶段：**  
19\. **物流系统** → **商家系统**: 商品到达商家  
20\. **商家系统** → **商家系统**: 商品验收  
21\. **商家系统** → **Service层**: 提交验收结果

**子分支A1：验收通过**  
22\. **Service层** → **DAO层**: 更新状态为"退款中"  
23\. **Service层** → **支付系统**: 调用退款接口  
24\. **支付系统** → **Service层**: 返回退款结果  
25\. **Service层** → **DAO层**: 更新为"退款完成"  
26\. **Service层** → **通知系统**: 发送完成通知

**子分支A2：验收不通过**  
27\. **Service层** → **DAO层**: 更新状态为"申请被拒"  
28\. **Service层** → **通知系统**: 发送拒绝通知

**情况B：客服审核拒绝**  
29\. **Service层** → **DAO层**: 更新状态为"申请被拒"  
30\. **Service层** → **通知系统**: 发送拒绝通知

**时序图：**

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant SP as 第三方供货平台
    participant WH as 转运仓
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退货退款(指定件数)
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请服务
    S->>S: 4. 校验申请条件
    S->>S: 5. 计算申请金额和服务费
    S->>D: 6. 创建售后订单(包含服务费信息)
    D-->>S: 7. 返回售后单号
    S->>S: 8. 计算国际运费
    S->>S: 9. 设置运费支付截止时间(30分钟)
    S->>D: 10. 更新状态为待支付运费
    S-->>W: 11. 返回运费信息和截止时间
    W-->>U: 12. 展示运费和倒计时，要求用户支付

    Note over U: 用户在时限内支付运费
    U->>W: 13. 确认支付运费
    W->>P: 14. 调用支付接口(运费)
    P-->>W: 15. 返回支付结果
    W->>S: 16. 通知运费支付成功
    S->>D: 17. 更新运费支付状态

    alt 用户在时限内支付运费
        S->>SP: 17. 提交退货申请到供货平台
        Note over SP: 第三方供货平台审核
        SP->>SP: 18. 供货平台审核
        SP->>S: 19. 消息通知审核结果+转运仓地址

        alt 供货平台审核通过
            S->>D: 23. 更新状态为审核通过
            S->>N: 24. 发送转运仓地址给用户
            S->>D: 25. 更新状态为待寄回

            Note over U: 用户寄回商品到转运仓
            U->>WH: 26. 寄回商品到国内转运仓
            WH->>WH: 27. 转运仓接收验收
            WH->>SP: 28. 转运仓转交给供货平台
            SP->>SP: 29. 供货平台最终验收
            SP->>S: 30. 消息通知验收结果(实际收到件数)

            alt 验收通过
                S->>S: 31. 计算商品退款金额(实际件数×单价)
                S->>S: 32. 计算服务费退款金额(实际件数×单件服务费)
                S->>D: 33. 创建商品退款明细
                S->>D: 34. 创建服务费退款明细
                S->>D: 35. 更新状态为退款中
                S->>P: 36. 调用商品退款接口
                P-->>S: 37. 返回商品退款结果
                S->>P: 38. 调用服务费退款接口
                P-->>S: 39. 返回服务费退款结果
                S->>D: 40. 更新退款明细状态为成功
                S->>D: 41. 更新售后订单为退款完成
                S->>N: 42. 发送完成通知给用户
            else 验收不通过
                S->>D: 43. 更新状态为申请被拒
                S->>N: 44. 发送拒绝通知给用户
            end
        else 供货平台审核拒绝
            S->>D: 45. 更新状态为申请被拒
            S->>D: 46. 创建运费退款明细
            S->>P: 47. 退还运费给用户
            S->>D: 48. 更新运费退款明细状态
            S->>N: 49. 发送拒绝通知给用户
        end
    else 用户超时未支付运费
        Note over S: 系统定时任务检测超时
        S->>S: 50. 检测到运费支付超时
        S->>D: 51. 更新状态为超时撤销
        S->>N: 52. 发送超时撤销通知给用户
    end
```

\> 说明：该时序图展示了最复杂的退货退款流程，包含物流跟踪、商品验收等完整环节。

#### 4.2.4 售后端上交互流程时序图

**参与者：**

*   客服人员
*   Web管理端
*   Service层
*   DAO层
*   用户端
*   通知系统
*   支付系统

**工单查询阶段：**

1.  **客服人员** → **Web管理端**: 登录管理系统
2.  **Web管理端** → **Service层**: 查询待处理工单
3.  **Service层** → **DAO层**: 查询售后订单列表
4.  **DAO层** → **Service层**: 返回工单数据
5.  **Service层** → **Web管理端**: 返回工单列表
6.  **Web管理端** → **客服人员**: 展示待处理工单

**工单详情查看：**  
7\. **客服人员** → **Web管理端**: 选择处理工单  
8\. **Web管理端** → **Service层**: 查询工单详情  
9\. **Service层** → **DAO层**: 查询售后详情  
10\. **DAO层** → **Service层**: 返回详细信息  
11\. **Service层** → **Web管理端**: 返回工单详情  
12\. **Web管理端** → **客服人员**: 展示工单详情

**分支处理：**

**情况A：审核通过**  
13\. **客服人员** → **Web管理端**: 提交审核通过  
14\. **Web管理端** → **Service层**: 更新审核状态  
15\. **Service层** → **DAO层**: 更新数据库状态  
16\. **Service层** → **通知系统**: 发送通知给用户  
17\. **通知系统** → **用户端**: 推送审核通过消息

**子分支A1：需要退货**  
18\. **Service层** → **Service层**: 生成退货地址  
19\. **Service层** → **通知系统**: 发送退货地址  
20\. **通知系统** → **用户端**: 推送退货地址

**子分支A2：直接退款**  
21\. **Service层** → **支付系统**: 调用退款接口  
22\. **支付系统** → **Service层**: 返回退款结果  
23\. **Service层** → **DAO层**: 更新退款状态  
24\. **Service层** → **通知系统**: 发送退款完成通知  
25\. **通知系统** → **用户端**: 推送退款完成消息

**情况B：审核拒绝**  
26\. **客服人员** → **Web管理端**: 提交审核拒绝  
27\. **Web管理端** → **Service层**: 更新拒绝状态  
28\. **Service层** → **DAO层**: 更新数据库状态  
29\. **Service层** → **通知系统**: 发送拒绝通知  
30\. **通知系统** → **用户端**: 推送审核拒绝消息

**流程结束：**  
31\. **Service层** → **Web管理端**: 返回处理结果  
32\. **Web管理端** → **客服人员**: 显示处理完成

**时序图：**

```mermaid
sequenceDiagram
    participant Admin as 管理员
    participant W as Web管理端
    participant S as Service层
    participant D as DAO层
    participant SP as 第三方供货平台
    participant P as 支付系统
    participant N as 通知系统

    Note over Admin,N: Cashop售后管理流程

    Admin->>W: 1. 登录管理系统
    W->>S: 2. 查询售后订单列表
    S->>D: 3. 查询售后订单数据
    D-->>S: 4. 返回订单数据
    S-->>W: 5. 返回订单列表
    W-->>Admin: 6. 展示售后订单

    Admin->>W: 7. 选择查看订单详情
    W->>S: 8. 查询订单详情
    S->>D: 9. 查询详细信息
    D-->>S: 10. 返回详细信息
    S-->>W: 11. 返回订单详情
    W-->>Admin: 12. 展示订单详情

    Note over Admin: 管理员操作

    alt 手动同步供货平台状态
        Admin->>W: 13. 请求同步状态
        W->>S: 14. 调用同步服务
        S->>SP: 15. 查询供货平台状态
        SP-->>S: 16. 返回最新状态
        S->>D: 17. 更新本地状态
        S-->>W: 18. 返回同步结果
        W-->>Admin: 19. 显示同步完成
    else 手动处理退款
        Admin->>W: 20. 确认处理退款
        W->>S: 21. 执行退款操作
        S->>P: 22. 调用退款接口
        P-->>S: 23. 返回退款结果
        S->>D: 24. 更新退款状态
        S->>N: 25. 发送通知给用户
        S-->>W: 26. 返回处理结果
        W-->>Admin: 27. 显示处理完成
    end
```

\> 说明：该时序图展示了客服人员在管理端处理售后工单的完整操作流程。

#### 4.2.5 仓库次品申请售后流程时序图

**参与者：**

*   仓库操作员
*   仓库系统
*   售后系统Service层
*   DAO层
*   第三方供货平台
*   支付系统
*   MQ消息队列
*   仓库通知系统

**主流程步骤：**

1.  **仓库操作员** → **仓库系统**: 发现次品，记录次品信息
2.  **仓库系统** → **售后系统**: 调用创建售后接口
3.  **售后系统** → **售后系统**: 验证仓库权限和商品信息
4.  **售后系统** → **DAO层**: 创建售后订单(申请人为仓库操作员)
5.  **售后系统** → **第三方供货平台**: 提交次品售后申请
6.  **售后系统** → **MQ消息队列**: 发送售后创建通知
7.  **MQ消息队列** → **仓库通知系统**: 通知仓库售后已创建

**供货平台处理阶段：**
8.  **第三方供货平台** → **第三方供货平台**: 审核次品申请
9.  **第三方供货平台** → **售后系统**: 返回审核结果
10. **售后系统** → **DAO层**: 更新审核状态

**分支处理：**

**情况A：审核通过**
11. **售后系统** → **支付系统**: 调用退款接口
12. **支付系统** → **售后系统**: 返回退款结果
13. **售后系统** → **DAO层**: 更新为退款完成
14. **售后系统** → **MQ消息队列**: 发送退款完成通知
15. **MQ消息队列** → **仓库通知系统**: 通知仓库退款完成

**情况B：审核拒绝**
16. **售后系统** → **DAO层**: 更新状态为申请被拒
17. **售后系统** → **MQ消息队列**: 发送拒绝通知
18. **MQ消息队列** → **仓库通知系统**: 通知仓库申请被拒

**时序图：**

```mermaid
sequenceDiagram
    participant WO as 仓库操作员
    participant WS as 仓库系统
    participant AS as 售后系统
    participant D as DAO层
    participant SP as 第三方供货平台
    participant P as 支付系统
    participant MQ as MQ消息队列
    participant WN as 仓库通知系统

    WO->>WS: 1. 发现次品，记录次品信息
    WS->>AS: 2. 调用创建售后接口(次品信息)
    AS->>AS: 3. 验证仓库权限和商品信息
    AS->>D: 4. 创建售后订单(申请人=仓库操作员)
    D-->>AS: 5. 返回售后单号
    AS->>SP: 6. 提交次品售后申请
    AS->>MQ: 7. 发送售后创建通知
    MQ->>WN: 8. 通知仓库售后已创建
    AS-->>WS: 9. 返回申请成功
    WS-->>WO: 10. 显示申请结果

    Note over SP: 第三方供货平台处理
    SP->>SP: 11. 审核次品申请
    SP->>AS: 12. 消息通知审核结果
    AS->>D: 13. 更新审核状态

    alt 审核通过
        AS->>P: 14. 调用退款接口
        P-->>AS: 15. 返回退款结果
        AS->>D: 16. 更新为退款完成
        AS->>MQ: 17. 发送退款完成通知
        MQ->>WN: 18. 通知仓库退款完成
    else 审核拒绝
        AS->>D: 19. 更新状态为申请被拒
        AS->>MQ: 20. 发送拒绝通知
        MQ->>WN: 21. 通知仓库申请被拒
    end
```

\> 说明：该时序图展示了仓库发现次品时自动申请售后的完整流程，包含MQ通知机制。

#### 4.2.6 仓库物流信息管理流程时序图

**参与者：**

*   第三方供货平台
*   售后系统Service层
*   DAO层
*   MQ消息队列
*   仓库通知系统
*   仓库操作员
*   仓库系统
*   定时任务

**主流程步骤：**

**一审同意阶段：**
1.  **第三方供货平台** → **售后系统**: 一审同意退货通知
2.  **售后系统** → **DAO层**: 更新状态为"待仓库发货"
3.  **售后系统** → **MQ消息队列**: 发送仓库发货通知
4.  **MQ消息队列** → **仓库通知系统**: 通知仓库需要发货
5.  **仓库通知系统** → **仓库操作员**: 推送发货任务

**仓库发货阶段：**
6.  **仓库操作员** → **仓库系统**: 按要求寄货给商家
7.  **仓库操作员** → **仓库系统**: 填写物流信息
8.  **仓库系统** → **售后系统**: 提交物流信息
9.  **售后系统** → **DAO层**: 保存物流信息
10. **售后系统** → **第三方供货平台**: 推送物流信息
11. **售后系统** → **DAO层**: 更新状态为"已发货"

**超时检查阶段：**
12. **定时任务** → **定时任务**: 每天检查超时未发货订单
13. **定时任务** → **DAO层**: 查询超时订单(7天未填写物流)
14. **定时任务** → **售后系统**: 处理超时订单

**分支处理：**

**情况A：7天内填写物流信息**
15. **售后系统** → **MQ消息队列**: 发送物流信息已提交通知
16. **MQ消息队列** → **仓库通知系统**: 通知仓库物流信息已提交

**情况B：超时未填写物流信息**
17. **售后系统** → **DAO层**: 更新状态为"超时关闭"
18. **售后系统** → **第三方供货平台**: 同步售后关单状态
19. **售后系统** → **MQ消息队列**: 发送售后关闭通知
20. **MQ消息队列** → **仓库通知系统**: 通知仓库售后已关闭

**时序图：**

```mermaid
sequenceDiagram
    participant SP as 第三方供货平台
    participant AS as 售后系统
    participant D as DAO层
    participant MQ as MQ消息队列
    participant WN as 仓库通知系统
    participant WO as 仓库操作员
    participant WS as 仓库系统
    participant T as 定时任务

    Note over SP,T: 一审同意退货流程
    SP->>AS: 1. 一审同意退货通知
    AS->>D: 2. 更新状态为"待仓库发货"
    AS->>AS: 3. 设置发货截止时间(7天后)
    AS->>MQ: 4. 发送仓库发货通知
    MQ->>WN: 5. 通知仓库需要发货
    WN->>WO: 6. 推送发货任务

    Note over WO,WS: 仓库发货流程
    WO->>WS: 7. 按要求寄货给商家
    WO->>WS: 8. 填写物流信息
    WS->>AS: 9. 提交物流信息
    AS->>D: 10. 保存物流信息
    AS->>SP: 11. 推送物流信息给供货平台
    AS->>D: 12. 更新状态为"已发货"
    AS->>MQ: 13. 发送物流信息已提交通知
    MQ->>WN: 14. 通知仓库物流信息已提交

    Note over T: 定时任务检查超时
    T->>T: 15. 每天检查超时未发货订单
    T->>D: 16. 查询超时订单(7天未填写物流)
    
    alt 发现超时订单
        T->>AS: 17. 处理超时订单
        AS->>D: 18. 更新状态为"超时关闭"
        AS->>SP: 19. 同步售后关单状态
        AS->>MQ: 20. 发送售后关闭通知
        MQ->>WN: 21. 通知仓库售后已关闭
    end
```

\> 说明：该时序图展示了一审同意后仓库物流信息管理的完整流程，包含超时自动关闭机制。

### 4.3 状态管理

基于流程图中的状态设计，系统维护以下状态：

#### 4.3.1 发货前退款状态

*   申请中
*   审核通过
*   退款中
*   退款完成
*   申请被拒

#### 4.3.2 发货后退款状态

*   申请中
*   客服审核中
*   商家确认中
*   退款中
*   退款完成
*   申请被拒

#### 4.3.3 发货后退货退款状态

*   申请中
*   审核通过
*   待寄回
*   商品寄回中
*   商品验收中
*   退款中
*   退款完成
*   申请被拒

#### 4.3.4 仓库次品售后状态

*   申请中
*   已提交供货平台
*   供货平台处理中
*   退款中
*   退款完成
*   申请被拒

#### 4.3.5 仓库物流管理状态

*   待仓库发货
*   已发货
*   超时关闭
*   物流信息已提交

#### 4.3.6 售后申请人类型

*   用户申请 (user_apply)
*   仓库申请 (warehouse_apply)

## 5\. 数据库设计

### 5.1 核心表结构

#### 5.1.1 售后订单表 (as\_order)

```sql
CREATE TABLE as_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    service_number VARCHAR(32) NOT NULL COMMENT 'Cashop售后单号',
    order_no VARCHAR(32) NOT NULL COMMENT 'Cashop订单号',
    sub_order_no VARCHAR(32) COMMENT 'Cashop子订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    as_type TINYINT NOT NULL COMMENT '售后类型:1-退款,2-退货退款',
    as_status TINYINT NOT NULL COMMENT '售后状态:1-申请中,2-待支付运费,3-已提交供货平台,4-供货平台处理中,5-已完成,6-已拒绝,7-超时撤销',
    apply_reason VARCHAR(500) COMMENT '申请原因',

    -- 申请信息(简化)
    apply_quantity INT NOT NULL COMMENT '申请退货总件数',
    apply_amount DECIMAL(10,2) NOT NULL COMMENT '申请总金额',
    currency_code VARCHAR(8) NOT NULL COMMENT '币种代码:USD,EUR,USDT等',

    -- 退货物流信息
    return_volume DECIMAL(10,3) DEFAULT 0 COMMENT '退货总体积(立方米)',
    return_weight DECIMAL(10,3) DEFAULT 0 COMMENT '退货总重量(公斤)',

    -- 运费相关(仅退货退款需要)
    shipping_fee DECIMAL(10,2) DEFAULT 0 COMMENT '国际运费',
    service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '服务费',
    total_logistics_fee DECIMAL(10,2) DEFAULT 0 COMMENT '物流总费用(运费+服务费)',
    logistics_paid TINYINT DEFAULT 0 COMMENT '物流费用支付状态:0-未支付,1-已支付,2-已退还',
    logistics_pay_time DATETIME COMMENT '物流费用支付时间',
    logistics_expire_time DATETIME COMMENT '物流费用支付截止时间',

    -- 订单维度服务费(按件收取)
    order_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '订单服务费总额(按件数计算)',
    unit_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '单件服务费',

    -- 申请人信息
    applicant_type TINYINT DEFAULT 1 COMMENT '申请人类型:1-用户申请,2-仓库申请',
    applicant_id BIGINT COMMENT '申请人ID(用户ID或仓库操作员ID)',
    applicant_name VARCHAR(100) COMMENT '申请人姓名',

    -- 仓库物流信息
    warehouse_shipping_deadline DATETIME COMMENT '仓库发货截止时间',
    warehouse_shipping_time DATETIME COMMENT '仓库实际发货时间',
    warehouse_logistics_company VARCHAR(100) COMMENT '仓库使用的物流公司',
    warehouse_tracking_number VARCHAR(100) COMMENT '仓库物流单号',
    warehouse_logistics_info TEXT COMMENT '仓库物流详细信息(JSON格式)',

    -- 第三方供货平台信息
    supplier_order_no VARCHAR(64) COMMENT '供货平台订单号',
    supplier_as_no VARCHAR(64) COMMENT '供货平台售后单号',

    -- 供货平台处理结果(简化记录)
    approved_quantity INT DEFAULT 0 COMMENT '供货平台批准退货总件数',
    actual_refund_quantity INT DEFAULT 0 COMMENT '实际退款总件数',
    actual_refund_amount DECIMAL(10,2) DEFAULT 0 COMMENT '实际退款总金额',



    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_service_number (service_number),
    INDEX idx_order_no (order_no),
    INDEX idx_supplier_order_no (supplier_order_no),
    INDEX idx_supplier_as_no (supplier_as_no),
    INDEX idx_user_id (user_id),
    INDEX idx_currency_code (currency_code),
    INDEX idx_as_status (as_status),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='售后订单表';
```

#### 5.1.2 子订单商品表 (as\_product)

```sql
CREATE TABLE as_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,

    -- 订单关联信息
    order_no VARCHAR(32) NOT NULL COMMENT '主订单号',
    sub_order_no VARCHAR(32) NOT NULL COMMENT '子订单号',

    -- 商品基础信息
    product_id BIGINT NOT NULL COMMENT '商品ID',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    product_name VARCHAR(200) NOT NULL COMMENT '商品名称',
    product_description VARCHAR(500) COMMENT '商品描述',
    product_image_url VARCHAR(500) COMMENT '商品图片URL',

    -- 规格属性信息
    sku_name VARCHAR(200) COMMENT 'SKU名称',
    sku_attributes TEXT COMMENT '规格属性(颜色、尺码等) {"color":"红色","size":"L"}',

    -- 价格信息(子订单购买时的价格)
    unit_price DECIMAL(10,2) NOT NULL COMMENT '购买时单价',
    currency_code VARCHAR(8) NOT NULL COMMENT '币种代码:USD,EUR,USDT等',

    -- 数量信息(子订单的购买数量)
    purchase_quantity INT NOT NULL COMMENT '购买数量',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '商品总金额(购买数量*单价)',

    -- 物流信息(单件商品)
    unit_volume DECIMAL(10,3) DEFAULT 0 COMMENT '单件商品体积(立方米)',
    unit_weight DECIMAL(10,3) DEFAULT 0 COMMENT '单件商品重量(公斤)',

    -- 商品状态
    product_status TINYINT DEFAULT 1 COMMENT '商品状态:1-正常,2-已下架,3-已删除',

    -- 售后相关统计(便于计算可申请数量)
    applied_quantity INT DEFAULT 0 COMMENT '已申请售后数量',
    refunded_quantity INT DEFAULT 0 COMMENT '已退款数量',

    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    UNIQUE KEY uk_sub_order_sku (sub_order_no, sku_id),
    INDEX idx_order_no (order_no),
    INDEX idx_sub_order_no (sub_order_no),
    INDEX idx_product_id (product_id),
    INDEX idx_sku_id (sku_id),
    INDEX idx_currency_code (currency_code),
    INDEX idx_product_status (product_status),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='子订单商品表';
```

#### 5.1.3 售后流程记录表 (as\_process\_log)

```sql
CREATE TABLE as_process_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    from_status TINYINT COMMENT '原状态',
    to_status TINYINT NOT NULL COMMENT '目标状态',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(50) COMMENT '操作人姓名',
    remark VARCHAR(500) COMMENT '备注',

    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_as_order_id (as_order_id),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='售后流程记录表';
```

#### 5.1.4 售后退款明细表 (as_refund_detail)

```sql
CREATE TABLE as_refund_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    service_number VARCHAR(32) NOT NULL COMMENT '售后单号',

    -- 退款类型和金额
    refund_type TINYINT NOT NULL COMMENT '退款类型:1-商品退款,2-服务费退款,3-运费退款',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退款金额',
    currency_code VARCHAR(8) NOT NULL COMMENT '币种代码',

    -- 退款明细说明
    refund_reason VARCHAR(200) COMMENT '退款原因',
    refund_description VARCHAR(500) COMMENT '退款明细描述',

    -- 商品相关信息(仅商品退款时填写)
    product_id BIGINT COMMENT '商品ID(as_product表的id)',
    sku_id BIGINT COMMENT 'SKU ID',
    product_name VARCHAR(200) COMMENT '商品名称',
    refund_quantity INT DEFAULT 0 COMMENT '退款件数',
    unit_price DECIMAL(10,2) DEFAULT 0 COMMENT '单价',

    -- 服务费相关信息(仅服务费退款时填写)
    service_fee_quantity INT DEFAULT 0 COMMENT '服务费对应件数',
    unit_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '单件服务费',

    -- 退款状态和时间
    refund_status TINYINT DEFAULT 1 COMMENT '退款状态:1-待退款,2-退款中,3-退款成功,4-退款失败',
    refund_transaction_id VARCHAR(64) COMMENT '退款交易ID',
    refund_time DATETIME COMMENT '退款完成时间',

    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_as_order_id (as_order_id),
    INDEX idx_service_number (service_number),
    INDEX idx_refund_type (refund_type),
    INDEX idx_refund_status (refund_status),
    INDEX idx_product_id (product_id),
    INDEX idx_refund_transaction_id (refund_transaction_id),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='售后退款明细表';
```

#### 5.1.5 第三方供货平台消息记录表 (as_supplier_message)
```sql
CREATE TABLE as_supplier_message (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    message_type TINYINT NOT NULL COMMENT '消息类型:1-提交申请,2-审核结果,3-退款结果,4-状态变更',
    message_direction TINYINT NOT NULL COMMENT '消息方向:1-发送给供货平台,2-接收自供货平台',
    supplier_order_no VARCHAR(64) COMMENT '供货平台订单号',
    supplier_as_no VARCHAR(64) COMMENT '供货平台售后单号',
    message_content TEXT COMMENT '消息内容(JSON格式)',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    status TINYINT DEFAULT 0 COMMENT '处理状态:0-待处理,1-处理成功,2-处理失败',
    error_msg VARCHAR(500) COMMENT '错误信息',
    retry_times INT DEFAULT 0 COMMENT '重试次数',

    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_as_order_id (as_order_id),
    INDEX idx_supplier_order_no (supplier_order_no),
    INDEX idx_supplier_as_no (supplier_as_no),
    INDEX idx_message_type (message_type),
    INDEX idx_status (status),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='第三方供货平台消息记录表';
```

#### 5.1.6 MQ消息通知记录表 (as_mq_message)
```sql
CREATE TABLE as_mq_message (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    service_number VARCHAR(32) NOT NULL COMMENT '售后单号',
    
    -- 消息类型和内容
    message_type TINYINT NOT NULL COMMENT '消息类型:1-售后创建,2-售后退款,3-售后拒绝,4-售后关闭',
    message_topic VARCHAR(100) NOT NULL COMMENT 'MQ主题',
    message_tag VARCHAR(50) COMMENT 'MQ标签',
    message_content TEXT NOT NULL COMMENT '消息内容(JSON格式)',
    
    -- 发送状态
    send_status TINYINT DEFAULT 0 COMMENT '发送状态:0-待发送,1-发送成功,2-发送失败',
    send_time DATETIME COMMENT '发送时间',
    send_result VARCHAR(500) COMMENT '发送结果',
    error_msg VARCHAR(500) COMMENT '错误信息',
    retry_times INT DEFAULT 0 COMMENT '重试次数',
    
    -- 目标系统
    target_system VARCHAR(50) NOT NULL COMMENT '目标系统:warehouse,order,payment等',
    
    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_as_order_id (as_order_id),
    INDEX idx_service_number (service_number),
    INDEX idx_message_type (message_type),
    INDEX idx_send_status (send_status),
    INDEX idx_target_system (target_system),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='MQ消息通知记录表';
```





## 6. 第三方消息记录模块

### 6.1 第三方消息记录表 (third_party_message)
```sql
CREATE TABLE third_party_message (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(64) NOT NULL COMMENT '消息ID(第三方提供的唯一标识)',
    business_type TINYINT NOT NULL COMMENT '业务类型:1-售后申请,2-审核结果,3-退款结果,4-状态变更',
    business_id VARCHAR(64) NOT NULL COMMENT '关联的业务单号(如售后单号)',

    -- 第三方信息
    third_party_name VARCHAR(100) NOT NULL COMMENT '第三方名称(如供货平台名称)',
    third_party_id VARCHAR(64) COMMENT '第三方标识',

    -- 消息内容(完整记录第三方给的数据)
    raw_message LONGTEXT NOT NULL COMMENT '原始消息内容(完整JSON)',
    message_summary VARCHAR(500) COMMENT '消息摘要(便于查看)',

    -- 消费状态
    consume_status TINYINT DEFAULT 0 COMMENT '消费状态:0-未消费,1-消费成功,2-消费失败',
    consume_time DATETIME COMMENT '消费时间',
    consume_result TEXT COMMENT '消费结果描述',
    error_msg VARCHAR(500) COMMENT '错误信息',
    retry_times INT DEFAULT 0 COMMENT '重试次数',

    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    UNIQUE KEY uk_message_id (message_id),
    INDEX idx_business_type_id (business_type, business_id),
    INDEX idx_third_party (third_party_name, third_party_id),
    INDEX idx_consume_status (consume_status),
    INDEX idx_consume_time (consume_time),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='第三方消息记录表';
```

### 6.2 ER图关系

```plaintext
-- 售后模块
as_order (通过JSON字段关联) -----> as_product (子订单商品信息)
as_order (1) -----> (N) as_process_log (流程记录)
as_order (1) -----> (N) as_supplier_message (供货平台消息)
as_order (1) -----> (N) as_refund_detail (退款明细)

-- 第三方消息模块
as_order (1) -----> (N) third_party_message (通过business_id关联)

-- 数据关系说明
-- as_product: 纯粹的子订单商品信息，通过sub_order_no查询
-- as_order: 售后申请主单，通过JSON字段记录申请的商品信息，包含订单服务费信息
-- as_refund_detail: 退款明细记录，包含商品退款、服务费退款、运费退款等明细
-- 关联方式: as_order.apply_products 中的 productId 对应 as_product.id
-- 退款明细关联: as_refund_detail.as_order_id 对应 as_order.id
```

## 6\. API接口设计

### 6.1 售后申请接口

#### 6.1.1 申请售后

```http
POST /api/aftersale/apply
Content-Type: application/json

{
    "orderNo": "string",
    "subOrderNo": "string", 
    "asType": 1,
    "applyReason": "string",
    "refundAmount": 100.00,
    "applyQuantity": 3  // 申请总件数(从子订单商品中选择的总数量)
}
```

#### 6.1.2 查询售后详情

```http
GET /api/aftersale/detail/{serviceNumber}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "serviceNumber": "string",
        "orderNo": "string",
        "asType": 1,
        "asStatus": 1,
        "refundAmount": 100.00,
        "products": [...],
        "processLogs": [...]
    }
}
```

#### 6.1.3 查询用户售后列表

```http
GET /api/aftersale/list?userId={userId}&page=1&size=10

Response:
{
    "code": 200,
    "message": "success", 
    "data": {
        "total": 100,
        "list": [...]
    }
}
```

### 6.2 管理端接口

#### 6.2.1 审核售后申请

```http
POST /api/aftersale/audit
Content-Type: application/json

{
    "serviceNumber": "string",
    "auditResult": 1,
    "auditRemark": "string"
}
```

### 6.3 仓库系统接口

#### 6.3.1 仓库创建售后单

```http
POST /api/warehouse/aftersale/create
Content-Type: application/json

{
    "subOrderNo": "string",
    "skuId": 123456,
    "defectQuantity": 2,
    "defectReason": "商品破损",
    "defectDescription": "包装破损，商品受损",
    "defectImages": ["url1", "url2"],
    "operatorId": 789,
    "operatorName": "张三",
    "warehouseCode": "WH001"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "serviceNumber": "AS202312010001",
        "status": "申请成功"
    }
}
```

#### 6.3.2 填写物流信息

```http
POST /api/warehouse/aftersale/logistics
Content-Type: application/json

{
    "serviceNumber": "AS202312010001",
    "logisticsCompany": "顺丰快递",
    "trackingNumber": "SF1234567890",
    "shippingTime": "2023-12-01 10:30:00",
    "logisticsInfo": {
        "senderName": "仓库发货员",
        "senderPhone": "13800138000",
        "receiverName": "商家收货人",
        "receiverPhone": "13900139000",
        "receiverAddress": "广东省深圳市南山区xxx"
    },
    "operatorId": 789,
    "operatorName": "李四"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "status": "物流信息已提交"
    }
}
```

#### 6.3.3 仓库出库售后检查

```http
POST /api/warehouse/outbound/check
Content-Type: application/json

{
    "outboundOrderNo": "OUT202312010001",
    "items": [
        {
            "subOrderNo": "SO202312010001",
            "skuId": 123456,
            "requestQuantity": 10
        },
        {
            "subOrderNo": "SO202312010002", 
            "skuId": 123457,
            "requestQuantity": 5
        }
    ],
    "operatorId": 789,
    "operatorName": "王五"
}

Response:
{
    "code": 200,
    "message": "success",
    "data": {
        "canOutbound": true,
        "items": [
            {
                "subOrderNo": "SO202312010001",
                "skuId": 123456,
                "canOutbound": true,
                "originalQuantity": 10,
                "ongoingAftersaleQuantity": 0,
                "refundedQuantity": 2,
                "availableQuantity": 8,
                "ongoingAftersales": []
            },
            {
                "subOrderNo": "SO202312010002",
                "skuId": 123457,
                "canOutbound": false,
                "originalQuantity": 5,
                "ongoingAftersaleQuantity": 3,
                "refundedQuantity": 0,
                "availableQuantity": 2,
                "ongoingAftersales": [
                    {
                        "serviceNumber": "AS202312010002",
                        "asType": 2,
                        "asStatus": 1,
                        "applyQuantity": 3,
                        "reason": "有在途售后申请"
                    }
                ]
            }
        ]
    }
}
```

## 7\. 安全设计

### 7.1 认证授权

*   使用JWT Token进行用户认证
*   基于RBAC模型进行权限控制
*   API接口级别的权限验证

### 7.2 数据安全

*   敏感数据加密存储
*   SQL注入防护
*   XSS攻击防护
*   接口防刷限流

### 7.3 审计日志

*   关键操作审计记录
*   数据变更追踪
*   异常访问监控

## 8\. 部署架构

### 8.1 服务注册发现

*   使用Eureka作为服务注册中心
*   服务实例自动注册和发现
*   健康检查和故障转移

### 8.2 配置管理

*   Apollo配置中心统一管理配置
*   多环境配置隔离
*   配置热更新支持

### 8.3 监控告警

*   应用性能监控
*   业务指标监控
*   异常告警机制

### 8.4 部署环境

*   开发环境 (dev)
*   测试环境 (test)
*   预发环境 (pre)
*   生产环境 (prod)

## 9\. 开发规范

### 9.1 代码规范

*   遵循阿里巴巴Java开发手册
*   统一的包名规范: com.cashop.cashop.aftersale
*   规范的注释和文档

### 9.2 接口规范

*   RESTful API设计原则
*   统一的响应格式
*   完整的API文档

### 9.3 数据库规范

*   统一的命名规范
*   合理的索引设计
*   规范的字段类型和长度

## 10\. 质量保证

### 10.1 测试策略

*   单元测试覆盖率 > 80%
*   集成测试覆盖核心流程
*   性能测试验证系统容量

### 10.2 代码质量

*   SonarQube静态代码分析
*   代码评审机制
*   持续集成和部署

### 10.3 文档管理

*   完整的技术文档
*   API接口文档
*   运维手册

**文档版本**: v1.0  
**创建时间**: 2025-07-28  
**维护人员**: 开发团队