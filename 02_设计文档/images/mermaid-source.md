# 时序图Mermaid源码

本文件包含所有时序图的Mermaid源码，可用于生成PNG图片。

## 使用方法

1. 复制下面的Mermaid代码
2. 访问 https://mermaid.live/ 
3. 粘贴代码到编辑器
4. 点击"Download PNG"下载图片
5. 将图片保存到对应的文件名

---

## 1. 发货前申请退款流程时序图

**文件名**: `发货前申请退款流程时序图.png`

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant O as 订单系统
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退款请求
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请退款服务
    S->>O: 4. 查询订单状态
    O-->>S: 5. 返回订单信息
    
    alt 订单未发货
        S->>S: 6. 校验退款条件
        S->>D: 7. 创建售后订单
        D-->>S: 8. 返回售后单号
        S->>S: 9. 更新状态为"审核通过"
        S->>P: 10. 调用退款接口
        P-->>S: 11. 返回退款结果
        S->>D: 12. 更新退款状态
        S->>N: 13. 发送退款通知
        S-->>W: 14. 返回成功结果
    else 订单已发货
        S-->>W: 15. 返回失败(订单已发货)
    end
    
    W-->>U: 16. 返回申请结果
```

---

## 2. 发货后申请退款流程时序图

**文件名**: `发货后申请退款流程时序图.png`

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant CS as 客服系统
    participant M as 商家系统
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退款请求
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请退款服务
    S->>S: 4. 校验申请条件
    S->>D: 5. 创建售后订单
    D-->>S: 6. 返回售后单号
    S->>CS: 7. 推送客服审核任务
    S-->>W: 8. 返回申请成功
    W-->>U: 9. 返回申请结果

    Note over CS: 客服审核阶段
    CS->>CS: 10. 客服审核
    CS->>S: 11. 提交审核结果
    S->>D: 12. 更新审核状态
    
    alt 客服审核通过
        S->>M: 13. 推送商家确认任务
        M->>M: 14. 商家确认
        M->>S: 15. 提交确认结果
        
        alt 商家确认通过
            S->>D: 16. 更新状态为"退款中"
            S->>P: 17. 调用退款接口
            P-->>S: 18. 返回退款结果
            S->>D: 19. 更新为"退款完成"
            S->>N: 20. 发送完成通知
        else 商家拒绝
            S->>D: 21. 更新状态为"申请被拒"
            S->>N: 22. 发送拒绝通知
        end
    else 客服审核拒绝
        S->>D: 23. 更新状态为"申请被拒"
        S->>N: 24. 发送拒绝通知
    end
```

---

## 3. 发货后申请退货退款流程时序图

**文件名**: `发货后申请退货退款流程时序图.png`

```mermaid
sequenceDiagram
    participant U as 用户
    participant W as Web层
    participant S as Service层
    participant D as DAO层
    participant CS as 客服系统
    participant L as 物流系统
    participant M as 商家系统
    participant P as 支付系统
    participant N as 通知系统

    U->>W: 1. 申请退货退款
    W->>W: 2. 参数验证
    W->>S: 3. 调用申请服务
    S->>S: 4. 校验申请条件
    S->>D: 5. 创建售后订单
    D-->>S: 6. 返回售后单号
    S->>CS: 7. 推送客服审核任务
    S-->>W: 8. 返回申请成功
    W-->>U: 9. 返回申请结果

    Note over CS: 客服审核阶段
    CS->>CS: 10. 客服审核
    CS->>S: 11. 提交审核结果

    alt 客服审核通过
        S->>D: 12. 更新状态为"审核通过"
        S->>S: 13. 生成退货地址
        S->>N: 14. 发送退货地址通知
        S->>D: 15. 更新状态为"待寄回"

        Note over U: 用户寄回商品
        U->>L: 16. 寄回商品
        L->>S: 17. 物流状态回调
        S->>D: 18. 更新状态为"商品寄回中"

        L->>M: 19. 商品到达商家
        M->>M: 20. 商品验收
        M->>S: 21. 提交验收结果

        alt 验收通过
            S->>D: 22. 更新状态为"退款中"
            S->>P: 23. 调用退款接口
            P-->>S: 24. 返回退款结果
            S->>D: 25. 更新为"退款完成"
            S->>N: 26. 发送完成通知
        else 验收不通过
            S->>D: 27. 更新状态为"申请被拒"
            S->>N: 28. 发送拒绝通知
        end
    else 客服审核拒绝
        S->>D: 29. 更新状态为"申请被拒"
        S->>N: 30. 发送拒绝通知
    end
```

---

## 4. 售后端上交互流程时序图

**文件名**: `售后端上交互流程时序图.png`

```mermaid
sequenceDiagram
    participant CS as 客服人员
    participant W as Web管理端
    participant S as Service层
    participant D as DAO层
    participant U as 用户端
    participant N as 通知系统
    participant P as 支付系统

    Note over CS,P: 售后工单处理流程

    CS->>W: 1. 登录管理系统
    W->>S: 2. 查询待处理工单
    S->>D: 3. 查询售后订单列表
    D-->>S: 4. 返回工单数据
    S-->>W: 5. 返回工单列表
    W-->>CS: 6. 展示待处理工单

    CS->>W: 7. 选择处理工单
    W->>S: 8. 查询工单详情
    S->>D: 9. 查询售后详情
    D-->>S: 10. 返回详细信息
    S-->>W: 11. 返回工单详情
    W-->>CS: 12. 展示工单详情

    Note over CS: 客服处理决策

    alt 审核通过
        CS->>W: 13. 提交审核通过
        W->>S: 14. 更新审核状态
        S->>D: 15. 更新数据库状态
        S->>N: 16. 发送通知给用户
        N->>U: 17. 推送审核通过消息

        alt 需要退货
            S->>S: 18. 生成退货地址
            S->>N: 19. 发送退货地址
            N->>U: 20. 推送退货地址
        else 直接退款
            S->>P: 21. 调用退款接口
            P-->>S: 22. 返回退款结果
            S->>D: 23. 更新退款状态
            S->>N: 24. 发送退款完成通知
            N->>U: 25. 推送退款完成消息
        end

    else 审核拒绝
        CS->>W: 26. 提交审核拒绝
        W->>S: 27. 更新拒绝状态
        S->>D: 28. 更新数据库状态
        S->>N: 29. 发送拒绝通知
        N->>U: 30. 推送审核拒绝消息
    end

    Note over CS: 工单处理完成
    S-->>W: 31. 返回处理结果
    W-->>CS: 32. 显示处理完成
```

---

## 生成步骤总结

1. 访问 https://mermaid.live/
2. 复制上述任一Mermaid代码
3. 粘贴到在线编辑器
4. 点击"Download PNG"
5. 保存为对应的文件名
6. 将PNG文件放入 `02_设计文档/images/` 目录

完成后，文档中的图片引用就能正常显示了。
