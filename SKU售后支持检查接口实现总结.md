# SKU售后支持检查接口实现总结

## 1. 功能概述

根据需求，实现了一个供订单系统调用的接口，用于检查指定SKU是否支持申请售后。前端可以根据接口返回的状态来决定显示"申请售后"按钮还是"售后进度"按钮。

## 2. 核心业务逻辑

接口会根据以下规则判断SKU的售后支持状态：

1. **已申请过售后** - 返回有售后状态，显示售后进度按钮
2. **有在途售后** - 当有在途售后时，告知不能继续申请
3. **已全部退完** - 当SKU单没有在途售后，但已经全部金额退完时，不能支持申请售后
4. **支持申请** - 其他情况下支持申请售后

## 3. 技术实现

### 3.1 模块结构

```
cashop-aftersale/
├── cashop-aftersale-facade/          # 对外接口定义
│   ├── dto/                          # 请求响应对象
│   │   └── SkuAftersaleSupportRequest.java
│   ├── vo/                           # 视图对象
│   │   └── SkuAftersaleSupportVO.java
│   ├── client/                       # Feign客户端
│   │   ├── AftersaleThirdPartyClient.java
│   │   └── AftersaleThirdPartyClientFallbackFactory.java
│   ├── constants/                    # 常量定义
│   │   └── AftersaleApiConstants.java
│   └── README.md                     # 使用文档
├── cashop-aftersale-service/         # 业务逻辑层
│   └── impl/AftersaleOrderServiceImpl.java  # 新增方法实现
├── cashop-aftersale-dao/             # 数据访问层
│   ├── mapper/AsProductMapper.java   # 新增查询方法
│   └── resources/mapper/AsProductMapperExtension.xml
└── cashop-aftersale-web/             # Web接口层
    └── controller/ThirdPartyController.java  # 新增接口
```

### 3.2 核心文件说明

#### 3.2.1 请求响应对象

**SkuAftersaleSupportRequest.java**
- 子订单号 (subOrderNo) - 必填
- SKU ID (skuId) - 必填  
- 用户ID (userId) - 可选，用于权限验证

**SkuAftersaleSupportVO.java**
- 支持状态标识和状态码
- 商品基础信息
- 售后统计信息（已申请、已退款、可申请数量等）
- 在途售后单列表

#### 3.2.2 业务逻辑实现

**AftersaleOrderServiceImpl.checkSkuAftersaleSupport()**

核心处理流程：
1. 查询商品信息
2. 查询该子订单的所有售后记录
3. 筛选出该SKU相关的售后记录
4. 计算退款统计信息
5. 查询在途售后订单
6. 判断售后支持状态
7. 构建响应数据

#### 3.2.3 状态码定义

| 状态码 | 常量 | 含义 | 前端处理 |
|--------|------|------|----------|
| 1 | SUPPORT_APPLY | 支持申请售后 | 显示"申请售后"按钮 |
| 2 | HAS_AFTERSALE_RECORD | 已有售后记录 | 显示"售后进度"按钮 |
| 3 | HAS_ONGOING_AFTERSALE | 有在途售后申请 | 显示"售后进度"按钮，提示不能继续申请 |
| 4 | FULLY_REFUNDED | 已全部退完 | 不显示售后相关按钮 |
| 5 | SYSTEM_ERROR | 系统异常 | 显示错误信息或隐藏按钮 |

### 3.3 接口信息

**接口路径**: `POST /api/third-party/aftersale/support/check`

**Feign客户端**: `AftersaleThirdPartyClient.checkSkuAftersaleSupport()`

**请求示例**:
```json
{
  "subOrderNo": "SO202312010001",
  "skuId": 123456,
  "userId": 789
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "supportAftersale": true,
    "supportStatus": 1,
    "statusMessage": "支持申请售后",
    "productInfo": {
      "skuId": 123456,
      "productName": "商品名称",
      "purchaseQuantity": 2,
      "unitPrice": 99.99,
      "currencyCode": "USD"
    },
    "aftersaleStatistics": {
      "appliedQuantity": 0,
      "refundedQuantity": 0,
      "availableQuantity": 2,
      "totalRefundedAmount": 0.00,
      "availableRefundAmount": 199.98
    },
    "ongoingAftersales": []
  }
}
```

## 4. 关键特性

### 4.1 降级处理
- 实现了Feign客户端的降级处理
- 当售后系统不可用时，返回系统异常状态
- 确保调用方不会因为售后系统故障而受到影响

### 4.2 状态管理
- 使用常量类统一管理状态码和状态描述
- 便于维护和扩展

### 4.3 数据完整性
- 返回详细的商品信息和售后统计
- 包含在途售后单列表，便于前端展示详细信息

### 4.4 错误处理
- 完善的异常处理机制
- 区分不同类型的错误（商品不存在、系统异常等）

## 5. 数据库变更

### 5.1 新增查询方法

在 `AsProductMapper` 中新增：
```java
AsProduct selectBySubOrderNoAndSkuId(@Param("subOrderNo") String subOrderNo, @Param("skuId") Long skuId);
```

对应的SQL实现在 `AsProductMapperExtension.xml` 中。

## 6. 使用示例

### 6.1 Spring Boot项目集成

```java
@Service
public class OrderAftersaleService {
    
    @Autowired
    private AftersaleThirdPartyClient aftersaleClient;
    
    public boolean canApplyAftersale(String subOrderNo, Long skuId, Long userId) {
        SkuAftersaleSupportRequest request = new SkuAftersaleSupportRequest();
        request.setSubOrderNo(subOrderNo);
        request.setSkuId(skuId);
        request.setUserId(userId);
        
        Result<SkuAftersaleSupportVO> result = aftersaleClient.checkSkuAftersaleSupport(request);
        
        if (result.isSuccess()) {
            return result.getData().getSupportAftersale();
        }
        
        return false;
    }
}
```

### 6.2 前端使用

```javascript
async function checkAftersaleSupport(subOrderNo, skuId) {
  const response = await fetch('/api/third-party/aftersale/support/check', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ subOrderNo, skuId, userId: getCurrentUserId() })
  });
  
  const result = await response.json();
  
  if (result.code === 200) {
    const data = result.data;
    switch (data.supportStatus) {
      case 1: showAftersaleButton(true); break;
      case 2:
      case 3: showAftersaleProgressButton(data.ongoingAftersales); break;
      case 4: hideAftersaleButton(); break;
      case 5: showErrorMessage(data.statusMessage); break;
    }
  }
}
```

## 7. 注意事项

### 7.1 性能考虑
- 接口会查询数据库，建议在必要时才调用
- 可考虑添加缓存优化性能

### 7.2 权限验证
- userId 参数可用于权限验证
- 确保用户只能查询自己的订单

### 7.3 监控告警
- 建议对接口调用进行监控
- 及时发现和处理异常情况

### 7.4 编译问题
- 当前实现中可能存在Lombok相关的编译问题
- 需要确保项目中正确配置了Lombok依赖和IDE插件

## 8. 后续优化建议

1. **缓存优化**: 对频繁查询的商品信息添加缓存
2. **性能优化**: 优化SQL查询，减少数据库访问次数
3. **监控完善**: 添加接口调用监控和告警
4. **测试完善**: 补充单元测试和集成测试
5. **文档完善**: 补充API文档和使用示例

## 9. 版本信息

- **版本**: v1.0.0
- **创建时间**: 2024年12月
- **主要功能**: SKU售后支持状态检查
- **适用场景**: 订单系统前端售后按钮显示逻辑
