# 基础依赖配置说明

## 概述

本项目已更新为使用公司的基础依赖包，包括：
- `base-parent`：基础父 POM，包含核心依赖管理
- `common-model`：基础 DTO 包，包含通用响应类和异常类

## 1. 父 POM 配置

### 1.1 更新后的父 POM

```xml
<parent>
    <groupId>com.cashop.base</groupId>
    <artifactId>base-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <relativePath/>
</parent>
```

### 1.2 base-parent 包含的核心依赖

`base-parent` 提供以下核心依赖的版本管理：

- **Spring Boot**：2.7.18
- **Spring Cloud**：2021.0.8
- **HikariCP**：5.0.1
- **MyBatis**：3.5.14
- **Eureka**：服务注册发现
- **Apollo**：2.1.0 配置中心
- **Log4j2**：2.21.1 日志框架
- **Jedis**：4.4.3 Redis 客户端
- **OpenAPI**：1.7.0 API 文档
- **OpenFeign**：4.1.4 服务调用
- **Commons Lang3**：3.14.0 工具库

## 2. common-model 包

### 2.1 依赖配置

```xml
<dependency>
    <groupId>com.cashop</groupId>
    <artifactId>common-model</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 2.2 包含的基础类

#### 2.2.1 异常处理

```java
// 通用异常编码接口
com.cashop.common.base.exception.IErrorCode

// 业务异常类
com.cashop.common.base.exception.BusinessException
```

#### 2.2.2 请求 DTO

```java
// 分页请求
com.cashop.common.base.request.PageRequest
```

#### 2.2.3 响应 DTO

```java
// 通用响应结果
com.cashop.common.base.response.Result<T>

// 分页响应结果
com.cashop.common.base.response.PageResult<T>
```

## 3. Facade 模块更新

### 3.1 依赖优化

Facade 模块现在使用最小依赖原则：

```xml
<dependencies>
    <!-- 基础 DTO 包 -->
    <dependency>
        <groupId>com.cashop</groupId>
        <artifactId>common-model</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <!-- Spring Cloud OpenFeign -->
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-openfeign</artifactId>
    </dependency>

    <!-- Swagger Annotations -->
    <dependency>
        <groupId>io.swagger.core.v3</groupId>
        <artifactId>swagger-annotations</artifactId>
    </dependency>
</dependencies>
```

### 3.2 接口更新

所有 Facade 接口现在使用 `common-model` 包中的基础类：

```java
import com.cashop.common.base.response.Result;
import com.cashop.common.base.response.PageResult;

@FeignClient(name = "cashop-aftersale-service", path = "/api/aftersale")
public interface AftersaleFacade {
    
    @PostMapping("/apply")
    Result<String> applyAftersale(@RequestBody AftersaleApplyRequestDTO request);
    
    @GetMapping("/list")
    PageResult<AftersaleDetailResponseDTO> getUserAftersaleList(
        @RequestParam("userId") Long userId,
        @RequestParam(value = "page", defaultValue = "1") Integer page,
        @RequestParam(value = "size", defaultValue = "10") Integer size,
        @RequestParam(value = "asStatus", required = false) Integer asStatus
    );
}
```

## 4. 使用示例

### 4.1 异常处理

```java
import com.cashop.common.base.exception.BusinessException;
import com.cashop.common.base.response.Result;

public void example() {
    Result<String> response = aftersaleFacade.someMethod();
    
    if (response.isSuccess()) {
        // 处理成功结果
        String data = response.getData();
    } else {
        // 使用 BusinessException 处理错误
        throw new BusinessException(response.getCode(), response.getMessage());
    }
}
```

### 4.2 分页查询

```java
import com.cashop.common.base.response.PageResult;

public List<SomeDTO> getPageData(Long userId, Integer page, Integer size) {
    PageResult<SomeDTO> response = facade.getPageData(userId, page, size);
    
    if (response.isSuccess()) {
        return response.getData();
    } else {
        throw new BusinessException(response.getCode(), response.getMessage());
    }
}
```

### 4.3 错误码定义

```java
import com.cashop.common.base.exception.IErrorCode;

public enum ErrorCodes implements IErrorCode {
    SUCCESS("00000", "操作成功"),
    ARGUMENT_ERROR("A0001", "参数错误"),
    SYSTEM_ERROR("B0001", "系统异常");
    
    private final String code;
    private final String message;
    
    // 构造函数和 getter 方法...
}
```

## 5. 配置要求

### 5.1 Maven 仓库配置

确保 Maven 配置了正确的仓库地址：

```xml
<repositories>
    <repository>
        <id>nexus-releases</id>
        <url>http://maven.aikucun.com:8082/nexus/content/groups/public</url>
    </repository>
</repositories>
```

### 5.2 版本兼容性

- **Java 版本**：JDK 17
- **Spring Boot 版本**：2.7.18
- **Spring Cloud 版本**：2021.0.8

## 6. 注意事项

### 6.1 依赖管理

- 所有核心依赖版本由 `base-parent` 统一管理
- 子模块不需要指定版本号，由父 POM 控制
- 新增依赖时优先使用父 POM 中已管理的版本

### 6.2 异常处理

- 统一使用 `BusinessException` 进行业务异常处理
- 错误码遵循公司规范，使用 `IErrorCode` 接口
- 避免使用 `RuntimeException` 等通用异常

### 6.3 响应格式

- 所有接口响应使用 `Result<T>` 或 `PageResult<T>` 包装
- 确保响应格式的一致性
- 错误信息包含错误码和错误描述

## 7. 迁移指南

### 7.1 从旧版本迁移

1. **更新父 POM**：将 `spring-boot-starter-parent` 替换为 `base-parent`
2. **移除重复依赖**：删除已在父 POM 中管理的依赖版本
3. **更新导入**：使用 `common-model` 包中的基础类
4. **异常处理**：将 `RuntimeException` 替换为 `BusinessException`

### 7.2 验证步骤

1. **编译检查**：确保所有依赖都能正确解析
2. **功能测试**：验证接口调用和异常处理
3. **文档更新**：更新 README 和使用示例

## 总结

通过使用公司的基础依赖包，项目获得了：

1. **统一的依赖管理**：版本由 `base-parent` 统一控制
2. **标准化的接口**：使用 `common-model` 包中的基础类
3. **规范的异常处理**：统一的异常处理机制
4. **更好的维护性**：减少重复代码，提高代码质量

这些改进确保了项目的标准化和可维护性。
