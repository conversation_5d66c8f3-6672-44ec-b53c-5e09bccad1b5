# Cashop售后系统前端对接文档

## 1. 接口概览

### 1.1 基础信息
- **服务名称**: cashop-aftersale
- **基础URL**: `http://localhost:8080/cashop-aftersale/api`
- **接口协议**: HTTP/HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {},
  "timestamp": 1703750400000
}
```

**响应码说明:**
- `200`: 成功
- `400`: 参数错误
- `500`: 系统错误

## 2. 用户端接口

### 2.1 获取可退款商品列表

**接口地址:** `GET /aftersale/products`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| subOrderNo | String | 是 | 子订单号 | SUB202412280001 |
| currencyCode | String | 否 | 币种代码，默认USD | USD |

**请求示例:**
```bash
GET /cashop-aftersale/api/aftersale/products?subOrderNo=SUB202412280001&currencyCode=USD
```

**响应示例:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "productId": 12345,
      "skuId": 67890,
      "productName": "iPhone 15 Pro",
      "productDescription": "苹果iPhone 15 Pro 智能手机",
      "productImageUrl": "https://example.com/images/iphone15pro.jpg",
      "skuName": "iPhone 15 Pro 深空黑色 256GB",
      "skuAttributes": {
        "color": "深空黑色",
        "storage": "256GB",
        "network": "5G"
      },
      "unitPrice": 999.00,
      "currencyCode": "USD",
      "displayPrice": "USD 999.00",
      "purchaseQuantity": 2,
      "appliedQuantity": 0,
      "availableQuantity": 2,
      "unitVolume": 0.0002,
      "unitWeight": 0.221,
      "productStatus": 1,
      "statusText": "正常"
    }
  ],
  "timestamp": 1703750400000
}
```

### 2.2 创建售后申请

**接口地址:** `POST /aftersale/apply`

**请求参数:**
```json
{
  "orderNo": "CO202412280001",
  "subOrderNo": "SUB202412280001",
  "userId": 12345,
  "asType": 1,
  "applyReason": "商品质量问题",
  "applyQuantity": 2
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| orderNo | String | 是 | 主订单号 | CO202412280001 |
| subOrderNo | String | 是 | 子订单号 | SUB202412280001 |
| userId | Long | 是 | 用户ID | 12345 |
| asType | Integer | 是 | 售后类型：1-发货前退款，2-发货后退款，3-发货后退货退款 | 1 |
| applyReason | String | 是 | 申请原因 | 商品质量问题 |
| applyQuantity | Integer | 是 | 申请数量，必须大于0 | 2 |

**响应示例:**
```json
{
  "code": 200,
  "message": "售后申请提交成功",
  "data": "AS20241228000001",
  "timestamp": 1703750400000
}
```

### 2.3 获取运费明细

**接口地址:** `GET /aftersale/shipping-fee/{serviceNumber}`

**路径参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| serviceNumber | String | 是 | 售后单号 | AS20241228000001 |

**响应示例:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "serviceNumber": "AS20241228000001",
    "applyQuantity": 2,
    "returnVolume": 0.0004,
    "returnWeight": 0.442,
    "shippingFee": 22.10,
    "serviceFee": 2.21,
    "totalLogisticsFee": 24.31,
    "currencyCode": "USD",
    "expireTime": "2024-12-28 15:30:00",
    "remainingMinutes": 25,
    "logisticsPaid": 0,
    "paidStatusText": "未支付"
  },
  "timestamp": 1703750400000
}
```

### 2.4 支付物流费用

**接口地址:** `POST /aftersale/pay-logistics`

**请求参数:**
```json
{
  "serviceNumber": "AS20241228000001",
  "userId": 12345,
  "paymentAmount": 24.31,
  "paymentMethod": "credit_card",
  "paymentTransactionId": "TXN123456789"
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| serviceNumber | String | 是 | 售后单号 | AS20241228000001 |
| userId | Long | 是 | 用户ID | 12345 |
| paymentAmount | BigDecimal | 是 | 支付金额 | 24.31 |
| paymentMethod | String | 是 | 支付方式 | credit_card |
| paymentTransactionId | String | 否 | 支付流水号 | TXN123456789 |

**响应示例:**
```json
{
  "code": 200,
  "message": "支付成功",
  "data": true,
  "timestamp": 1703750400000
}
```

### 2.5 获取售后订单详情

**接口地址:** `GET /aftersale/detail/{serviceNumber}`

**路径参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| serviceNumber | String | 是 | 售后单号 | AS20241228000001 |

**响应示例:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "serviceNumber": "AS20241228000001",
    "orderNo": "CO202412280001",
    "subOrderNo": "SUB202412280001",
    "userId": 12345,
    "asType": 1,
    "asTypeText": "发货前退款",
    "applyReason": "商品质量问题",
    "asStatus": 5,
    "asStatusText": "已完成",
    "applyQuantity": 2,
    "applyAmount": 1998.00,
    "currencyCode": "USD",
    "returnVolume": 0.0004,
    "returnWeight": 0.442,
    "shippingFee": 22.10,
    "serviceFee": 2.21,
    "totalLogisticsFee": 24.31,
    "logisticsPaid": 1,
    "logisticsPayTime": "2024-12-28 15:15:00",
    "logisticsExpireTime": "2024-12-28 15:30:00",
    "approvedQuantity": 2,
    "actualRefundQuantity": 2,
    "actualRefundAmount": 1998.00,
    "supplierOrderNo": "SUP_ORDER_123",
    "supplierAsNo": "SUP_AS_456",
    "createdTime": "2024-12-28 15:00:00",
    "lastModifiedTime": "2024-12-28 16:00:00",
    "processLogs": [
      {
        "operationType": 1,
        "operationTypeText": "申请",
        "operationDesc": "申请",
        "operatorId": "12345",
        "operatorName": "用户",
        "remark": "用户申请发货前退款",
        "createdTime": "2024-12-28 15:00:00"
      }
    ],
    "products": [
      {
        "productId": 12345,
        "productName": "iPhone 15 Pro",
        "unitPrice": 999.00,
        "availableQuantity": 2
      }
    ]
  },
  "timestamp": 1703750400000
}
```

### 2.6 分页查询用户售后订单

**接口地址:** `GET /aftersale/user-orders`

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| userId | Long | 是 | 用户ID | 12345 |
| asStatus | Integer | 否 | 售后状态筛选 | 1 |
| page | Integer | 否 | 页码，默认1 | 1 |
| size | Integer | 否 | 页大小，默认20 | 20 |

**请求示例:**
```bash
GET /cashop-aftersale/api/aftersale/user-orders?userId=12345&asStatus=1&page=1&size=20
```

**响应示例:**
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "records": [
      {
        "serviceNumber": "AS20241228000001",
        "orderNo": "CO202412280001",
        "asType": 1,
        "asTypeText": "发货前退款",
        "asStatus": 5,
        "asStatusText": "已完成",
        "applyQuantity": 2,
        "applyAmount": 1998.00,
        "currencyCode": "USD",
        "createdTime": "2024-12-28 15:00:00"
      }
    ],
    "total": 1,
    "size": 20,
    "current": 1,
    "pages": 1
  },
  "timestamp": 1703750400000
}
```

### 2.7 取消售后申请

**接口地址:** `POST /aftersale/cancel/{serviceNumber}`

**路径参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| serviceNumber | String | 是 | 售后单号 | AS20241228000001 |

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| userId | Long | 是 | 用户ID | 12345 |

**请求示例:**
```bash
POST /cashop-aftersale/api/aftersale/cancel/AS20241228000001?userId=12345
```

**响应示例:**
```json
{
  "code": 200,
  "message": "取消成功",
  "data": true,
  "timestamp": 1703750400000
}
```

## 3. 第三方接口

### 3.1 接收第三方消息

**接口地址:** `POST /third-party/message/receive`

**请求参数:**
```json
{
  "messageId": "MSG123456789",
  "businessType": "aftersale_result",
  "data": {
    "cashopServiceNumber": "AS20241228000001",
    "status": "approved",
    "approvedQuantity": 2,
    "supplierAsNo": "SUP_AS_456"
  }
}
```

**响应示例:**
```json
{
  "code": 200,
  "message": "消息接收成功",
  "data": null,
  "timestamp": 1703750400000
}
```

## 4. 状态码说明

### 4.1 售后类型 (asType)
| 值 | 说明 |
|----|------|
| 1 | 发货前退款 |
| 2 | 发货后退款 |
| 3 | 发货后退货退款 |

### 4.2 售后状态 (asStatus)
| 值 | 说明 |
|----|------|
| 1 | 申请中 |
| 2 | 待支付物流费用 |
| 3 | 已提交供货平台 |
| 4 | 供货平台处理中 |
| 5 | 已完成 |
| 6 | 已拒绝 |
| 7 | 超时撤销 |

### 4.3 物流费用支付状态 (logisticsPaid)
| 值 | 说明 |
|----|------|
| 0 | 未支付 |
| 1 | 已支付 |
| 2 | 已退还 |

## 5. 错误处理

### 5.1 常见错误码
| 错误码 | 说明 | 解决方案 |
|--------|------|----------|
| INVALID_PARAM | 参数错误 | 检查请求参数格式和必填项 |
| ORDER_NOT_FOUND | 订单不存在 | 确认订单号是否正确 |
| INSUFFICIENT_QUANTITY | 申请数量不足 | 检查可申请数量 |
| PAYMENT_TIMEOUT | 支付超时 | 重新申请或联系客服 |
| SUPPLIER_ERROR | 供货平台错误 | 联系客服处理 |

### 5.2 错误响应示例
```json
{
  "code": 400,
  "message": "申请数量超过可申请数量，可申请数量：1",
  "data": null,
  "timestamp": 1703750400000
}
```

## 6. 前端集成建议

### 6.1 状态轮询
对于需要实时更新状态的场景，建议：
- 支付完成后轮询订单状态
- 轮询间隔：5-10秒
- 最大轮询时间：5分钟

### 6.2 用户体验优化
- 显示剩余支付时间倒计时
- 提供清晰的状态说明
- 支持订单状态变更通知
- 提供客服联系方式

### 6.3 异常处理
- 网络超时重试机制
- 友好的错误提示
- 降级处理方案
