# 支付回调测试示例

## 测试场景

使用支付系统的`QueryPaymentResponseDTO`进行支付回调测试。

## 测试用例

### 1. 支付成功回调测试

**请求URL:** `POST http://localhost:8080/api/aftersale/payment/callback`

**请求Headers:**
```
Content-Type: application/json
```

**请求Body:**
```json
{
    "paymentTradeNo": "PAY20250823001",
    "businessOrderNo": "AS202501220001",
    "status": "SUCCESS",
    "paidAmount": 15.50,
    "basePaidAmount": 120.00,
    "currency": "USD",
    "baseCurrency": "HKD",
    "finishTime": "2025-08-23T10:35:00",
    "thirdPartyTradeNo": "TP20250823001",
    "extInfo": "{\"channel\":\"AEON_PAY\",\"method\":\"QR_CODE\"}"
}
```

**期望响应:**
```json
{
    "code": 200,
    "message": "回调处理成功",
    "data": "回调处理成功"
}
```

### 2. 支付失败回调测试

**请求Body:**
```json
{
    "paymentTradeNo": "PAY20250823002",
    "businessOrderNo": "AS202501220001",
    "status": "FAILED",
    "paidAmount": 0.00,
    "currency": "USD",
    "finishTime": "2025-08-23T10:35:00",
    "thirdPartyTradeNo": "TP20250823002",
    "extInfo": "{\"error\":\"INSUFFICIENT_BALANCE\"}"
}
```

## 业务逻辑验证

### 支付成功后的状态变更
1. 订单状态从 `2`(待支付物流费用) 更新为 `3`(已支付，待审核)
2. `logistics_paid` 字段更新为 `1`(已支付)
3. `payment_transaction_id` 字段记录支付交易号
4. `logistics_pay_time` 字段记录支付完成时间
5. 触发一致性框架任务，提交给供货平台

### 支付失败后的处理
1. 订单状态保持不变，允许用户重新支付
2. 记录支付失败日志
3. 可发送支付失败通知（待实现）

## 数据验证

### 回调数据验证项
- ✅ 售后订单存在性验证 (`businessOrderNo` 对应的售后单号)
- ✅ 订单状态验证 (必须是待支付状态)
- ✅ 支付金额验证 (`paidAmount` 与订单物流费用匹配)
- ✅ 支付状态处理 (SUCCESS/PAID 为成功，FAILED/FAIL 为失败)

### 错误处理
- 订单不存在：返回 `false`，记录错误日志
- 订单状态不正确：返回 `true`，避免重复回调
- 支付金额不匹配：返回 `false`，记录错误日志
- 未知支付状态：返回 `false`，记录警告日志

## 集成要点

1. **使用支付系统标准DTO**: 直接使用 `com.cashop.payment.facade.dto.QueryPaymentResponseDTO`
2. **业务订单号映射**: `businessOrderNo` 字段对应售后单号 `serviceNumber`
3. **支付状态映射**: 
   - 成功状态: `SUCCESS` 或 `PAID`
   - 失败状态: `FAILED` 或 `FAIL`
4. **金额验证**: 比较 `paidAmount` 与 `totalLogisticsFee`
5. **时间记录**: 使用 `finishTime` 更新 `logisticsPayTime`

## 注意事项

- 确保支付系统依赖已正确添加到 service 模块
- 回调接口具有幂等性，重复调用不会产生副作用
- 支付成功后会自动触发供货平台提交流程
- 所有关键操作都有详细的日志记录
