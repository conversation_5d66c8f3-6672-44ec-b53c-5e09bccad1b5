app:
  id: cashop-aftersale

apollo:
  meta: http://apollo.castable.hk:8080
  bootstrap:
    enabled: true
    namespaces: application
  cluster: ${apollo.cluster:default}

spring:
  application:
    name: cashop-aftersale
  profiles:
    active: local  # 默认使用本地环境，可通过启动参数覆盖
  main:
    allow-bean-definition-overriding: true

---

spring:
  config:
    activate:
      on-profile: local
  datasource:
    url: *****************************************************************************************************************
    username: root
    password: 
    driver-class-name: com.mysql.cj.jdbc.Driver
  sql:
    init:
      mode: never
