app:
  id: cashop-aftersale

apollo:
  meta: http://apollo.castable.hk:8080
  bootstrap:
    enabled: true
    namespaces: application
  cluster: ${apollo.cluster:default}

spring:
  application:
    name: cashop-aftersale
  profiles:
    active: stable
  main:
    allow-bean-definition-overriding: true

---

spring:
  config:
    activate:
      on-profile: stable
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfigurationV2
  datasource:
    url: *****************************************************************************************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver
  sql:
    init:
      mode: never

# MyBatis配置
mybatis:
  mapper-locations:
    - classpath:mapper/**/*.xml
    - classpath:com/mengxiang/transaction/framework/mapper/*.xml
  type-aliases-package:
    - com.cashop.aftersale.dao.entity
    - com.mengxiang.transaction.framework.dao
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false

# 事务一致性框架配置
com:
  mengxiang:
    transaction:
      task:
        enable: true
        enableImmediatelyDelete: true
        taskExpireTime: 7
        deleteTaskStatus: SUCCESS

        # 努力确保型任务线程池配置
        insureCorePoolSize: 2
        insureMaxPoolSize: 10
        insureQueueCapacity: 1000

        # 异常冲正型任务线程池配置
        reversalCorePoolSize: 2
        reversalMaxPoolSize: 10
        reversalQueueCapacity: 1000

        # 定时任务重试配置
        retryFetchSize: 50
        errorLogBeginTimes: 1

        # 线程拒绝策略配置
        insureRejectHandlerClass: java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
        reversalRejectHandlerClass: java.util.concurrent.ThreadPoolExecutor$AbortPolicy

        # 重复key报错日志级别
        duplicateKeyLogLevel: ERROR

        # 分片调度数量
        useShardingNum: 1

# XXL-Job配置
xxl:
  job:
    admin:
      addresses: http://localhost:8080/xxl-job-admin
    executor:
      appname: cashop-aftersale
      address:
      ip:
      port: 9999
      logpath: /tmp/xxl-job/jobhandler
      logretentiondays: 30
    accessToken:
