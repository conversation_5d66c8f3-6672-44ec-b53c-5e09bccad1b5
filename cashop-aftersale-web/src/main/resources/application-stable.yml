spring:
  config:
    activate:
      on-profile: stable

# Apollo配置 - stable环境
apollo:
  cluster: stable
  meta: http://apollo.castable.hk:8080

# 数据库配置 - 从Apollo获取，这里提供默认配置作为备用
spring:
  datasource:
    # 这些配置应该从Apollo配置中心获取
    # 如果Apollo连接失败，可以临时使用这些本地配置
    url: *****************************************************************************************************************
    username: root
    password: 
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      connection-test-query: SELECT 1
  sql:
    init:
      mode: never

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.cashop.aftersale.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: false
    auto-mapping-behavior: partial
    auto-mapping-unknown-column-behavior: warning
    default-executor-type: simple
    default-statement-timeout: 25
    default-fetch-size: 100
    safe-row-bounds-enabled: false
    local-cache-scope: session
    jdbc-type-for-null: other
    lazy-load-trigger-methods: equals,clone,hashCode,toString

# 日志配置
logging:
  level:
    com.cashop.aftersale: DEBUG
    com.mengxiang.transaction: DEBUG
    org.springframework.web: INFO
    org.springframework.jdbc: DEBUG
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 200
    min-spare-threads: 10
