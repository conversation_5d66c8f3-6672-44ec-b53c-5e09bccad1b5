# 本地开发环境配置
spring:
  
  # 数据源配置
  datasource:
    druid:
      url: ***************************************************************************************************************************************************************************************
      username: root
      password: 123456
      driver-class-name: com.mysql.cj.jdbc.Driver
      
      # 连接池配置
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 监控配置
      filters: stat,wall,log4j2
      web-stat-filter:
        enabled: true
        url-pattern: /*
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
      stat-view-servlet:
        enabled: true
        url-pattern: /druid/*
        reset-enable: false
        login-username: admin
        login-password: admin

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.cashop.aftersale.dao.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    aggressive-lazy-loading: false

# 事务一致性框架配置（本地开发可以禁用）
com:
  mengxiang:
    transaction:
      task:
        enable: false

# 日志配置
logging:
  level:
    com.cashop.aftersale: DEBUG
    com.mengxiang.transaction: INFO
    org.mybatis: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /aftersale

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,druid
  endpoint:
    health:
      show-details: always
