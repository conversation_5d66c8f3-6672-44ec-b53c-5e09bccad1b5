package com.cashop.aftersale.web.controller;

import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;
import com.cashop.aftersale.service.AftersaleOrderService;
import com.cashop.common.base.response.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

// import javax.validation.Valid;

/**
 * 第三方接口控制器
 * Web层 - 第三方系统接口
 */
@Slf4j
@RestController
@RequestMapping("/api/third-party")
@RequiredArgsConstructor
public class ThirdPartyController {

    private final AftersaleOrderService aftersaleOrderService;

    /**
     * 接收第三方供货平台消息
     */
    @PostMapping("/message/receive")
    public Result<String> receiveMessage(@RequestBody String rawMessage) {
        log.info("接收第三方消息: length={}", rawMessage != null ? rawMessage.length() : 0);

        // TODO: 实现第三方消息处理逻辑
        // 这里可以调用相应的 Service 来处理第三方消息
        log.info("第三方消息处理完成");
        
        return Result.success("消息接收成功");
    }

    /**
     * 批量检查SKU单是否支持申请售后
     * 供订单系统调用，用于前端判断显示申请售后按钮还是售后进度按钮
     */
    @PostMapping("/aftersale/support/check")
    public Result<SkuAftersaleSupportVO> checkSkuAftersaleSupport(@RequestBody SkuAftersaleSupportRequest request) {
        log.info("批量检查SKU售后支持请求: subOrderNos={}, userId={}", 
                request.getSubOrderNos(), request.getUserId());

        try {
            SkuAftersaleSupportVO result = aftersaleOrderService.checkSkuAftersaleSupport(request);
            
            log.info("批量SKU售后支持检查完成: 请求数量={}, 返回结果数量={}", 
                    request.getSubOrderNos() != null ? request.getSubOrderNos().size() : 0,
                    result.getItems() != null ? result.getItems().size() : 0);
            
            return Result.success(result);
            
        } catch (Exception e) {
            log.error("批量检查SKU售后支持异常: subOrderNos={}", 
                    request.getSubOrderNos(), e);
            return Result.error("检查售后支持状态失败: " + e.getMessage());
        }
    }
}
