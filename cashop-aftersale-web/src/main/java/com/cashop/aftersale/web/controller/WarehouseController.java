package com.cashop.aftersale.web.controller;

import com.cashop.aftersale.facade.dto.WarehouseAftersaleCreateRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsInfoRequest;
import com.cashop.aftersale.facade.dto.WarehouseOutboundCheckRequest;
import com.cashop.aftersale.facade.vo.WarehouseAftersaleCreateVO;
import com.cashop.aftersale.facade.vo.WarehouseOutboundCheckVO;
import com.cashop.aftersale.service.WarehouseAftersaleService;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 仓库系统售后接口控制器
 * Web层 - 仓库系统相关的售后接口
 */
@Tag(name = "仓库系统接口", description = "仓库系统相关的售后管理接口")
@Slf4j
@RestController
@RequestMapping("/api/warehouse")
@RequiredArgsConstructor
public class WarehouseController {

    private final WarehouseAftersaleService warehouseAftersaleService;

    @Operation(
        summary = "仓库创建次品售后申请",
        description = "仓库发现次品时创建售后申请"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "售后申请创建成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "创建售后申请失败")
    })
    @PostMapping("/aftersale/create")
    public Result<WarehouseAftersaleCreateVO> createDefectAftersale(
            @Parameter(description = "仓库售后创建请求", required = true)
            @RequestBody WarehouseAftersaleCreateRequest request) {
        log.info("仓库创建次品售后申请: subOrderNo={}, skuId={}, defectQuantity={}, operatorId={}", 
                request.getSubOrderNo(), request.getSkuId(), request.getDefectQuantity(), request.getOperatorId());

        try {
            WarehouseAftersaleCreateVO result = warehouseAftersaleService.createDefectAftersale(request);
            
            if (result.getSuccess()) {
                log.info("仓库次品售后申请创建成功: serviceNumber={}", result.getServiceNumber());
                return Result.success(result);
            } else {
                log.warn("仓库次品售后申请创建失败: {}", result.getErrorMessage());
                return Result.error(result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("仓库创建次品售后申请异常: subOrderNo={}, skuId={}", 
                    request.getSubOrderNo(), request.getSkuId(), e);
            return Result.error("创建售后申请失败: " + e.getMessage());
        }
    }

    @Operation(
        summary = "仓库填写物流信息",
        description = "仓库提交物流信息，包括物流公司和快递单号"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "物流信息提交成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "物流信息提交失败")
    })
    @PostMapping("/aftersale/logistics")
    public Result<String> submitLogisticsInfo(
            @Parameter(description = "仓库物流信息请求", required = true)
            @RequestBody WarehouseLogisticsInfoRequest request) {
        log.info("仓库填写物流信息: serviceNumber={}, logisticsCompany={}, trackingNumber={}", 
                request.getServiceNumber(), request.getLogisticsCompany(), request.getTrackingNumber());

        try {
            Boolean success = warehouseAftersaleService.submitLogisticsInfo(request);
            
            if (success) {
                log.info("仓库物流信息提交成功: serviceNumber={}", request.getServiceNumber());
                return Result.success("物流信息已提交");
            } else {
                log.warn("仓库物流信息提交失败: serviceNumber={}", request.getServiceNumber());
                return Result.error("物流信息提交失败");
            }

        } catch (Exception e) {
            log.error("仓库填写物流信息异常: serviceNumber={}", request.getServiceNumber(), e);
            return Result.error("提交物流信息失败: " + e.getMessage());
        }
    }

    @Operation(
        summary = "仓库售后状态查询",
        description = "提供商品售后状态信息供仓库参考，不做出库决策，不记录检查结果"
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "售后状态查询成功"),
        @ApiResponse(responseCode = "400", description = "请求参数错误"),
        @ApiResponse(responseCode = "500", description = "售后状态查询失败")
    })
    @PostMapping("/outbound/check")
    public Result<WarehouseOutboundCheckVO> checkOutboundAftersale(
            @Parameter(description = "仓库出库售后状态查询请求", required = true)
            @RequestBody WarehouseOutboundCheckRequest request) {
        log.info("仓库售后状态查询: outboundOrderNo={}, itemCount={}", 
                request.getOutboundOrderNo(), request.getItems() != null ? request.getItems().size() : 0);

        try {
            WarehouseOutboundCheckVO result = warehouseAftersaleService.checkOutboundAftersale(request);

            log.info("仓库售后状态查询完成: outboundOrderNo={}, itemCount={}", 
                    request.getOutboundOrderNo(), 
                    result.getItems() != null ? result.getItems().size() : 0);

            return Result.success(result);

        } catch (Exception e) {
            log.error("仓库售后状态查询异常: outboundOrderNo={}", request.getOutboundOrderNo(), e);
            return Result.error("售后状态查询失败: " + e.getMessage());
        }
    }
}
