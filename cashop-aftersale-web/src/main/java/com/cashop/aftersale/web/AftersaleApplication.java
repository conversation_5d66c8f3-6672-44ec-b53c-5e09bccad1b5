package com.cashop.aftersale.web;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 售后系统启动类
 * Web层 - 应用启动入口
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.cashop.aftersale.web", 
        "com.cashop.aftersale.service",
        "com.mengxiang.transaction.framework" // Explicitly scan the transaction framework package
    },
    exclude = {
        MybatisPlusAutoConfiguration.class, // Exclude MyBatis-Plus auto-configuration
        com.baomidou.mybatisplus.autoconfigure.MybatisPlusLanguageDriverAutoConfiguration.class,
        org.springframework.cloud.netflix.eureka.EurekaClientAutoConfiguration.class,
        org.springframework.cloud.netflix.eureka.EurekaDiscoveryClientConfiguration.class,
        org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClientAutoConfiguration.class
    }
)
@EnableTransactionManagement
@EnableScheduling
@EnableAsync
// @EnableFeignClients(basePackages = "com.cashop.aftersale.facade.client") // 使用本地实现，避免循环依赖
@MapperScan("com.cashop.aftersale.dao.mapper")
public class AftersaleApplication {

    public static void main(String[] args) {
        SpringApplication.run(AftersaleApplication.class, args);
        System.out.println("=================================");
        System.out.println("Cashop售后系统启动成功！");
        System.out.println("=================================");
    }
}
