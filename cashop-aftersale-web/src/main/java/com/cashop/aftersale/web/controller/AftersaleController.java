package com.cashop.aftersale.web.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cashop.aftersale.facade.dto.AftersaleApplyRequest;
import com.cashop.aftersale.facade.dto.PrePaymentRequest;
import com.cashop.aftersale.facade.dto.PrePaymentResponse;
import com.cashop.aftersale.facade.vo.AftersaleApplyPageVO;
import com.cashop.aftersale.facade.vo.AftersaleOrderDetailVO;
import com.cashop.aftersale.service.AftersaleOrderService;
import com.cashop.common.base.response.PageResult;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@Tag(
        name = "售后系统接口",
        description = "提供售后管理相关的所有功能，包括售后申请、查询、审核等操作。支持退款、退货退款等多种售后类型，提供完整的售后流程管理和状态跟踪。"
)
@RestController
@RequestMapping("/api/aftersale")
@RequiredArgsConstructor
public class AftersaleController {

    private final AftersaleOrderService aftersaleOrderService;

    @Operation(
            summary = "渲染售后申请页面",
            description = "获取申请售后页面所需的初始数据。调用时机：用户进入申请售后页面时。"
    )
    @GetMapping("/apply/render")
    public Result<AftersaleApplyPageVO> renderApplyPage(
            @RequestParam("userId") Long userId,
            @RequestParam("orderNo") String orderNo,
            @RequestParam("skuOrderNo") String skuOrderNo) {
        return Result.success(aftersaleOrderService.renderApplyPage(userId, orderNo, skuOrderNo));
    }

    @Operation(
            summary = "申请售后",
            description = "用户提交售后申请，支持退款和退货退款两种类型。调用时机：用户在订单详情页面点击申请售后按钮后。成功后返回售后单号，失败时根据具体错误类型返回相应错误码。"
    )
    @PostMapping("/apply")
    public Result<String> applyAftersale(@RequestBody AftersaleApplyRequest request) {
        return Result.success(aftersaleOrderService.createAftersaleOrder(request));
    }

    @Operation(
            summary = "查询售后详情",
            description = "根据售后单号查询售后订单的详细信息，包括订单信息、商品信息、流程记录等。调用时机：用户查看售后详情、客服处理售后工单等场景。成功后返回完整的售后详情信息。"
    )
    @GetMapping("/detail/{serviceNumber}")
    public Result<AftersaleOrderDetailVO> getAftersaleDetail(@PathVariable("serviceNumber") String serviceNumber) {
        return Result.success(aftersaleOrderService.getOrderDetail(serviceNumber));
    }

    @Operation(
            summary = "查询用户售后列表",
            description = "分页查询指定用户的售后订单列表，支持按状态筛选。调用时机：用户查看我的售后列表、客服查询用户售后历史等场景。成功后返回分页的售后订单列表。"
    )
    @GetMapping("/list")
    public PageResult<AftersaleOrderDetailVO> getUserAftersaleList(
            @RequestParam("userId") Long userId,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size,
            @RequestParam(value = "asStatus", required = false) Integer asStatus
    ) {
        IPage<AftersaleOrderDetailVO> pageData = aftersaleOrderService.getUserOrders(userId, asStatus, page, size);
        return PageResult.success((int) pageData.getCurrent(), (int) pageData.getSize(), pageData.getTotal(), pageData.getRecords());
    }

    @Operation(
            summary = "创建运费预支付订单",
            description = "用户为退货退款流程创建运费预支付订单。调用时机：用户在售后详情页点击支付运费时。成功后返回支付所需的信息。"
    )
    @PostMapping("/pre-payment")
    public Result<PrePaymentResponse> createPrePayment(@RequestBody PrePaymentRequest request) {
        return Result.success(aftersaleOrderService.createPrePayment(request));
    }

    @Operation(
            summary = "取消售后申请",
            description = "用户在商家处理前取消售后申请。调用时机：用户在售后详情页点击取消按钮。"
    )
    @PostMapping("/cancel")
    public Result<Boolean> cancelAftersaleOrder(@RequestParam String serviceNumber, @RequestParam Long userId) {
        return Result.success(aftersaleOrderService.cancelAftersaleOrder(serviceNumber, userId));
    }

    @Operation(
            summary = "手动同步供货平台状态",
            description = "手动同步供货平台的售后处理状态。调用时机：管理员发现状态不同步时手动触发。成功后更新本地状态，失败时返回同步失败原因。"
    )
    @PostMapping("/sync-supplier-status")
    public Result<Boolean> syncSupplierStatus(@RequestParam("serviceNumber") String serviceNumber) {
        return Result.success(aftersaleOrderService.resendToSupplier(serviceNumber));
    }
}
