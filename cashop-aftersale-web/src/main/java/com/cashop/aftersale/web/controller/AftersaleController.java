package com.cashop.aftersale.web.controller;

import com.cashop.aftersale.facade.dto.PrePaymentRequest;
import com.cashop.aftersale.facade.dto.PrePaymentResponse;
import com.cashop.aftersale.facade.dto.PaymentCallbackRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsRequest;
import com.cashop.aftersale.facade.dto.ReturnServiceFeeRefundRequest;
import com.cashop.payment.facade.dto.QueryPaymentResponseDTO;
import com.cashop.aftersale.service.AftersaleOrderService;
import com.cashop.common.base.response.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

/**
 * 售后控制器
 * Web层 - HTTP接口定义
 */
@Slf4j
@RestController
@RequestMapping("/api/aftersale")
@RequiredArgsConstructor
public class AftersaleController {

    private final AftersaleOrderService aftersaleOrderService;

    /**
     * 测试接口
     */
    @GetMapping("/test")
    public String test() {
        log.info("售后系统测试接口被调用");
        return "售后系统运行正常";
    }

    /**
     * 创建预支付订单
     * 用于退货退款时支付运费
     */
    @PostMapping("/prepay")
    public Result<PrePaymentResponse> createPrePayment(@RequestBody @Valid PrePaymentRequest request) {
        try {
            log.info("创建预支付订单请求: serviceNumber={}, userId={}, amount={}, paymentMethod={}", 
                    request.getServiceNumber(), request.getUserId(), request.getAmount(), request.getPaymentMethod());

            PrePaymentResponse response = aftersaleOrderService.createPrePayment(request);

            log.info("预支付订单创建成功: serviceNumber={}, prepayId={}", 
                    request.getServiceNumber(), response.getPrepayId());

            return Result.success(response);

        } catch (Exception e) {
            log.error("创建预支付订单失败: serviceNumber={}", request.getServiceNumber(), e);
            return Result.error("创建预支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 支付回调接口
     * 供支付系统回调使用
     * 接收支付系统的QueryPaymentResponseDTO，转换为内部的PaymentCallbackRequest
     */
    @PostMapping("/payment/callback")
    public Result<String> paymentCallback(@RequestBody @Valid QueryPaymentResponseDTO response,
                                        @RequestParam(required = false) Long userId) {
        try {
            log.info("接收支付回调: businessOrderNo={}, paymentTradeNo={}, status={}", 
                    response.getBusinessOrderNo(), response.getPaymentTradeNo(), response.getStatus());

            // 转换外部DTO为内部DTO
            PaymentCallbackRequest internalRequest = convertToInternalRequest(response, userId);
            
            boolean success = aftersaleOrderService.handlePaymentCallback(internalRequest);

            if (success) {
                log.info("支付回调处理成功: businessOrderNo={}", response.getBusinessOrderNo());
                return Result.success("回调处理成功");
            } else {
                log.error("支付回调处理失败: businessOrderNo={}", response.getBusinessOrderNo());
                return Result.error("回调处理失败");
            }

        } catch (Exception e) {
            log.error("支付回调处理异常: businessOrderNo={}", response.getBusinessOrderNo(), e);
            return Result.error("回调处理异常: " + e.getMessage());
        }
    }

    /**
     * 转换支付系统DTO为内部DTO
     * 只在web层处理外部依赖，保持service层的纯净
     */
    private PaymentCallbackRequest convertToInternalRequest(QueryPaymentResponseDTO response, Long userId) {
        PaymentCallbackRequest request = new PaymentCallbackRequest();
        request.setPaymentTradeNo(response.getPaymentTradeNo());
        request.setBusinessOrderNo(response.getBusinessOrderNo());
        request.setStatus(response.getStatus());
        request.setPaidAmount(response.getPaidAmount());
        request.setBasePaidAmount(response.getBasePaidAmount());
        request.setCurrency(response.getCurrency());
        request.setBaseCurrency(response.getBaseCurrency());
        request.setFinishTime(response.getFinishTime());
        request.setThirdPartyTradeNo(response.getThirdPartyTradeNo());
        request.setExtInfo(response.getExtInfo());
        request.setUserId(userId);
        return request;
    }

    /**
     * 仓库填写物流信息接口
     * 供仓库系统调用，填写物流信息和增值服务费
     */
    @PostMapping("/warehouse/logistics")
    public Result<String> updateWarehouseLogistics(@RequestBody @Valid WarehouseLogisticsRequest request) {
        try {
            log.info("仓库填写物流信息请求: serviceNumber={}, trackingNumber={}, valueAddedServiceFee={}", 
                    request.getServiceNumber(), request.getWarehouseTrackingNumber(), request.getValueAddedServiceFee());

            boolean success = aftersaleOrderService.updateWarehouseLogistics(request);

            if (success) {
                log.info("仓库物流信息填写成功: serviceNumber={}", request.getServiceNumber());
                return Result.success("物流信息填写成功");
            } else {
                log.error("仓库物流信息填写失败: serviceNumber={}", request.getServiceNumber());
                return Result.error("物流信息填写失败");
            }

        } catch (Exception e) {
            log.error("仓库物流信息填写异常: serviceNumber={}", request.getServiceNumber(), e);
            return Result.error("物流信息填写异常: " + e.getMessage());
        }
    }

    /**
     * 退还退货服务费接口
     * 用于用户撤销售后或商家拒绝时的服务费退还
     */
    @PostMapping("/refund/service-fee")
    public Result<String> refundReturnServiceFee(@RequestBody @Valid ReturnServiceFeeRefundRequest request) {
        try {
            log.info("退还退货服务费请求: serviceNumber={}, userId={}, refundReasonType={}", 
                    request.getServiceNumber(), request.getUserId(), request.getRefundReasonType());

            boolean success = aftersaleOrderService.refundReturnServiceFee(request);

            if (success) {
                log.info("退货服务费退还成功: serviceNumber={}", request.getServiceNumber());
                return Result.success("退货服务费退还成功");
            } else {
                log.error("退货服务费退还失败: serviceNumber={}", request.getServiceNumber());
                return Result.error("退货服务费退还失败");
            }

        } catch (Exception e) {
            log.error("退货服务费退还异常: serviceNumber={}", request.getServiceNumber(), e);
            return Result.error("退货服务费退还异常: " + e.getMessage());
        }
    }
}