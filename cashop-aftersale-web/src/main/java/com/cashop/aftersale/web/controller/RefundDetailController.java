package com.cashop.aftersale.web.controller;

import com.cashop.aftersale.common.result.Result;
import com.cashop.aftersale.dao.entity.AsRefundDetail;
import com.cashop.aftersale.facade.vo.RefundDetailVO;
import com.cashop.aftersale.service.AsRefundDetailService;
import com.cashop.aftersale.common.constants.RefundDetailConstants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 退款明细Controller
 * Web层 - 退款明细相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/refund-detail")
@RequiredArgsConstructor
public class RefundDetailController {

    private final AsRefundDetailService asRefundDetailService;

    /**
     * 根据售后单号查询退款明细列表
     */
    @GetMapping("/list/{serviceNumber}")
    public Result<List<RefundDetailVO>> getRefundDetailsByServiceNumber(@PathVariable String serviceNumber) {
        try {
            log.info("查询退款明细列表: serviceNumber={}", serviceNumber);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getRefundDetailsByServiceNumber(serviceNumber);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询退款明细列表成功: serviceNumber={}, 明细数量={}", serviceNumber, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询退款明细列表失败: serviceNumber={}", serviceNumber, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据售后订单ID查询退款明细列表
     */
    @GetMapping("/list/order/{asOrderId}")
    public Result<List<RefundDetailVO>> getRefundDetailsByAsOrderId(@PathVariable Long asOrderId) {
        try {
            log.info("查询退款明细列表: asOrderId={}", asOrderId);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getRefundDetailsByAsOrderId(asOrderId);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询退款明细列表成功: asOrderId={}, 明细数量={}", asOrderId, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询退款明细列表失败: asOrderId={}", asOrderId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据退款类型查询退款明细
     */
    @GetMapping("/list/order/{asOrderId}/type/{refundType}")
    public Result<List<RefundDetailVO>> getRefundDetailsByType(@PathVariable Long asOrderId, 
                                                              @PathVariable Integer refundType) {
        try {
            log.info("查询指定类型退款明细: asOrderId={}, refundType={}", asOrderId, refundType);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getRefundDetailsByType(asOrderId, refundType);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询指定类型退款明细成功: asOrderId={}, refundType={}, 明细数量={}", 
                    asOrderId, refundType, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询指定类型退款明细失败: asOrderId={}, refundType={}", asOrderId, refundType, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询售后订单的退款总金额
     */
    @GetMapping("/total-amount/{asOrderId}")
    public Result<BigDecimal> getTotalRefundAmount(@PathVariable Long asOrderId) {
        try {
            log.info("查询退款总金额: asOrderId={}", asOrderId);
            
            BigDecimal totalAmount = asRefundDetailService.getTotalRefundAmount(asOrderId);
            
            log.info("查询退款总金额成功: asOrderId={}, totalAmount={}", asOrderId, totalAmount);
            return Result.success("查询成功", totalAmount);
            
        } catch (Exception e) {
            log.error("查询退款总金额失败: asOrderId={}", asOrderId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询指定类型的退款总金额
     */
    @GetMapping("/amount/{asOrderId}/type/{refundType}")
    public Result<BigDecimal> getRefundAmountByType(@PathVariable Long asOrderId, 
                                                   @PathVariable Integer refundType) {
        try {
            log.info("查询指定类型退款金额: asOrderId={}, refundType={}", asOrderId, refundType);
            
            BigDecimal amount = asRefundDetailService.getRefundAmountByType(asOrderId, refundType);
            
            log.info("查询指定类型退款金额成功: asOrderId={}, refundType={}, amount={}", 
                    asOrderId, refundType, amount);
            return Result.success("查询成功", amount);
            
        } catch (Exception e) {
            log.error("查询指定类型退款金额失败: asOrderId={}, refundType={}", asOrderId, refundType, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询商品退款明细
     */
    @GetMapping("/product-refunds/{asOrderId}")
    public Result<List<RefundDetailVO>> getProductRefunds(@PathVariable Long asOrderId) {
        try {
            log.info("查询商品退款明细: asOrderId={}", asOrderId);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getProductRefunds(asOrderId);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询商品退款明细成功: asOrderId={}, 明细数量={}", asOrderId, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询商品退款明细失败: asOrderId={}", asOrderId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询服务费退款明细
     */
    @GetMapping("/service-fee-refunds/{asOrderId}")
    public Result<List<RefundDetailVO>> getServiceFeeRefunds(@PathVariable Long asOrderId) {
        try {
            log.info("查询服务费退款明细: asOrderId={}", asOrderId);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getServiceFeeRefunds(asOrderId);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询服务费退款明细成功: asOrderId={}, 明细数量={}", asOrderId, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询服务费退款明细失败: asOrderId={}", asOrderId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询运费退款明细
     */
    @GetMapping("/shipping-fee-refunds/{asOrderId}")
    public Result<List<RefundDetailVO>> getShippingFeeRefunds(@PathVariable Long asOrderId) {
        try {
            log.info("查询运费退款明细: asOrderId={}", asOrderId);
            
            List<AsRefundDetail> refundDetails = asRefundDetailService.getShippingFeeRefunds(asOrderId);
            List<RefundDetailVO> refundDetailVOs = refundDetails.stream()
                    .map(this::convertToVO)
                    .collect(Collectors.toList());
            
            log.info("查询运费退款明细成功: asOrderId={}, 明细数量={}", asOrderId, refundDetailVOs.size());
            return Result.success("查询成功", refundDetailVOs);
            
        } catch (Exception e) {
            log.error("查询运费退款明细失败: asOrderId={}", asOrderId, e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 转换为VO对象
     */
    private RefundDetailVO convertToVO(AsRefundDetail refundDetail) {
        RefundDetailVO vo = new RefundDetailVO();
        BeanUtils.copyProperties(refundDetail, vo);
        
        // 设置文本显示
        vo.setRefundTypeText(RefundDetailConstants.getRefundTypeText(refundDetail.getRefundType()));
        vo.setRefundStatusText(RefundDetailConstants.getRefundStatusText(refundDetail.getRefundStatus()));
        
        // 格式化金额显示
        DecimalFormat df = new DecimalFormat("#,##0.00");
        vo.setDisplayAmount(df.format(refundDetail.getRefundAmount()) + " " + refundDetail.getCurrencyCode());
        
        if (refundDetail.getUnitPrice() != null) {
            vo.setDisplayUnitPrice(df.format(refundDetail.getUnitPrice()) + " " + refundDetail.getCurrencyCode());
        }
        
        if (refundDetail.getUnitServiceFee() != null) {
            vo.setDisplayUnitServiceFee(df.format(refundDetail.getUnitServiceFee()) + " " + refundDetail.getCurrencyCode());
        }
        
        return vo;
    }
}
