package com.cashop.aftersale.web.controller;

import com.cashop.aftersale.facade.client.AftersaleFacade;
import com.cashop.aftersale.facade.dto.AftersaleApplyRequestDTO;
import com.cashop.aftersale.facade.dto.AftersaleDetailResponseDTO;
import com.cashop.common.base.response.PageResult;
import com.cashop.common.base.response.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 售后系统对外Facade接口控制器
 * 提供HTTP接口供其他服务调用
 * 路径与Feign客户端定义保持一致
 */
@Tag(
    name = "售后系统对外接口",
    description = "提供售后管理相关的所有功能，供其他服务调用"
)
@Slf4j
@RestController
@RequestMapping("/api/aftersale")
@RequiredArgsConstructor
@Validated
public class AftersaleFacadeController {

    private final AftersaleFacade aftersaleFacade;

    @Operation(
        summary = "申请售后",
        description = "用户提交售后申请，支持退款和退货退款两种类型"
    )
    @PostMapping("/apply")
    public Result<String> applyAftersale(@RequestBody @Valid AftersaleApplyRequestDTO request) {
        return aftersaleFacade.applyAftersale(request);
    }

    @Operation(
        summary = "查询售后详情",
        description = "根据售后单号查询售后订单的详细信息"
    )
    @GetMapping("/detail/{serviceNumber}")
    public Result<AftersaleDetailResponseDTO> getAftersaleDetail(
            @PathVariable @NotBlank(message = "售后单号不能为空") String serviceNumber) {
        return aftersaleFacade.getAftersaleDetail(serviceNumber);
    }

    @Operation(
        summary = "查询用户售后列表",
        description = "分页查询指定用户的售后订单列表，支持按状态筛选"
    )
    @GetMapping("/list")
    public PageResult<AftersaleDetailResponseDTO> getUserAftersaleList(
            @RequestParam @NotNull(message = "用户ID不能为空") Long userId,
            @RequestParam(value = "asStatus", required = false) Integer asStatus,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "size", defaultValue = "10") Integer size) {
        return aftersaleFacade.getUserAftersaleList(userId, asStatus, page, size);
    }
}
