package com.cashop.aftersale.web.config;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * DdlApplicationRunner Bean后处理器
 * 用于替换有问题的ddlApplicationRunner Bean
 */
@Slf4j
@Component
public class DdlApplicationRunnerBeanPostProcessor implements BeanPostProcessor {

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if ("ddlApplicationRunner".equals(beanName)) {
            log.info("Replacing ddlApplicationRunner bean with custom implementation");
            return new ApplicationRunner() {
                @Override
                public void run(ApplicationArguments args) throws Exception {
                    log.info("Custom ddlApplicationRunner executed successfully - no DDL operations needed");
                }
            };
        }
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }
}

