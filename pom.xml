<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.cashop.base</groupId>
        <artifactId>base-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>com.cashop</groupId>
    <artifactId>cashop-aftersale</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <name>cashop-aftersale</name>
    <description>售后管理系统</description>

    <modules>
        <module>cashop-aftersale-common</module>
        <module>cashop-aftersale-dao</module>
        <module>cashop-aftersale-facade</module>
        <module>cashop-aftersale-service</module>
        <module>cashop-aftersale-web</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.release>17</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <skipTests>true</skipTests>

        <!-- 事务一致性框架 -->
        <transaction-framework.version>1.1.20</transaction-framework.version>
        
        <!-- 数据源 -->
        <druid.version>1.2.20</druid.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 内部模块依赖 -->
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-facade</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cashop</groupId>
                <artifactId>cashop-aftersale-service</artifactId>
                <version>${project.version}</version>
            </dependency>

            <!-- 事务一致性框架 -->
            <dependency>
                <groupId>com.mengxiang.base</groupId>
                <artifactId>transaction-framework</artifactId>
                <version>${transaction-framework.version}</version>
            </dependency>
            
            <!-- 数据源 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>17</source>
                        <target>17</target>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-resources-plugin</artifactId>
                    <version>3.3.1</version>
                    <configuration>
                        <encoding>UTF-8</encoding>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project> 