package com.cashop.aftersale.common.constants;

/**
 * 退款类型常量
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
public class RefundTypeConstants {

    /**
     * 商品退款（货款按件退）
     */
    public static final int PRODUCT_REFUND = 1;

    /**
     * 增值服务费退款（按件退）
     */
    public static final int VALUE_ADDED_SERVICE_REFUND = 2;

    /**
     * 国内段运费退款
     */
    public static final int DOMESTIC_SHIPPING_REFUND = 3;

    /**
     * 退货服务费退款（仓库作业费 + 仓库退商家仓运费）
     */
    public static final int RETURN_SERVICE_FEE_REFUND = 4;

    /**
     * 获取退款类型描述
     * 
     * @param refundType 退款类型
     * @return 退款类型描述
     */
    public static String getRefundTypeDescription(Integer refundType) {
        if (refundType == null) {
            return "未知类型";
        }
        
        switch (refundType) {
            case PRODUCT_REFUND:
                return "商品退款";
            case VALUE_ADDED_SERVICE_REFUND:
                return "增值服务费退款";
            case DOMESTIC_SHIPPING_REFUND:
                return "国内段运费退款";
            case RETURN_SERVICE_FEE_REFUND:
                return "退货服务费退款";
            default:
                return "未知类型";
        }
    }

    /**
     * 验证退款类型是否有效
     * 
     * @param refundType 退款类型
     * @return 是否有效
     */
    public static boolean isValidRefundType(Integer refundType) {
        return refundType != null && 
               (refundType == PRODUCT_REFUND || 
                refundType == VALUE_ADDED_SERVICE_REFUND || 
                refundType == DOMESTIC_SHIPPING_REFUND || 
                refundType == RETURN_SERVICE_FEE_REFUND);
    }
}
