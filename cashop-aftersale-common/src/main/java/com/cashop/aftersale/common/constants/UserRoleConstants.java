package com.cashop.aftersale.common.constants;

/**
 * 用户角色常量
 * 
 * <AUTHOR>
 * @since 2025-01-22
 */
public class UserRoleConstants {

    /**
     * 购买者
     */
    public static final int BUYER = 1;

    /**
     * 店主
     */
    public static final int SHOP_OWNER = 2;

    /**
     * 获取角色描述
     * 
     * @param roleCode 角色代码
     * @return 角色描述
     */
    public static String getRoleDescription(Integer roleCode) {
        if (roleCode == null) {
            return "未知角色";
        }
        
        switch (roleCode) {
            case BUYER:
                return "购买者";
            case SHOP_OWNER:
                return "店主";
            default:
                return "未知角色";
        }
    }

    /**
     * 验证角色代码是否有效
     * 
     * @param roleCode 角色代码
     * @return 是否有效
     */
    public static boolean isValidRole(Integer roleCode) {
        return roleCode != null && (roleCode == BUYER || roleCode == SHOP_OWNER);
    }
}
