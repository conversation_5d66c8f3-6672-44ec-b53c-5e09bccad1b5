package com.cashop.aftersale.common.constants;

/**
 * 售后系统常量定义
 */
public class AftersaleConstants {

    /**
     * 售后类型常量
     */
    public static class AsType {
        public static final int PRE_DELIVERY_REFUND = 1;      // 发货前退款
        public static final int POST_DELIVERY_REFUND = 2;     // 发货后退款
        public static final int POST_DELIVERY_RETURN_REFUND = 3; // 发货后退货退款
    }

    /**
     * 售后状态常量
     */
    public static class AsStatus {
        public static final int APPLYING = 1;                 // 申请中
        public static final int WAITING_LOGISTICS_PAYMENT = 2; // 待支付物流费用
        public static final int SUBMITTED_TO_SUPPLIER = 3;    // 已提交供货平台
        public static final int SUPPLIER_PROCESSING = 4;      // 供货平台处理中
        public static final int COMPLETED = 5;                // 已完成
        public static final int REJECTED = 6;                 // 已拒绝
        public static final int TIMEOUT_CANCELLED = 7;        // 超时撤销
    }

    /**
     * 物流费用支付状态
     */
    public static class LogisticsPaid {
        public static final int UNPAID = 0;    // 未支付
        public static final int PAID = 1;      // 已支付
        public static final int REFUNDED = 2;  // 已退还
    }

    /**
     * 消息消费状态
     */
    public static class ConsumeStatus {
        public static final int UNCONSUMED = 0; // 未消费
        public static final int SUCCESS = 1;    // 消费成功
        public static final int FAILED = 2;     // 消费失败
    }

    /**
     * 业务配置常量
     */
    public static class Config {
        public static final int LOGISTICS_PAYMENT_TIMEOUT_MINUTES = 30; // 物流费用支付超时时间(分钟)
        public static final int MESSAGE_MAX_RETRY_TIMES = 3;            // 消息最大重试次数
        public static final String SERVICE_NUMBER_PREFIX = "AS";        // 售后单号前缀
    }

    /**
     * 错误码常量
     */
    public static class ErrorCode {
        public static final String INVALID_PARAM = "INVALID_PARAM";
        public static final String ORDER_NOT_FOUND = "ORDER_NOT_FOUND";
        public static final String INSUFFICIENT_QUANTITY = "INSUFFICIENT_QUANTITY";
        public static final String PAYMENT_TIMEOUT = "PAYMENT_TIMEOUT";
        public static final String SUPPLIER_ERROR = "SUPPLIER_ERROR";
    }
}
