package com.cashop.aftersale.common.constants;

/**
 * 退款明细相关常量
 * Common层 - 退款明细常量定义
 */
public class RefundDetailConstants {

    /**
     * 退款类型
     */
    public static class RefundType {
        /** 商品退款 */
        public static final Integer PRODUCT_REFUND = 1;
        /** 服务费退款 */
        public static final Integer SERVICE_FEE_REFUND = 2;
        /** 运费退款 */
        public static final Integer SHIPPING_FEE_REFUND = 3;
    }

    /**
     * 退款状态
     */
    public static class RefundStatus {
        /** 待退款 */
        public static final Integer PENDING = 1;
        /** 退款中 */
        public static final Integer PROCESSING = 2;
        /** 退款成功 */
        public static final Integer SUCCESS = 3;
        /** 退款失败 */
        public static final Integer FAILED = 4;
    }

    /**
     * 退款类型文本映射
     */
    public static String getRefundTypeText(Integer refundType) {
        if (refundType == null) {
            return "未知";
        }
        
        switch (refundType) {
            case 1:
                return "商品退款";
            case 2:
                return "服务费退款";
            case 3:
                return "运费退款";
            default:
                return "未知";
        }
    }

    /**
     * 退款状态文本映射
     */
    public static String getRefundStatusText(Integer refundStatus) {
        if (refundStatus == null) {
            return "未知";
        }
        
        switch (refundStatus) {
            case 1:
                return "待退款";
            case 2:
                return "退款中";
            case 3:
                return "退款成功";
            case 4:
                return "退款失败";
            default:
                return "未知";
        }
    }

    /**
     * 默认退款原因
     */
    public static class DefaultRefundReason {
        /** 商品退款原因 */
        public static final String PRODUCT_REFUND_REASON = "商品退款";
        /** 服务费退款原因 */
        public static final String SERVICE_FEE_REFUND_REASON = "服务费退款";
        /** 运费退款原因 */
        public static final String SHIPPING_FEE_REFUND_REASON = "运费退款";
    }

    /**
     * 退款明细描述模板
     */
    public static class RefundDescriptionTemplate {
        /** 商品退款描述 */
        public static final String PRODUCT_REFUND_DESC = "商品退款: %s, 退款件数: %d, 单价: %s";
        /** 服务费退款描述 */
        public static final String SERVICE_FEE_REFUND_DESC = "服务费退款: 退款件数: %d, 单件服务费: %s";
        /** 运费退款描述 */
        public static final String SHIPPING_FEE_REFUND_DESC = "运费退款: 退款金额: %s";
    }
}
