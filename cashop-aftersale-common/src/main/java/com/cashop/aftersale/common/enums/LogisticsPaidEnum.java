package com.cashop.aftersale.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 物流费用支付状态枚举
 */
@Getter
@AllArgsConstructor
public enum LogisticsPaidEnum {

    /**
     * 未支付
     */
    UNPAID(0, "未支付"),

    /**
     * 已支付
     */
    PAID(1, "已支付"),

    /**
     * 已退还
     */
    REFUNDED(2, "已退还");

    private final Integer code;
    private final String desc;

    public static LogisticsPaidEnum getByCode(Integer code) {
        for (LogisticsPaidEnum paidEnum : values()) {
            if (paidEnum.getCode().equals(code)) {
                return paidEnum;
            }
        }
        return null;
    }
}
