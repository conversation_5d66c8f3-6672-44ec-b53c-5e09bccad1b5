package com.cashop.aftersale.common.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 售后单号生成器
 * 公共模块 - 工具类
 */
public class ServiceNumberGenerator {

    private static final String PREFIX = "AS";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final AtomicLong SEQUENCE = new AtomicLong(1);

    /**
     * 生成售后单号
     * 格式：AS + yyyyMMdd + 4位序列号
     * 例如：AS202312010001
     * 
     * @return 售后单号
     */
    public static String generate() {
        String dateStr = LocalDateTime.now().format(DATE_FORMATTER);
        long sequence = SEQUENCE.getAndIncrement();
        
        // 如果序列号超过9999，重置为1
        if (sequence > 9999) {
            SEQUENCE.set(1);
            sequence = 1;
        }
        
        return String.format("%s%s%04d", PREFIX, dateStr, sequence);
    }

    /**
     * 重置序列号（通常在新的一天开始时调用）
     */
    public static void resetSequence() {
        SEQUENCE.set(1);
    }
}