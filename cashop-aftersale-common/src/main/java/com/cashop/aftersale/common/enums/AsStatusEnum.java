package com.cashop.aftersale.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后状态枚举
 */
@Getter
@AllArgsConstructor
public enum AsStatusEnum {

    /**
     * 申请中
     */
    APPLYING(1, "申请中"),

    /**
     * 待支付物流费用
     */
    WAITING_LOGISTICS_PAYMENT(2, "待支付物流费用"),

    /**
     * 已提交供货平台
     */
    SUBMITTED_TO_SUPPLIER(3, "已提交供货平台"),

    /**
     * 供货平台处理中
     */
    SUPPLIER_PROCESSING(4, "供货平台处理中"),

    /**
     * 已完成
     */
    COMPLETED(5, "已完成"),

    /**
     * 已拒绝
     */
    REJECTED(6, "已拒绝"),

    /**
     * 超时撤销
     */
    TIMEOUT_CANCELLED(7, "超时撤销");

    private final Integer code;
    private final String desc;

    public static AsStatusEnum getByCode(Integer code) {
        for (AsStatusEnum statusEnum : values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum;
            }
        }
        return null;
    }
}
