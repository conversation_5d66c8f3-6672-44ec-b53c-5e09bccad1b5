package com.cashop.aftersale.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 金额工具类
 */
public class MoneyUtils {

    /**
     * 默认精度
     */
    public static final int DEFAULT_SCALE = 2;

    /**
     * 默认舍入模式
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;

    /**
     * 金额相加
     */
    public static BigDecimal add(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) amount1 = BigDecimal.ZERO;
        if (amount2 == null) amount2 = BigDecimal.ZERO;
        return amount1.add(amount2).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 金额相减
     */
    public static BigDecimal subtract(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) amount1 = BigDecimal.ZERO;
        if (amount2 == null) amount2 = BigDecimal.ZERO;
        return amount1.subtract(amount2).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 金额相乘
     */
    public static BigDecimal multiply(BigDecimal amount, BigDecimal multiplier) {
        if (amount == null) amount = BigDecimal.ZERO;
        if (multiplier == null) multiplier = BigDecimal.ZERO;
        return amount.multiply(multiplier).setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 金额相除
     */
    public static BigDecimal divide(BigDecimal amount, BigDecimal divisor) {
        if (amount == null) amount = BigDecimal.ZERO;
        if (divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("除数不能为空或零");
        }
        return amount.divide(divisor, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 格式化金额显示
     */
    public static String formatAmount(BigDecimal amount, String currencyCode) {
        if (amount == null) {
            amount = BigDecimal.ZERO;
        }
        return currencyCode + " " + amount.setScale(DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }

    /**
     * 判断金额是否为零
     */
    public static boolean isZero(BigDecimal amount) {
        return amount == null || amount.compareTo(BigDecimal.ZERO) == 0;
    }

    /**
     * 判断金额是否为正数
     */
    public static boolean isPositive(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 判断金额是否为负数
     */
    public static boolean isNegative(BigDecimal amount) {
        return amount != null && amount.compareTo(BigDecimal.ZERO) < 0;
    }

    /**
     * 获取较大值
     */
    public static BigDecimal max(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) return amount2;
        if (amount2 == null) return amount1;
        return amount1.max(amount2);
    }

    /**
     * 获取较小值
     */
    public static BigDecimal min(BigDecimal amount1, BigDecimal amount2) {
        if (amount1 == null) return amount2;
        if (amount2 == null) return amount1;
        return amount1.min(amount2);
    }
}
