package com.cashop.aftersale.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 售后类型枚举
 */
@Getter
@AllArgsConstructor
public enum AsTypeEnum {

    /**
     * 发货前退款
     */
    PRE_DELIVERY_REFUND(1, "发货前退款"),

    /**
     * 发货后退款
     */
    POST_DELIVERY_REFUND(2, "发货后退款"),

    /**
     * 发货后退货退款
     */
    POST_DELIVERY_RETURN_REFUND(3, "发货后退货退款");

    private final Integer code;
    private final String desc;

    public static AsTypeEnum getByCode(Integer code) {
        for (AsTypeEnum typeEnum : values()) {
            if (typeEnum.getCode().equals(code)) {
                return typeEnum;
            }
        }
        return null;
    }
}
