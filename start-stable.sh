#!/bin/bash

# 售后系统启动脚本 - stable环境
# 使用Apollo配置中心

echo "启动售后系统 - stable环境"
echo "Apollo配置: cluster=stable, env=LPT"

# 设置JVM参数
export JAVA_OPTS="-Xms512m -Xmx1024m -XX:+UseG1GC"

# Apollo配置参数
export APOLLO_OPTS="-javaagent:/Users/<USER>/Documents/hades-agent-dist/hades-agent-dist/hades-agent.jar"
export APOLLO_OPTS="$APOLLO_OPTS -Dhades.env.identity=stable"
export APOLLO_OPTS="$APOLLO_OPTS -Denv=LPT"
export APOLLO_OPTS="$APOLLO_OPTS -Dapollo.cluster=stable"
export APOLLO_OPTS="$APOLLO_OPTS -Dspring.profiles.active=stable"

# 应用配置
export APP_OPTS="-Dapp.id=cashop-aftersale"
export APP_OPTS="$APP_OPTS -Dapollo.meta=http://apollo.castable.hk:8080"

# 启动应用
cd /Users/<USER>/Documents/cashop/cashop-aftersale/cashop-aftersale-web

echo "使用参数: $JAVA_OPTS $APOLLO_OPTS $APP_OPTS"
echo "启动中..."

mvn spring-boot:run -Dspring-boot.run.jvmArguments="$JAVA_OPTS $APOLLO_OPTS $APP_OPTS"
