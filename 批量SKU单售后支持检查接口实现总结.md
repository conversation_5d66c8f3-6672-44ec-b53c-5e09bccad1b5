# 批量SKU单售后支持检查接口实现总结

## 1. 需求变更说明

根据第三方系统的实际需求，对原有的SKU售后支持检查接口进行了重新设计：

### 1.1 主要变更
- **批量查询**: 支持一次请求检查多个SKU单的售后支持状态
- **去除SKU ID**: 不需要传入skuId，因为子订单号已经能确定SKU信息
- **原因说明**: 当不支持申请售后时，提供详细的原因说明

### 1.2 业务逻辑
- 如果已经申请过售后了，返回有售后状态
- 当有在途售后的时候，告知不能继续申请，并提供原因
- 当SKU单没有在途售后，但已经全部金额退完的时候，也不能支持申请售后，并说明原因

## 2. 接口设计

### 2.1 请求参数
```json
{
  "subOrderNos": [                 // 子订单号列表 (必填)
    "SO202312010001",
    "SO202312010002",
    "SO202312010003"
  ],
  "userId": 789                    // 用户ID (可选，用于权限验证)
}
```

### 2.2 响应结果
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [                     // 每个SKU单对应一个结果项
      {
        "subOrderNo": "SO202312010001",
        "supportAftersale": true,  // 是否支持申请售后
        "supportStatus": 1,        // 售后支持状态码
        "statusMessage": "支持申请售后",
        "unsupportedReason": null, // 不支持原因(仅当supportAftersale=false时有值)
        "productInfo": { /* SKU商品信息 */ },
        "aftersaleStatistics": { /* 售后统计信息 */ },
        "ongoingAftersales": [ /* 在途售后单列表 */ ]
      },
      {
        "subOrderNo": "SO202312010002",
        "supportAftersale": false,
        "supportStatus": 3,
        "statusMessage": "有在途售后申请，暂不支持继续申请",
        "unsupportedReason": "该SKU单当前有在途的售后申请，请等待处理完成后再申请",
        // ... 其他字段
      }
    ]
  }
}
```

## 3. 核心实现

### 3.1 数据结构变更

#### SkuAftersaleSupportRequest.java
```java
@Data
public class SkuAftersaleSupportRequest {
    private List<String> subOrderNos;  // 批量子订单号
    private Long userId;               // 用户ID
}
```

#### SkuAftersaleSupportVO.java
```java
@Data
@Builder
public class SkuAftersaleSupportVO {
    private List<SkuAftersaleSupportItem> items;  // 批量结果列表
    
    @Data
    @Builder
    public static class SkuAftersaleSupportItem {
        private String subOrderNo;          // 子订单号
        private Boolean supportAftersale;   // 是否支持申请售后
        private Integer supportStatus;      // 状态码
        private String statusMessage;       // 状态描述
        private String unsupportedReason;   // 不支持原因说明
        private ProductInfo productInfo;    // 商品信息
        private AftersaleStatistics aftersaleStatistics;  // 售后统计
        private List<OngoingAftersaleInfo> ongoingAftersales;  // 在途售后单
    }
}
```

### 3.2 业务逻辑实现

#### 批量处理逻辑
```java
@Override
public SkuAftersaleSupportVO checkSkuAftersaleSupport(SkuAftersaleSupportRequest request) {
    List<SkuAftersaleSupportVO.SkuAftersaleSupportItem> items = new ArrayList<>();
    
    for (String subOrderNo : request.getSubOrderNos()) {
        try {
            SkuAftersaleSupportVO.SkuAftersaleSupportItem item = 
                checkSingleSkuAftersaleSupport(subOrderNo, request.getUserId());
            items.add(item);
        } catch (Exception e) {
            items.add(buildErrorItem(subOrderNo, e.getMessage()));
        }
    }
    
    return SkuAftersaleSupportVO.builder().items(items).build();
}
```

#### 单个SKU单检查逻辑
```java
private SkuAftersaleSupportVO.SkuAftersaleSupportItem checkSingleSkuAftersaleSupport(String subOrderNo, Long userId) {
    // 1. 查询子订单商品信息（一个子订单对应一个SKU）
    List<AsProduct> products = asProductMapper.selectBySubOrderNo(subOrderNo);
    if (CollectionUtils.isEmpty(products)) {
        return buildNotFoundItem(subOrderNo);
    }
    
    AsProduct product = products.get(0);  // 取第一个商品
    
    // 2. 查询售后记录和统计信息
    List<AsOrder> aftersaleOrders = asOrderMapper.selectBySubOrderNo(subOrderNo);
    SkuAftersaleSupportVO.AftersaleStatistics statistics = calculateAftersaleStatistics(product, aftersaleOrders);
    List<AsOrder> ongoingOrders = filterOngoingAftersales(aftersaleOrders);
    
    // 3. 判断支持状态
    Integer supportStatus = determineSupportStatus(product, statistics, ongoingOrders);
    
    // 4. 构建响应
    return buildAftersaleSupportItem(subOrderNo, product, statistics, ongoingOrders, supportStatus);
}
```

### 3.3 不支持原因说明

#### 原因常量定义
```java
public static class UnsupportedReason {
    public static final String HAS_ONGOING_AFTERSALE = "该SKU单当前有在途的售后申请，请等待处理完成后再申请";
    public static final String FULLY_REFUNDED = "该SKU单的商品已全部退款完成，无法再次申请售后";
    public static final String PRODUCT_NOT_FOUND = "未找到该子订单的商品信息，请确认订单号是否正确";
    public static final String SYSTEM_ERROR = "系统处理异常，请稍后重试或联系客服";
    // ... 其他原因
}
```

#### 原因判断逻辑
```java
private String getUnsupportedReason(Integer supportStatus) {
    if (AftersaleApiConstants.SupportStatus.HAS_ONGOING_AFTERSALE.equals(supportStatus)) {
        return AftersaleApiConstants.UnsupportedReason.HAS_ONGOING_AFTERSALE;
    } else if (AftersaleApiConstants.SupportStatus.FULLY_REFUNDED.equals(supportStatus)) {
        return AftersaleApiConstants.UnsupportedReason.FULLY_REFUNDED;
    } else if (AftersaleApiConstants.SupportStatus.SYSTEM_ERROR.equals(supportStatus)) {
        return AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR;
    }
    // ... 其他状态处理
    return null;
}
```

## 4. 状态码和原因说明

| 状态码 | 含义 | supportAftersale | unsupportedReason |
|--------|------|------------------|-------------------|
| 1 | 支持申请售后 | true | null |
| 2 | 已有售后记录 | true | null |
| 3 | 有在途售后申请 | false | "该SKU单当前有在途的售后申请，请等待处理完成后再申请" |
| 4 | 已全部退完 | false | "该SKU单的商品已全部退款完成，无法再次申请售后" |
| 5 | 系统异常 | false | "系统处理异常，请稍后重试或联系客服" |

## 5. Feign客户端降级处理

### 5.1 批量降级逻辑
```java
@Override
public Result<SkuAftersaleSupportVO> checkSkuAftersaleSupport(SkuAftersaleSupportRequest request) {
    // 为每个子订单返回系统异常状态
    List<SkuAftersaleSupportVO.SkuAftersaleSupportItem> fallbackItems = new ArrayList<>();
    
    if (request.getSubOrderNos() != null) {
        for (String subOrderNo : request.getSubOrderNos()) {
            SkuAftersaleSupportVO.SkuAftersaleSupportItem item = 
                SkuAftersaleSupportVO.SkuAftersaleSupportItem.builder()
                    .subOrderNo(subOrderNo)
                    .supportAftersale(false)
                    .supportStatus(AftersaleApiConstants.SupportStatus.SYSTEM_ERROR)
                    .statusMessage(AftersaleApiConstants.SupportStatusMessage.SERVICE_UNAVAILABLE)
                    .unsupportedReason(AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR)
                    .build();
            fallbackItems.add(item);
        }
    }
    
    return Result.success(SkuAftersaleSupportVO.builder().items(fallbackItems).build());
}
```

## 6. 前端集成建议

### 6.1 批量调用
```javascript
// 批量检查售后支持状态
async function batchCheckAftersaleSupport(subOrderNos) {
    const response = await fetch('/api/third-party/aftersale/support/check', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            subOrderNos: subOrderNos,
            userId: getCurrentUserId()
        })
    });
    
    const result = await response.json();
    
    if (result.code === 200 && result.data && result.data.items) {
        result.data.items.forEach(item => {
            handleAftersaleStatus(item.subOrderNo, item);
        });
    }
}
```

### 6.2 状态处理
```javascript
function handleAftersaleStatus(subOrderNo, item) {
    switch (item.supportStatus) {
        case 1: // 支持申请售后
            showAftersaleButton(subOrderNo);
            break;
        case 2: // 已有售后记录
        case 3: // 有在途售后
            showAftersaleProgressButton(subOrderNo, item.ongoingAftersales);
            break;
        case 4: // 已全部退完
            hideAftersaleButton(subOrderNo);
            break;
        case 5: // 系统异常
            showErrorMessage(subOrderNo, item.unsupportedReason);
            break;
    }
}
```

## 7. 性能优化建议

### 7.1 批量查询优化
- 限制单次请求的子订单数量（建议不超过50个）
- 对频繁查询的数据添加缓存
- 使用异步处理提高响应速度

### 7.2 数据库优化
- 为子订单号字段添加索引
- 优化售后记录查询SQL
- 考虑使用读写分离

## 8. 监控和告警

### 8.1 关键指标
- 接口调用量和响应时间
- 批量请求的平均子订单数量
- 各状态码的分布情况
- 降级处理的触发频率

### 8.2 异常告警
- 接口响应时间超过阈值
- 错误率超过5%
- 降级处理频繁触发

## 9. 版本信息

- **版本**: v2.0.0
- **更新时间**: 2024年12月
- **主要变更**: 
  - 支持批量SKU单查询
  - 移除SKU ID参数
  - 添加不支持原因说明
  - 优化响应结构

## 10. 后续扩展

### 10.1 功能扩展
- 支持按用户ID批量查询
- 添加售后申请时间限制检查
- 支持商品类别的售后规则配置

### 10.2 性能扩展
- 引入分布式缓存
- 实现异步批量处理
- 添加限流和熔断机制
