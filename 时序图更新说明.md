# 售后系统时序图更新说明

## 更新概述

为了体现新增的服务费退款功能，我们对售后系统的时序图进行了全面更新，主要变化包括：

1. **在创建售后订单时增加服务费计算步骤**
2. **在退款处理时分别处理商品退款和服务费退款**
3. **增加退款明细创建和状态管理步骤**
4. **完善运费退款明细处理**

## 主要变化详情

### 1. 发货前申请退款流程

#### 原有流程问题：
- 只有简单的"计算退款金额"和"调用退款接口"
- 没有体现服务费的计算和退款
- 缺少退款明细的创建和管理

#### 更新后的流程：
```
7. 计算申请金额和服务费
8. 创建售后订单(包含服务费信息)
...
16. 计算商品退款金额(确认件数×单价)
17. 计算服务费退款金额(确认件数×单件服务费)
18. 创建商品退款明细
19. 创建服务费退款明细
20. 调用商品退款接口
21. 返回商品退款结果
22. 调用服务费退款接口
23. 返回服务费退款结果
24. 更新退款明细状态为成功
25. 更新售后订单为退款完成
26. 发送完成通知给用户
```

### 2. 发货后申请退款流程

#### 更新后的流程：
```
5. 计算申请金额和服务费
6. 创建售后订单(包含服务费信息)
...
14. 计算商品退款金额(批准件数×单价)
15. 计算服务费退款金额(批准件数×单件服务费)
16. 创建商品退款明细
17. 创建服务费退款明细
18. 调用商品退款接口
19. 返回商品退款结果
20. 调用服务费退款接口
21. 返回服务费退款结果
22. 更新退款明细状态为成功
23. 更新售后订单为退款完成
24. 发送完成通知给用户
```

### 3. 发货后申请退货退款流程

#### 更新后的流程：
```
5. 计算申请金额和服务费
6. 创建售后订单(包含服务费信息)
...
31. 计算商品退款金额(实际件数×单价)
32. 计算服务费退款金额(实际件数×单件服务费)
33. 创建商品退款明细
34. 创建服务费退款明细
35. 更新状态为退款中
36. 调用商品退款接口
37. 返回商品退款结果
38. 调用服务费退款接口
39. 返回服务费退款结果
40. 更新退款明细状态为成功
41. 更新售后订单为退款完成
42. 发送完成通知给用户
```

#### 运费退款处理：
```
45. 更新状态为申请被拒
46. 创建运费退款明细
47. 退还运费给用户
48. 更新运费退款明细状态
49. 发送拒绝通知给用户
```

## 关键改进点

### 1. 服务费计算集成
- **时机**: 在创建售后订单时就计算并记录服务费信息
- **计算方式**: 单件服务费 × 申请件数 = 订单服务费总额
- **存储**: 在as_order表中记录unit_service_fee和order_service_fee

### 2. 分离式退款处理
- **商品退款**: 单独计算商品退款金额并创建明细
- **服务费退款**: 单独计算服务费退款金额并创建明细
- **运费退款**: 在特定场景下创建运费退款明细
- **状态同步**: 所有退款明细状态与主订单状态保持同步

### 3. 退款明细管理
- **明细创建**: 每种类型的退款都创建对应的明细记录
- **状态跟踪**: 从待退款→退款中→退款成功/失败的完整状态流转
- **详细记录**: 记录退款金额、数量、原因、交易ID等完整信息

### 4. 错误处理优化
- **退款失败**: 更新明细状态为失败，记录失败原因
- **部分成功**: 支持商品退款成功但服务费退款失败的场景
- **重试机制**: 为失败的退款明细提供重试处理能力

## 业务价值

### 1. 透明度提升
- 用户可以清楚看到每笔退款的详细构成
- 区分商品退款、服务费退款、运费退款
- 完整的退款状态跟踪

### 2. 财务管理
- 精确的退款金额计算和记录
- 便于财务对账和审计
- 支持不同类型退款的分别处理

### 3. 系统可靠性
- 分离式退款处理降低了单点失败风险
- 详细的状态管理便于问题排查
- 支持异常情况的恢复处理

### 4. 扩展性
- 退款明细表设计支持未来新的退款类型
- 状态管理机制可以扩展更复杂的业务流程
- 接口设计支持多维度的查询需求

## 技术实现要点

### 1. 事务管理
- 退款明细创建和主订单更新在同一事务中
- 支付系统调用失败时的回滚处理
- 分布式事务的一致性保证

### 2. 并发控制
- 防止重复退款的幂等性设计
- 退款状态更新的并发安全
- 乐观锁机制防止数据冲突

### 3. 监控告警
- 退款明细创建失败的告警
- 退款处理超时的监控
- 退款金额异常的检测

## 总结

通过这次时序图的更新，我们成功地将服务费退款功能完整地集成到了售后系统的业务流程中。新的时序图不仅体现了技术实现的细节，更重要的是展现了完整的业务逻辑和用户体验。

这些改进使得售后系统能够：
- 更精确地处理各种类型的退款
- 提供更透明的退款明细信息
- 支持更复杂的业务场景
- 具备更好的可维护性和扩展性

---
**文档版本**: v1.0  
**创建时间**: 2025-01-06  
**作者**: 开发团队
