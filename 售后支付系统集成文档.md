# 售后支付系统集成文档

## 1. 功能概述

本次集成为售后系统添加了完整的支付功能，支持用户在申请退货退款时支付运费。集成了支付系统的预支付接口，实现了从预支付到支付回调的完整流程。

## 2. 核心功能

### 2.1 预支付功能
- **接口路径**: `POST /api/aftersale/prepay`
- **功能描述**: 创建预支付订单，获取支付参数供前端唤起支付
- **支持支付方式**: 支付宝、微信、信用卡、二维码支付等

### 2.2 支付回调处理
- **接口路径**: `POST /api/aftersale/payment/callback`
- **功能描述**: 处理支付系统的异步回调通知
- **支持状态**: 支付成功、支付失败

### 2.3 业务流程集成
- 与售后申请流程无缝集成
- 支持一致性框架确保数据可靠性
- 完整的状态流转和日志记录

## 3. 技术架构

### 3.1 依赖集成
```xml
<dependency>
    <groupId>com.cashop.payment</groupId>
    <artifactId>cashop-payment-facade</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```

### 3.2 核心组件

#### 3.2.1 DTO类
- `PrePaymentRequest`: 预支付请求参数
- `PrePaymentResponse`: 预支付响应数据
- `QueryPaymentResponseDTO`: 支付系统回调响应参数（来自支付系统依赖）

#### 3.2.2 服务层
- `PaymentIntegrationService`: 支付集成服务接口
- `PaymentIntegrationServiceImpl`: 支付集成服务实现
- `AftersaleOrderService.createPrePayment()`: 预支付业务方法
- `AftersaleOrderService.handlePaymentCallback()`: 回调处理业务方法

#### 3.2.3 控制器层
- `AftersaleController.createPrePayment()`: 预支付接口
- `AftersaleController.paymentCallback()`: 回调接口

## 4. API接口详情

### 4.1 创建预支付订单

**请求示例:**
```json
POST /api/aftersale/prepay
Content-Type: application/json

{
    "serviceNumber": "AS202501220001",
    "userId": 12345,
    "amount": 15.50,
    "currencyCode": "USD",
    "paymentMethod": "ALIPAY",
    "description": "售后退货运费支付",
    "callbackUrl": "https://aftersale.example.com/api/aftersale/payment/callback",
    "returnUrl": "https://app.example.com/aftersale/payment/result"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "prepayId": "PREPAY_abc123def456",
        "transactionId": "TXN_xyz789uvw012",
        "paymentParams": "{\"app_id\":\"test\",\"prepay_id\":\"PREPAY_abc123def456\"}",
        "qrCode": null,
        "paymentUrl": null,
        "expireTime": 1737540000,
        "paymentMethod": "ALIPAY",
        "orderStatus": "CREATED"
    }
}
```

### 4.2 支付回调处理

**请求示例:**
```json
POST /api/aftersale/payment/callback
Content-Type: application/json

{
    "paymentTradeNo": "PAY20240726001",
    "businessOrderNo": "AS202501220001",
    "status": "SUCCESS",
    "paidAmount": 15.50,
    "basePaidAmount": 120.00,
    "currency": "USD",
    "baseCurrency": "HKD",
    "finishTime": "2024-07-26T10:35:00",
    "thirdPartyTradeNo": "TP20240726001",
    "extInfo": "{\"channel\":\"AEON_PAY\",\"method\":\"QR_CODE\"}"
}
```

**响应示例:**
```json
{
    "code": 200,
    "message": "回调处理成功",
    "data": "回调处理成功"
}
```

## 5. 业务流程

### 5.1 支付流程
1. 用户申请退货退款 → 订单状态变为"待支付物流费用"
2. 用户点击支付 → 调用预支付接口
3. 前端获取支付参数 → 唤起支付客户端
4. 用户完成支付 → 支付系统异步回调
5. 售后系统处理回调 → 更新订单状态为"已支付，待审核"
6. 提交给供货平台处理

### 5.2 状态流转
```
申请中(1) → 待支付物流费用(2) → 已支付，待审核(3) → 供货平台处理中(4) → 已完成(5)
```

## 6. 错误处理

### 6.1 业务验证
- 订单状态验证：只有"待支付物流费用"状态的订单才能支付
- 用户权限验证：只有订单所属用户才能支付
- 金额验证：支付金额必须与订单物流费用一致
- 币种验证：支付币种必须与订单币种一致

### 6.2 异常情况
- 支付失败：记录失败原因，订单状态保持不变，用户可重新支付
- 回调重复：通过订单状态判断，避免重复处理
- 网络异常：使用一致性框架确保数据最终一致性

## 7. 安全考虑

### 7.1 签名验证
- 支持回调签名验证（预留接口）
- 防止恶意回调攻击

### 7.2 幂等性
- 支付回调处理具有幂等性
- 避免重复处理导致的数据不一致

## 8. 监控和日志

### 8.1 关键日志
- 预支付订单创建日志
- 支付回调处理日志
- 业务状态变更日志
- 异常处理日志

### 8.2 业务监控
- 支付成功率监控
- 回调处理延迟监控
- 异常情况告警

## 9. 后续扩展

### 9.1 待实现功能
- 支付系统真实接口对接（目前为模拟实现）
- 签名验证逻辑完善
- 支付失败重试机制
- 支付超时处理

### 9.2 优化方向
- 支付参数缓存优化
- 回调处理性能优化
- 支付方式扩展
- 多币种支持完善

## 10. 部署说明

### 10.1 配置要求
- 确保Apollo配置中心包含支付相关配置
- 配置支付系统回调地址白名单
- 设置合适的支付超时时间

### 10.2 依赖服务
- 支付系统服务正常运行
- 一致性框架服务可用
- Apollo配置中心可用

---

**注意**: 当前实现为模拟版本，实际部署时需要替换为真实的支付系统接口调用。
