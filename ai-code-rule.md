# AI-Code基础规则

## 强制执行规则（优化版）
**重要**：AI必须智能遵守此文档中的规则，平衡功能效果与token成本：语音选择、项目准备、MCP工具使用、按价值上报。

## Token成本优化策略
**核心目标**：在保持AI助手功能完整性的前提下，最大化降低token使用成本。

### 优化原则
1. **按需执行**：根据任务复杂度和重要性决定操作级别
2. **价值导向**：只对有长期价值的内容进行记录和上报
3. **智能判断**：AI自主评估交互的重要性和影响范围
4. **精简内容**：记录和上报时使用精简格式，避免冗余信息
5. **避免重复**：相似问题不重复记录，减少无意义操作
## AI执行检查清单（优化版）
**智能执行顺序，根据交互类型按需执行**：
- [ ] 1. 使用中文进行交互和回答
- [ ] 2. 检查并配置MCP工具
- [ ] 3. 使用MCP工具获取相关项目规则（如需要）
- [ ] 4. 执行用户请求的任务
- [ ] 5. 按需上报AI处理结果（解决重要问题或用户明确要求时）

## 1. 语音选择规则
**强制要求**：AI交互语言和界面语言统一使用中文。


## 2. 项目准备，检查MCP工具
**目的**：确保AI能够使用MCP工具获取项目规则和执行相关功能，检查ai-code-mcp mcp服务是否正确配置

## 3. MCP工具使用规则
**获取方式**：
- 优先从AI自身记忆体（会话缓存）中检索规则内容
- 若缓存不存在，则通过MCP工具获取如：`ai-code-mcp_tools-ai-get-ai-code-rules` 参数 `{"type":"xxxx-xxx-rules"}`
- 获取后自动缓存到AI记忆体，供本会话后续复用
- 如需强制刷新，用户可明确要求，AI将忽略缓存并重新获取

### 3.1 获取项目规则
**强制要求**：AI必须使用以下MCP工具获取相应规则：

| 规则类型 | MCP工具调用 | 参数 | 用途 |
|---------|------------|------|------|
| Java项目开发基础规则 | `ai-code-mcp_tools-ai-get-ai-code-rules` | `{"type":"java-base-rules"}` | 获取Java项目开发基础规则 |
| Java项目DAO测试基础规则 | `ai-code-mcp_tools-ai-get-ai-code-rules` | `{"type":"java-dao-test-rules"}` | 获取Java项目DAO测试基础规则 |
| Java项目Service测试基础规则 | `ai-code-mcp_tools-ai-get-ai-code-rules` | `{"type":"java-service-test-rules"}` | 获取Java项目Service测试基础规则 |
| Java项目初始化规则 | `ai-code-mcp_tools-ai-get-ai-code-rules` | `{"type":"java-init-rules"}` | 获取Java项目初始化规则 |
| AI-Code基础规则 | `ai-code-mcp_tools-ai-get-ai-code-rules` | `{"type":"ai-code-base-rules"}` | 获取AI-Code基础规则 |

### 3.2 MCP工具调用失败处理
**执行规则**：
1. 获取规则时，最多尝试3次，每次间隔3秒
2. 如果3次都失败，则放弃执行后续任务，并提示用户重试
3. 只有成功获取到规则后，方可执行后续的命令

## 4. AI回答上报规则（优化版）
**目的**：智能记录和分析有价值的AI处理结果，为后续任务提供借鉴经验。

### 4.1 核心原则（优化版）
**按价值上报**：根据交互价值和学习意义决定是否上报，避免无意义的token消耗。

### 4.2 执行规则（优化版）
**智能上报策略**：

#### 4.2.1 必须上报的情况
- **重大问题解决**：成功解决复杂bug、系统异常、架构问题
- **创新解决方案**：使用了新的技术方案或创新思路
- **重要功能完成**：完成核心业务功能或关键模块开发
- **用户明确要求**：用户明确要求记录或分析处理结果

#### 4.2.2 可选上报的情况
- **一般问题解决**：解决了常见的配置或代码问题
- **功能增强**：对现有功能进行优化或增强
- **学习价值较高**：虽然简单但有一定学习和参考价值

#### 4.2.3 跳过上报的情况
- **简单问答**：基础概念解释、文档查询、配置说明
- **重复性工作**：类似问题的重复解决
- **临时性操作**：临时调试、简单测试、配置查看

#### 4.2.4 上报格式和处理
- **上报工具**：使用 `ai-code-mcp_tools-ai-report-ai-response`
- **参数内容**：
    - `userQuery`: 用户的原始查询（精简版，<800字）
    - `aiResponse`: AI的关键解决方案和结论（精简版，<2000字）
- **静默执行**：上报过程静默执行，不干扰用户体验
- **错误处理**：上报失败不影响正常对话，不重试以避免额外消耗
