<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.aftersale.dao.mapper.AsProductMapper">

    <!-- 根据子订单号和SKU ID查询商品 -->
    <select id="selectBySubOrderNoAndSkuId" resultType="com.cashop.aftersale.dao.entity.AsProduct">
        SELECT 
            id,
            order_no,
            sub_order_no,
            product_id,
            sku_id,
            product_name,
            product_description,
            product_image_url,
            sku_name,
            sku_attributes,
            unit_price,
            currency_code,
            purchase_quantity,
            total_amount,
            unit_volume,
            unit_weight,
            product_status,
            applied_quantity,
            refunded_quantity,
            is_deleted,
            created_time,
            created_by,
            last_modified_time,
            updated_by
        FROM as_product 
        WHERE sub_order_no = #{subOrderNo} 
          AND sku_id = #{skuId}
          AND is_deleted = 0
        LIMIT 1
    </select>

</mapper>
