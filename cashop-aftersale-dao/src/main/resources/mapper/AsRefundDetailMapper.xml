<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cashop.aftersale.dao.mapper.AsRefundDetailMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="com.cashop.aftersale.dao.entity.AsRefundDetail">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="as_order_id" property="asOrderId" jdbcType="BIGINT"/>
        <result column="service_number" property="serviceNumber" jdbcType="VARCHAR"/>
        <result column="refund_type" property="refundType" jdbcType="TINYINT"/>
        <result column="refund_amount" property="refundAmount" jdbcType="DECIMAL"/>
        <result column="currency_code" property="currencyCode" jdbcType="VARCHAR"/>
        <result column="refund_reason" property="refundReason" jdbcType="VARCHAR"/>
        <result column="refund_description" property="refundDescription" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="BIGINT"/>
        <result column="sku_id" property="skuId" jdbcType="BIGINT"/>
        <result column="product_name" property="productName" jdbcType="VARCHAR"/>
        <result column="refund_quantity" property="refundQuantity" jdbcType="INTEGER"/>
        <result column="unit_price" property="unitPrice" jdbcType="DECIMAL"/>
        <result column="service_fee_quantity" property="serviceFeeQuantity" jdbcType="INTEGER"/>
        <result column="unit_service_fee" property="unitServiceFee" jdbcType="DECIMAL"/>
        <result column="refund_status" property="refundStatus" jdbcType="TINYINT"/>
        <result column="refund_transaction_id" property="refundTransactionId" jdbcType="VARCHAR"/>
        <result column="refund_time" property="refundTime" jdbcType="TIMESTAMP"/>
        <result column="is_deleted" property="isDeleted" jdbcType="TINYINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="last_modified_time" property="lastModifiedTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, as_order_id, service_number, refund_type, refund_amount, currency_code,
        refund_reason, refund_description, product_id, sku_id, product_name,
        refund_quantity, unit_price, service_fee_quantity, unit_service_fee,
        refund_status, refund_transaction_id, refund_time, is_deleted,
        created_time, created_by, last_modified_time, updated_by
    </sql>

    <!-- 根据售后订单ID查询退款明细列表 -->
    <select id="selectByAsOrderId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 根据售后单号查询退款明细列表 -->
    <select id="selectByServiceNumber" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE service_number = #{serviceNumber}
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 根据退款类型查询退款明细 -->
    <select id="selectByRefundType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_type = #{refundType}
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 查询指定售后订单的退款总金额 -->
    <select id="selectTotalRefundAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(refund_amount), 0)
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_status = 3
        AND is_deleted = 0
    </select>

    <!-- 查询指定售后订单指定类型的退款总金额 -->
    <select id="selectRefundAmountByType" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(refund_amount), 0)
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_type = #{refundType}
        AND refund_status = 3
        AND is_deleted = 0
    </select>

    <!-- 批量插入退款明细 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO as_refund_detail (
            as_order_id, service_number, refund_type, refund_amount, currency_code,
            refund_reason, refund_description, product_id, sku_id, product_name,
            refund_quantity, unit_price, service_fee_quantity, unit_service_fee,
            refund_status, created_by
        ) VALUES
        <foreach collection="details" item="item" separator=",">
            (
                #{item.asOrderId}, #{item.serviceNumber}, #{item.refundType},
                #{item.refundAmount}, #{item.currencyCode}, #{item.refundReason},
                #{item.refundDescription}, #{item.productId}, #{item.skuId},
                #{item.productName}, #{item.refundQuantity}, #{item.unitPrice},
                #{item.serviceFeeQuantity}, #{item.unitServiceFee},
                #{item.refundStatus}, #{item.createdBy}
            )
        </foreach>
    </insert>

    <!-- 更新退款状态 -->
    <update id="updateRefundStatus">
        UPDATE as_refund_detail
        SET refund_status = #{refundStatus},
            refund_transaction_id = #{refundTransactionId},
            refund_time = CASE WHEN #{refundStatus} = 3 THEN NOW() ELSE refund_time END,
            last_modified_time = NOW()
        WHERE id = #{id}
        AND is_deleted = 0
    </update>

    <!-- 查询待退款的明细 -->
    <select id="selectPendingRefunds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE refund_status = 1
        AND is_deleted = 0
        ORDER BY created_time ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询退款失败的明细 -->
    <select id="selectFailedRefunds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE refund_status = 4
        AND is_deleted = 0
        ORDER BY created_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 统计退款明细数量 -->
    <select id="countByAsOrderId" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND is_deleted = 0
    </select>

    <!-- 根据商品ID查询退款明细 -->
    <select id="selectByProductId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE product_id = #{productId}
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 查询服务费退款明细 -->
    <select id="selectServiceFeeRefunds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_type = 2
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 查询商品退款明细 -->
    <select id="selectProductRefunds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_type = 1
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 查询运费退款明细 -->
    <select id="selectShippingFeeRefunds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM as_refund_detail
        WHERE as_order_id = #{asOrderId}
        AND refund_type = 3
        AND is_deleted = 0
        ORDER BY created_time DESC
    </select>

</mapper>
