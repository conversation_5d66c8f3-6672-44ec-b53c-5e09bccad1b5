package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 子订单商品表实体
 * 数据访问层 - 商品数据实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("as_product")
public class AsProduct {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 主订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 子订单号
     */
    @TableField("sub_order_no")
    private String subOrderNo;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * SKU ID
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 商品描述
     */
    @TableField("product_description")
    private String productDescription;

    /**
     * 商品图片URL
     */
    @TableField("product_image_url")
    private String productImageUrl;

    /**
     * SKU名称
     */
    @TableField("sku_name")
    private String skuName;

    /**
     * 规格属性(JSON格式)
     */
    @TableField("sku_attributes")
    private String skuAttributes;

    /**
     * 购买时单价
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 商品售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 币种代码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 购买数量
     */
    @TableField("purchase_quantity")
    private Integer purchaseQuantity;

    /**
     * 商品总金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 单件商品体积(立方米)
     */
    @TableField("unit_volume")
    private BigDecimal unitVolume;

    /**
     * 单件商品重量(公斤)
     */
    @TableField("unit_weight")
    private BigDecimal unitWeight;

    /**
     * 商品状态:1-正常,2-已下架,3-已删除
     */
    @TableField("product_status")
    private Integer productStatus;

    /**
     * 已申请售后数量
     */
    @TableField("applied_quantity")
    private Integer appliedQuantity;

    /**
     * 已退款数量
     */
    @TableField("refunded_quantity")
    private Integer refundedQuantity;

    /**
     * 通用审计字段
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "last_modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModifiedTime;

    @TableField("updated_by")
    private String updatedBy;
}
