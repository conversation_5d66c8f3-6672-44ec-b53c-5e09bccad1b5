package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后退款明细表实体
 * 数据访问层 - 退款明细数据实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("as_refund_detail")
public class AsRefundDetail {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后订单ID
     */
    @TableField("as_order_id")
    private Long asOrderId;

    /**
     * 售后单号
     */
    @TableField("service_number")
    private String serviceNumber;

    /**
     * 退款类型:1-商品退款,2-增值服务费退款,3-国内段运费退款,4-退货服务费退款
     */
    @TableField("refund_type")
    private Integer refundType;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 币种代码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 退款原因
     */
    @TableField("refund_reason")
    private String refundReason;

    /**
     * 退款明细描述
     */
    @TableField("refund_description")
    private String refundDescription;

    /**
     * 商品ID(as_product表的id)
     */
    @TableField("product_id")
    private Long productId;

    /**
     * SKU ID
     */
    @TableField("sku_id")
    private Long skuId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 退款件数
     */
    @TableField("refund_quantity")
    private Integer refundQuantity;

    /**
     * 单价
     */
    @TableField("unit_price")
    private BigDecimal unitPrice;

    /**
     * 服务费对应件数
     */
    @TableField("service_fee_quantity")
    private Integer serviceFeeQuantity;

    /**
     * 单件服务费
     */
    @TableField("unit_service_fee")
    private BigDecimal unitServiceFee;

    /**
     * 退款状态:1-待退款,2-退款中,3-退款成功,4-退款失败
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 退款交易ID
     */
    @TableField("refund_transaction_id")
    private String refundTransactionId;

    /**
     * 退款完成时间
     */
    @TableField("refund_time")
    private LocalDateTime refundTime;

    /**
     * 国内段运费信息（仅退款类型为3时使用）
     */
    @TableField("domestic_shipping_info")
    private String domesticShippingInfo;

    /**
     * 通用审计字段
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "last_modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModifiedTime;

    @TableField("updated_by")
    private String updatedBy;
}
