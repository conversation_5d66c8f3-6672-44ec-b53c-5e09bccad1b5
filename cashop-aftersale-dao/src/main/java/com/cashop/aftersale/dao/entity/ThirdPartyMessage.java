package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 第三方消息记录表实体
 * 数据访问层 - 消息记录数据实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("third_party_message")
public class ThirdPartyMessage {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * 业务类型:1-售后申请,2-售后结果,3-退款结果,4-状态变更
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 业务ID(售后单号等)
     */
    @TableField("business_id")
    private String businessId;

    /**
     * 第三方名称
     */
    @TableField("third_party_name")
    private String thirdPartyName;

    /**
     * 第三方ID
     */
    @TableField("third_party_id")
    private String thirdPartyId;

    /**
     * 原始消息内容
     */
    @TableField("raw_message")
    private String rawMessage;

    /**
     * 消息摘要
     */
    @TableField("message_summary")
    private String messageSummary;

    /**
     * 消费状态:0-未消费,1-消费成功,2-消费失败
     */
    @TableField("consume_status")
    private Integer consumeStatus;

    /**
     * 消费时间
     */
    @TableField("consume_time")
    private LocalDateTime consumeTime;

    /**
     * 消费结果
     */
    @TableField("consume_result")
    private String consumeResult;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 重试次数
     */
    @TableField("retry_times")
    private Integer retryTimes;

    /**
     * 通用审计字段
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "last_modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModifiedTime;

    @TableField("updated_by")
    private String updatedBy;
}
