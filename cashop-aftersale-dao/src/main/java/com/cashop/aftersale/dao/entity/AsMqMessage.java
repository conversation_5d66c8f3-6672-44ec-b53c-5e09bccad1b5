package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * MQ消息通知记录表实体
 * DAO层 - 数据库实体对象
 */
@Data
@TableName("as_mq_message")
public class AsMqMessage {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后订单ID
     */
    @TableField("as_order_id")
    private Long asOrderId;

    /**
     * 售后单号
     */
    @TableField("service_number")
    private String serviceNumber;

    /**
     * 消息类型:1-售后创建,2-售后退款,3-售后拒绝,4-售后关闭,5-仓库发货通知,6-物流信息已提交
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * MQ主题
     */
    @TableField("message_topic")
    private String messageTopic;

    /**
     * MQ标签
     */
    @TableField("message_tag")
    private String messageTag;

    /**
     * 消息内容(JSON格式)
     */
    @TableField("message_content")
    private String messageContent;

    /**
     * 发送状态:0-待发送,1-发送成功,2-发送失败
     */
    @TableField("send_status")
    private Integer sendStatus;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private LocalDateTime sendTime;

    /**
     * 发送结果
     */
    @TableField("send_result")
    private String sendResult;

    /**
     * 错误信息
     */
    @TableField("error_msg")
    private String errorMsg;

    /**
     * 重试次数
     */
    @TableField("retry_times")
    private Integer retryTimes;

    /**
     * 目标系统:warehouse,order,payment等
     */
    @TableField("target_system")
    private String targetSystem;

    /**
     * 是否删除:0-有效,1-无效
     */
    @TableField("is_deleted")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @TableField("created_time")
    private LocalDateTime createdTime;

    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 最近一次修改时间
     */
    @TableField("last_modified_time")
    private LocalDateTime lastModifiedTime;

    /**
     * 修改人
     */
    @TableField("updated_by")
    private String updatedBy;
}
