package com.cashop.aftersale.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cashop.aftersale.dao.entity.AsProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 子订单商品Mapper接口
 * 数据访问层 - 商品数据操作接口
 */
@Mapper
public interface AsProductMapper extends BaseMapper<AsProduct> {

    /**
     * 根据子订单号查询商品列表
     */
    List<AsProduct> selectBySubOrderNo(@Param("subOrderNo") String subOrderNo);

    /**
     * 根据订单号查询商品列表
     */
    List<AsProduct> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 查询可申请售后的商品
     */
    List<AsProduct> selectRefundableProducts(@Param("subOrderNo") String subOrderNo);

    /**
     * 批量更新已申请数量
     */
    int batchUpdateAppliedQuantity(@Param("products") List<AsProduct> products);

    /**
     * 更新已申请数量
     */
    int updateAppliedQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);

    /**
     * 更新已退款数量
     */
    int updateRefundedQuantity(@Param("id") Long id, @Param("quantity") Integer quantity);

    /**
     * 根据商品ID和SKU ID查询
     */
    AsProduct selectByProductIdAndSkuId(@Param("productId") Long productId, @Param("skuId") Long skuId);

    /**
     * 统计商品售后情况
     */
    List<AsProduct> selectProductAftersaleStatistics(@Param("productIds") List<Long> productIds);

    /**
     * 根据子订单号和SKU ID查询商品
     */
    AsProduct selectBySubOrderNoAndSkuId(@Param("subOrderNo") String subOrderNo, @Param("skuId") Long skuId);
}
