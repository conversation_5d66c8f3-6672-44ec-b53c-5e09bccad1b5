package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 售后订单表实体
 * 数据访问层 - 数据库实体定义
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("as_order")
public class AsOrder {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后单号
     */
    @TableField("service_number")
    private String serviceNumber;

    /**
     * 主订单号
     */
    @TableField("order_no")
    private String orderNo;

    /**
     * 子订单号
     */
    @TableField("sub_order_no")
    private String subOrderNo;

    /**
     * Cashop二级单号
     */
    @TableField("cashop_order_no")
    private String cashopOrderNo;

    /**
     * SKU单号
     */
    @TableField("sku_order_no")
    private String skuOrderNo;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户角色:1-购买者,2-店主
     */
    @TableField("user_role")
    private Integer userRole;

    /**
     * 售后类型:1-发货前退款,2-发货后退款,3-发货后退货退款
     */
    @TableField("as_type")
    private Integer asType;

    /**
     * 申请原因
     */
    @TableField("apply_reason")
    private String applyReason;

    /**
     * 售后状态:1-申请中,2-待支付物流费用,3-已提交供货平台,4-供货平台处理中,5-已完成,6-已拒绝,7-超时撤销
     */
    @TableField("as_status")
    private Integer asStatus;

    /**
     * 申请退货总件数
     */
    @TableField("apply_quantity")
    private Integer applyQuantity;

    /**
     * 申请总金额
     */
    @TableField("apply_amount")
    private BigDecimal applyAmount;

    /**
     * 实付金额
     */
    @TableField("actual_paid_amount")
    private BigDecimal actualPaidAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 币种代码
     */
    @TableField("currency_code")
    private String currencyCode;

    /**
     * 退货总体积(立方米)
     */
    @TableField("return_volume")
    private BigDecimal returnVolume;

    /**
     * 退货总重量(公斤)
     */
    @TableField("return_weight")
    private BigDecimal returnWeight;

    /**
     * 第三方供货平台信息
     */
    @TableField("supplier_order_no")
    private String supplierOrderNo;

    @TableField("supplier_as_no")
    private String supplierAsNo;

    /**
     * 供货平台批准退货总件数
     */
    @TableField("approved_quantity")
    private Integer approvedQuantity;

    /**
     * 实际退款总件数
     */
    @TableField("actual_refund_quantity")
    private Integer actualRefundQuantity;

    /**
     * 实际退款总金额
     */
    @TableField("actual_refund_amount")
    private BigDecimal actualRefundAmount;

    /**
     * 货款退款金额（按件退）
     */
    @TableField("product_refund_amount")
    private BigDecimal productRefundAmount;

    /**
     * 增值服务费退款金额（按件退）
     */
    @TableField("value_added_service_refund_amount")
    private BigDecimal valueAddedServiceRefundAmount;

    /**
     * 退货服务费（仓库作业费 + 仓库退商家仓运费）
     */
    @TableField("return_service_fee")
    private BigDecimal returnServiceFee;

    /**
     * 退货服务费退款状态:0-未退款,1-已退款
     */
    @TableField("return_service_fee_refund_status")
    private Integer returnServiceFeeRefundStatus;

    /**
     * 运费相关字段
     */
    @TableField("shipping_fee")
    private BigDecimal shippingFee;

    @TableField("service_fee")
    private BigDecimal serviceFee;

    @TableField("total_logistics_fee")
    private BigDecimal totalLogisticsFee;

    @TableField("logistics_paid")
    private Integer logisticsPaid;

    @TableField("logistics_pay_time")
    private LocalDateTime logisticsPayTime;

    @TableField("logistics_expire_time")
    private LocalDateTime logisticsExpireTime;

    /**
     * 物流费用金额（用于支付回调）
     */
    @TableField("logistics_fee_amount")
    private BigDecimal logisticsFeeAmount;

    /**
     * 支付交易ID
     */
    @TableField("payment_transaction_id")
    private String paymentTransactionId;

    /**
     * 订单维度服务费(按件收取)
     */
    @TableField("order_service_fee")
    private BigDecimal orderServiceFee;

    @TableField("unit_service_fee")
    private BigDecimal unitServiceFee;

    /**
     * 申请人信息
     */
    @TableField("applicant_type")
    private Integer applicantType;

    @TableField("applicant_id")
    private Long applicantId;

    @TableField("applicant_name")
    private String applicantName;

    /**
     * 仓库物流信息
     */
    @TableField("warehouse_shipping_deadline")
    private LocalDateTime warehouseShippingDeadline;

    @TableField("warehouse_shipping_time")
    private LocalDateTime warehouseShippingTime;

    @TableField("warehouse_logistics_company")
    private String warehouseLogisticsCompany;

    @TableField("warehouse_tracking_number")
    private String warehouseTrackingNumber;

    @TableField("warehouse_logistics_info")
    private String warehouseLogisticsInfo;

    /**
     * 通用审计字段
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "last_modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModifiedTime;

    @TableField("updated_by")
    private String updatedBy;
}
