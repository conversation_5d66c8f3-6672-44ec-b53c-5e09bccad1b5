package com.cashop.aftersale.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cashop.aftersale.dao.entity.AsOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 售后订单Mapper接口
 * 数据访问层 - 数据库操作接口
 */
@Mapper
public interface AsOrderMapper extends BaseMapper<AsOrder> {

    /**
     * 根据售后单号查询
     */
    AsOrder selectByServiceNumber(@Param("serviceNumber") String serviceNumber);

    /**
     * 根据订单号查询售后记录
     */
    List<AsOrder> selectByOrderNo(@Param("orderNo") String orderNo);

    /**
     * 根据子订单号查询售后记录
     */
    List<AsOrder> selectBySubOrderNo(@Param("subOrderNo") String subOrderNo);

    /**
     * 根据用户ID分页查询售后订单
     */
    IPage<AsOrder> selectPageByUserId(Page<AsOrder> page, 
                                      @Param("userId") Long userId, 
                                      @Param("asStatus") Integer asStatus);

    /**
     * 查询超时未支付物流费用的订单
     */
    List<AsOrder> selectExpiredLogisticsPayment(@Param("status") Integer status, 
                                               @Param("expireTime") LocalDateTime expireTime, 
                                               @Param("limit") Integer limit);

    /**
     * 查询待处理的售后订单
     */
    List<AsOrder> selectPendingOrders(@Param("statuses") List<Integer> statuses);

    /**
     * 统计用户售后订单数量
     */
    Integer countByUserId(@Param("userId") Long userId);

    /**
     * 统计各状态订单数量
     */
    List<AsOrder> selectStatusStatistics(@Param("startDate") LocalDateTime startDate,
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * 查询需要同步状态的订单
     */
    List<AsOrder> selectOrdersForStatusSync(@Param("statuses") List<Integer> statuses,
                                           @Param("limit") Integer limit);

    /**
     * 批量更新订单状态
     */
    int batchUpdateStatus(@Param("orders") List<AsOrder> orders);

    /**
     * 根据供货平台单号查询
     */
    AsOrder selectBySupplierAsNo(@Param("supplierAsNo") String supplierAsNo);

    /**
     * 查询指定时间范围内的订单
     */
    List<AsOrder> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                   @Param("endTime") LocalDateTime endTime,
                                   @Param("statuses") List<Integer> statuses);

    /**
     * 查询子订单的在途售后订单
     * 在途状态包括：申请中、审核通过、待寄回、商品寄回中等
     */
    List<AsOrder> selectOngoingBySubOrderNo(@Param("subOrderNo") String subOrderNo);

    /**
     * 查询超时未发货的售后订单
     * 
     * @param timeoutDeadline 超时截止时间
     * @return 超时订单列表
     */
    List<AsOrder> selectTimeoutShippingOrders(@Param("timeoutDeadline") LocalDateTime timeoutDeadline);

    /**
     * 根据仓库操作员ID查询售后订单
     * 
     * @param operatorId 仓库操作员ID
     * @return 售后订单列表
     */
    List<AsOrder> selectByWarehouseOperator(@Param("operatorId") Long operatorId);

    /**
     * 查询需要仓库发货的售后订单
     * 
     * @param status 待仓库发货状态
     * @return 待发货订单列表
     */
    List<AsOrder> selectPendingWarehouseShipping(@Param("status") Integer status);
}
