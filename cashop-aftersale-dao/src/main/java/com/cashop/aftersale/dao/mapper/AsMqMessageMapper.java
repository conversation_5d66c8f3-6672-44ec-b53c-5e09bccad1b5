package com.cashop.aftersale.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cashop.aftersale.dao.entity.AsMqMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MQ消息通知记录表Mapper接口
 * DAO层 - 数据访问对象
 */
@Mapper
public interface AsMqMessageMapper extends BaseMapper<AsMqMessage> {

    /**
     * 根据售后单号查询MQ消息记录
     * 
     * @param serviceNumber 售后单号
     * @return MQ消息记录列表
     */
    List<AsMqMessage> selectByServiceNumber(@Param("serviceNumber") String serviceNumber);

    /**
     * 根据发送状态查询MQ消息记录
     * 
     * @param sendStatus 发送状态
     * @return MQ消息记录列表
     */
    List<AsMqMessage> selectBySendStatus(@Param("sendStatus") Integer sendStatus);

    /**
     * 查询待重试的MQ消息记录
     * 
     * @param maxRetryTimes 最大重试次数
     * @return 待重试的MQ消息记录列表
     */
    List<AsMqMessage> selectPendingRetryMessages(@Param("maxRetryTimes") Integer maxRetryTimes);

    /**
     * 更新MQ消息发送状态
     * 
     * @param id 消息ID
     * @param sendStatus 发送状态
     * @param sendResult 发送结果
     * @param errorMsg 错误信息
     * @return 更新行数
     */
    int updateSendStatus(@Param("id") Long id, 
                        @Param("sendStatus") Integer sendStatus,
                        @Param("sendResult") String sendResult,
                        @Param("errorMsg") String errorMsg);
}
