package com.cashop.aftersale.dao.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 售后流程记录表实体
 * 数据访问层 - 流程记录数据实体
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("as_process_log")
public class AsProcessLog {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后订单ID
     */
    @TableField("as_order_id")
    private Long asOrderId;

    /**
     * 操作类型:1-申请,2-支付,3-提交,4-审核,5-退款,6-取消,7-超时,8-重发
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 操作描述
     */
    @TableField("operation_desc")
    private String operationDesc;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private String operatorId;

    /**
     * 操作人姓名
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 通用审计字段
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(value = "created_time", fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField("created_by")
    private String createdBy;

    @TableField(value = "last_modified_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime lastModifiedTime;

    @TableField("updated_by")
    private String updatedBy;
}
