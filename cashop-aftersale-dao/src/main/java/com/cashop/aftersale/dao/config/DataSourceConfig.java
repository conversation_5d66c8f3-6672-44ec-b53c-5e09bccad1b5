package com.cashop.aftersale.dao.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;

/**
 * 数据源配置类
 * 配置Druid数据源和MyBatis SqlSessionFactory
 */
@Configuration
public class DataSourceConfig {

    @Value("${mybatis.mapper-locations:classpath:mapper/*.xml}")
    private String mapperLocations;

    @Value("${mybatis.type-aliases-package:com.cashop.aftersale.dao.entity}")
    private String typeAliasesPackage;

    /**
     * 配置Druid数据源
     */
    @Bean
    @Primary
    @ConfigurationProperties(prefix = "spring.datasource.druid")
    public DataSource dataSource() {
        return new DruidDataSource();
    }

    /**
     * 配置SqlSessionFactory
     */
    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource) throws Exception {
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        
        // 设置mapper文件位置
        sessionFactory.setMapperLocations(
            new PathMatchingResourcePatternResolver().getResources(mapperLocations)
        );
        
        // 设置实体类包路径
        sessionFactory.setTypeAliasesPackage(typeAliasesPackage);
        
        // 配置MyBatis设置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true); // 下划线转驼峰
        configuration.setCacheEnabled(true); // 启用二级缓存
        configuration.setLazyLoadingEnabled(true); // 启用懒加载
        configuration.setAggressiveLazyLoading(false); // 关闭积极懒加载
        
        sessionFactory.setConfiguration(configuration);
        
        return sessionFactory.getObject();
    }

    /**
     * 配置事务管理器
     */
    @Bean
    @Primary
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }
}
