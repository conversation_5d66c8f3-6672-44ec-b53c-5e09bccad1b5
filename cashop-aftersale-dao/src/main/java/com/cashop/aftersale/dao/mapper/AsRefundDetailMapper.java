package com.cashop.aftersale.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cashop.aftersale.dao.entity.AsRefundDetail;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后退款明细Mapper接口
 * 数据访问层 - 退款明细数据操作接口
 */
@Mapper
public interface AsRefundDetailMapper extends BaseMapper<AsRefundDetail> {

    /**
     * 根据售后订单ID查询退款明细列表
     */
    List<AsRefundDetail> selectByAsOrderId(@Param("asOrderId") Long asOrderId);

    /**
     * 根据售后单号查询退款明细列表
     */
    List<AsRefundDetail> selectByServiceNumber(@Param("serviceNumber") String serviceNumber);

    /**
     * 根据退款类型查询退款明细
     */
    List<AsRefundDetail> selectByRefundType(@Param("asOrderId") Long asOrderId, 
                                           @Param("refundType") Integer refundType);

    /**
     * 查询指定售后订单的退款总金额
     */
    BigDecimal selectTotalRefundAmount(@Param("asOrderId") Long asOrderId);

    /**
     * 查询指定售后订单指定类型的退款总金额
     */
    BigDecimal selectRefundAmountByType(@Param("asOrderId") Long asOrderId, 
                                       @Param("refundType") Integer refundType);

    /**
     * 批量插入退款明细
     */
    int batchInsert(@Param("details") List<AsRefundDetail> details);

    /**
     * 更新退款状态
     */
    int updateRefundStatus(@Param("id") Long id, 
                          @Param("refundStatus") Integer refundStatus,
                          @Param("refundTransactionId") String refundTransactionId);

    /**
     * 分页查询退款明细
     */
    IPage<AsRefundDetail> selectPageByCondition(Page<AsRefundDetail> page,
                                               @Param("serviceNumber") String serviceNumber,
                                               @Param("refundType") Integer refundType,
                                               @Param("refundStatus") Integer refundStatus);

    /**
     * 查询待退款的明细
     */
    List<AsRefundDetail> selectPendingRefunds(@Param("limit") Integer limit);

    /**
     * 查询退款失败的明细
     */
    List<AsRefundDetail> selectFailedRefunds(@Param("limit") Integer limit);

    /**
     * 统计退款明细数量
     */
    Long countByAsOrderId(@Param("asOrderId") Long asOrderId);

    /**
     * 根据商品ID查询退款明细
     */
    List<AsRefundDetail> selectByProductId(@Param("productId") Long productId);

    /**
     * 查询服务费退款明细
     */
    List<AsRefundDetail> selectServiceFeeRefunds(@Param("asOrderId") Long asOrderId);

    /**
     * 查询商品退款明细
     */
    List<AsRefundDetail> selectProductRefunds(@Param("asOrderId") Long asOrderId);

    /**
     * 查询运费退款明细
     */
    List<AsRefundDetail> selectShippingFeeRefunds(@Param("asOrderId") Long asOrderId);
}
