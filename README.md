# Cashop售后系统

## 项目简介

Cashop售后系统是一个完整的电商售后管理系统，支持发货前退款、发货后退款、发货后退货退款等多种售后类型，具备完整的业务流程管理、第三方消息处理、物流费用计算等功能。

## 核心功能

### 🎯 售后申请管理
- ✅ 发货前退款申请
- ✅ 发货后退款申请  
- ✅ 发货后退货退款申请
- ✅ 申请条件验证和时效控制
- ✅ 商品数量和金额计算

### 📦 商品管理
- ✅ 子订单商品信息管理
- ✅ 商品规格属性支持(JSON格式)
- ✅ 体积重量信息记录
- ✅ 可申请数量动态计算

### 💰 物流费用计算
- ✅ 基于体积重量的运费计算
- ✅ 运费和服务费分离
- ✅ 多币种支持
- ✅ 30分钟支付时效控制

### 🔄 第三方集成
- ✅ 供货平台消息接收和处理
- ✅ 异步消息消费机制
- ✅ 失败重试和监控
- ✅ 完整的消息追踪

### 📊 流程管理
- ✅ 完整的状态流转
- ✅ 操作日志记录
- ✅ 超时自动撤销
- ✅ 通知推送

## 技术架构

### 后端技术栈
- **Spring Boot 2.7+** - 应用框架
- **MyBatis Plus** - ORM框架
- **MySQL 8.0+** - 数据库
- **Redis** - 缓存
- **FastJSON** - JSON处理
- **Lombok** - 代码简化

### 核心设计模式
- **分层架构**: Controller -> Service -> Mapper
- **策略模式**: 不同售后类型的处理策略
- **观察者模式**: 状态变更通知
- **模板方法**: 通用业务流程模板

## 项目结构

```
src/main/java/com/cashop/aftersale/
├── controller/          # 控制器层
│   ├── AftersaleController.java
│   ├── AdminAftersaleController.java
│   └── ThirdPartyMessageController.java
├── service/            # 服务层
│   ├── AftersaleOrderService.java
│   ├── LogisticsCalculationService.java
│   ├── ThirdPartyMessageService.java
│   ├── AsProcessLogService.java
│   ├── NotificationService.java
│   ├── BusinessValidationService.java
│   └── DataSyncService.java
├── entity/             # 实体类
│   ├── AsOrder.java
│   ├── AsProduct.java
│   ├── AsProcessLog.java
│   └── ThirdPartyMessage.java
├── mapper/             # 数据访问层
├── dto/                # 数据传输对象
├── vo/                 # 视图对象
├── enums/              # 枚举类
├── exception/          # 异常处理
├── config/             # 配置类
└── utils/              # 工具类
```

## 业务流程

### 发货前退款流程
1. 用户申请退款 → 2. 系统验证条件 → 3. 提交供货平台 → 4. 供货平台处理 → 5. 退款完成

### 发货后退款流程  
1. 用户申请退款 → 2. 系统验证条件 → 3. 提交供货平台 → 4. 供货平台审核 → 5. 退款完成

### 发货后退货退款流程
1. 用户申请退货 → 2. 计算物流费用 → 3. 用户支付运费 → 4. 提交供货平台 → 5. 供货平台审核 → 6. 用户寄回商品 → 7. 验收完成 → 8. 退款完成

## 数据库设计

### 核心表结构
- **as_order**: 售后订单主表(包含订单服务费信息)
- **as_product**: 子订单商品表
- **as_refund_detail**: 退款明细表(记录各类退款明细)
- **as_process_log**: 流程记录表
- **third_party_message**: 第三方消息表

### 关键字段说明
- **apply_quantity**: 申请总件数
- **return_volume/weight**: 退货体积重量
- **shipping_fee/service_fee**: 运费和服务费
- **order_service_fee**: 订单服务费总额(按件收取)
- **unit_service_fee**: 单件服务费
- **approved_quantity**: 供货平台批准件数
- **actual_refund_quantity**: 实际退款件数
- **refund_type**: 退款类型(商品退款/服务费退款/运费退款)

## 快速开始

### 1. 环境要求
- JDK 8+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 执行设计文档中的建表SQL
-- 参考: 02_设计文档/售后系统研发设计.md
```

### 3. 配置修改
```yaml
# application.yml
spring:
  datasource:
    url: ********************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
```

### 4. 启动应用
```bash
mvn clean install
mvn spring-boot:run
```

### 5. 接口测试
```bash
# 查询可退款商品
curl -X GET "http://localhost:8080/api/aftersale/products?subOrderNo=SUB202412280001"

# 创建售后申请
curl -X POST "http://localhost:8080/api/aftersale/apply" \
  -H "Content-Type: application/json" \
  -d '{
    "orderNo": "CO202412280001",
    "subOrderNo": "SUB202412280001", 
    "userId": 12345,
    "asType": 1,
    "applyReason": "商品质量问题",
    "applyQuantity": 2
  }'
```

## TODO集成清单

### 订单系统集成
- [ ] `OrderService.getOrderInfo()` - 获取订单信息
- [ ] `OrderService.getOrderProducts()` - 获取订单商品
- [ ] `OrderService.validateOrderStatus()` - 验证订单状态

### 支付系统集成  
- [ ] `PaymentService.processLogisticsPayment()` - 物流费用支付
- [ ] `PaymentService.processRefund()` - 退款处理
- [ ] `PaymentService.refundLogisticsFee()` - 运费退还

### 第三方供货平台集成
- [ ] `SupplierService.submitAftersaleRequest()` - 提交售后申请
- [ ] `SupplierService.queryProcessStatus()` - 查询处理状态
- [ ] `SupplierService.cancelSupplierRequest()` - 取消申请

### 消息通知集成
- [ ] `MessageService.sendMessage()` - 发送用户通知
- [ ] `MessageService.sendSMS()` - 短信通知
- [ ] `MessageService.sendEmail()` - 邮件通知

## 监控和运维

### 定时任务
- **超时撤销检查**: 每分钟检查支付超时的订单
- **消息重试**: 每5分钟重试失败的第三方消息
- **数据同步**: 每小时同步订单商品数据

### 日志监控
- 所有业务操作都有详细日志记录
- 异常情况自动记录错误日志
- 支持日志级别动态调整

### 性能优化
- 数据库索引优化
- 分页查询支持
- 异步消息处理
- 缓存机制

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
