# 售后支付系统模块边界设计说明

## 设计原则

### 1. 模块职责清晰
- **Web层**: 负责外部接口适配，处理外部依赖的DTO转换
- **Service层**: 纯业务逻辑，不依赖外部系统的具体实现
- **Facade层**: 定义内部接口契约和数据传输对象

### 2. 依赖边界控制
- **外部依赖集中管理**: 只在Web层引入外部系统依赖
- **内部DTO独立**: Service层使用自定义的内部DTO，不直接依赖外部系统
- **转换层隔离**: 在Web层处理外部DTO与内部DTO的转换

## 架构设计

### 模块依赖关系
```
┌─────────────────┐    ┌──────────────────────┐
│   Web Layer     │    │  External Payment    │
│                 │────│  System DTO          │
│ - Controller    │    │  (QueryPaymentDTO)   │
│ - DTO Convert   │    └──────────────────────┘
└─────────────────┘
         │
         ▼
┌─────────────────┐    ┌──────────────────────┐
│ Service Layer   │    │  Internal DTO        │
│                 │────│  (PaymentCallback    │
│ - Business      │    │   Request)           │
│   Logic         │    └──────────────────────┘
└─────────────────┘
         │
         ▼
┌─────────────────┐
│ Facade Layer    │
│                 │
│ - Interface     │
│ - Internal DTO  │
└─────────────────┘
```

### 支付回调处理流程

#### 1. 外部接口接收 (Web Layer)
```java
@PostMapping("/payment/callback")
public Result<String> paymentCallback(
    @RequestBody QueryPaymentResponseDTO response,  // 外部DTO
    @RequestParam Long userId) {
    
    // 转换外部DTO为内部DTO
    PaymentCallbackRequest internalRequest = convertToInternalRequest(response, userId);
    
    // 调用业务层处理
    return aftersaleOrderService.handlePaymentCallback(internalRequest);
}
```

#### 2. DTO转换 (Web Layer)
```java
private PaymentCallbackRequest convertToInternalRequest(
    QueryPaymentResponseDTO response, Long userId) {
    
    PaymentCallbackRequest request = new PaymentCallbackRequest();
    request.setPaymentTradeNo(response.getPaymentTradeNo());
    request.setBusinessOrderNo(response.getBusinessOrderNo());
    request.setStatus(response.getStatus());
    // ... 其他字段映射
    request.setUserId(userId);  // 补充业务必需字段
    return request;
}
```

#### 3. 业务处理 (Service Layer)
```java
public boolean handlePaymentCallback(PaymentCallbackRequest request) {
    // 纯业务逻辑，不依赖外部系统具体实现
    // 1. 验证订单状态
    // 2. 验证支付金额
    // 3. 更新订单状态
    // 4. 触发后续流程
}
```

## 优势分析

### ✅ 模块边界清晰
- Service层专注业务逻辑，不被外部系统绑定
- Web层负责外部适配，隔离变化影响
- Facade层定义稳定的内部契约

### ✅ 可维护性强
- 外部系统DTO变更只影响Web层
- 业务逻辑变更不影响外部接口适配
- 内部DTO可以根据业务需要优化设计

### ✅ 可测试性好
- Service层可以独立进行单元测试
- 不需要模拟外部系统的复杂依赖
- 测试数据构造更简单

### ✅ 扩展性强
- 支持多种外部支付系统接入
- 可以在Web层添加不同的转换适配器
- 业务逻辑复用性高

## 文件结构

### Web层 (cashop-aftersale-web)
```
src/main/java/com/cashop/aftersale/web/controller/
├── AftersaleController.java           # 支付回调接口
└── ...

dependencies:
├── cashop-payment-facade             # 外部支付系统依赖
├── cashop-aftersale-service          # 内部服务依赖
└── cashop-aftersale-facade           # 内部接口依赖
```

### Service层 (cashop-aftersale-service)
```
src/main/java/com/cashop/aftersale/service/
├── AftersaleOrderService.java        # 业务接口
├── impl/AftersaleOrderServiceImpl.java # 业务实现
└── ...

dependencies:
├── cashop-aftersale-facade           # 内部接口依赖
├── cashop-aftersale-dao              # 数据访问依赖
└── transaction-framework             # 一致性框架依赖
# ❌ 不依赖外部支付系统
```

### Facade层 (cashop-aftersale-facade)
```
src/main/java/com/cashop/aftersale/facade/dto/
├── PaymentCallbackRequest.java       # 内部支付回调DTO
├── PrePaymentRequest.java            # 预支付请求DTO
├── PrePaymentResponse.java           # 预支付响应DTO
└── ...

dependencies:
├── common-base                       # 基础依赖
└── validation-api                    # 验证依赖
# ❌ 不依赖外部系统
```

## 关键设计点

### 1. 用户ID处理
- 外部支付系统可能不包含用户ID
- 在Web层通过请求参数或Token解析获取
- 转换时补充到内部DTO中

### 2. 字段映射策略
- 一对一映射: 直接字段复制
- 业务增强: 添加业务必需但外部系统不提供的字段
- 格式转换: 处理不同系统间的数据格式差异

### 3. 错误处理
- Web层处理外部系统相关的异常
- Service层处理业务逻辑相关的异常
- 分层异常处理，职责明确

### 4. 日志记录
- Web层记录外部接口调用日志
- Service层记录业务处理日志
- 便于问题排查和监控

## 最佳实践

1. **坚持单一职责**: 每层只处理自己职责范围内的事情
2. **依赖倒置**: 高层模块不依赖低层模块的具体实现
3. **接口隔离**: 定义最小化的接口契约
4. **开闭原则**: 对扩展开放，对修改关闭

这种设计确保了系统的可维护性、可扩展性和可测试性，是企业级应用的最佳实践。
