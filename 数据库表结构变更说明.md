# 售后系统数据库表结构变更说明

## 版本信息
- **版本号**: V1.1.0
- **变更日期**: 2025-01-22
- **变更类型**: 字段新增

## 变更概述

本次变更主要是为了完善售后订单的用户角色管理和订单关联信息，以及商品价格信息的记录。

## 详细变更内容

### 1. as_order 表变更

#### 1.1 用户角色字段
```sql
ALTER TABLE `as_order` 
ADD COLUMN `user_role` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用户角色:1-购买者,2-店主' AFTER `user_id`;
```

**字段说明:**
- `user_role`: 用户角色标识
  - `1`: 购买者 - 商品的实际购买人
  - `2`: 店主 - 店铺的经营者

**业务场景:**
- 区分不同角色用户的售后权限
- 支持店主代理购买者处理售后
- 便于统计不同角色的售后数据

#### 1.2 订单关联字段
```sql
ALTER TABLE `as_order` 
ADD COLUMN `cashop_order_no` varchar(64) DEFAULT NULL COMMENT 'Cashop二级单号' AFTER `sub_order_no`,
ADD COLUMN `sku_order_no` varchar(64) DEFAULT NULL COMMENT 'SKU单号' AFTER `cashop_order_no`;
```

**字段说明:**
- `cashop_order_no`: Cashop系统的二级订单号
- `sku_order_no`: SKU级别的订单号

**业务场景:**
- 售后单与SKU单一一对应关系
- 支持精确的订单层级追踪
- 便于与上游订单系统对接

#### 1.3 金额信息字段
```sql
ALTER TABLE `as_order` 
ADD COLUMN `actual_paid_amount` decimal(10,2) DEFAULT NULL COMMENT '实付金额' AFTER `apply_amount`,
ADD COLUMN `discount_amount` decimal(10,2) DEFAULT NULL COMMENT '优惠金额' AFTER `actual_paid_amount`;
```

**字段说明:**
- `actual_paid_amount`: 用户实际支付的金额
- `discount_amount`: 享受的优惠金额

**业务场景:**
- 记录订单的真实支付情况
- 支持优惠金额的退款计算
- 便于财务对账和分析

### 2. as_product 表变更

#### 2.1 商品售价字段
```sql
ALTER TABLE `as_product` 
ADD COLUMN `sale_price` decimal(10,2) DEFAULT NULL COMMENT '商品售价' AFTER `unit_price`;
```

**字段说明:**
- `sale_price`: 商品的标准售价

**业务场景:**
- 记录商品的官方售价
- 与实际购买价格进行对比分析
- 支持价格变动的历史追踪

### 3. 索引优化

为提高查询性能，新增以下索引：

```sql
CREATE INDEX `idx_as_order_user_role` ON `as_order` (`user_role`);
CREATE INDEX `idx_as_order_cashop_order_no` ON `as_order` (`cashop_order_no`);
CREATE INDEX `idx_as_order_sku_order_no` ON `as_order` (`sku_order_no`);
```

## 代码变更

### 1. 实体类更新

#### AsOrder.java
- 新增 `userRole` 字段
- 新增 `cashopOrderNo` 字段  
- 新增 `skuOrderNo` 字段
- 新增 `actualPaidAmount` 字段
- 新增 `discountAmount` 字段

#### AsProduct.java
- 新增 `salePrice` 字段

### 2. 常量类新增

#### UserRoleConstants.java
```java
public class UserRoleConstants {
    public static final int BUYER = 1;        // 购买者
    public static final int SHOP_OWNER = 2;   // 店主
}
```

### 3. DTO类更新

#### AftersaleApplyRequest.java
- 新增 `userRole` 字段
- 新增 `cashopOrderNo` 字段
- 新增 `skuOrderNo` 字段

## 兼容性说明

### 1. 向后兼容
- 所有新增字段均设置为可空或有默认值
- 现有API接口保持兼容
- 现有业务逻辑不受影响

### 2. 数据迁移
- 现有数据的 `user_role` 默认设置为 `1`（购买者）
- 其他新增字段保持NULL值，待业务数据补充

### 3. 升级建议
1. 先执行数据库迁移脚本
2. 部署新版本代码
3. 验证功能正常
4. 逐步补充历史数据

## 测试验证

### 1. 数据库验证
```sql
-- 验证字段是否正确添加
DESCRIBE as_order;
DESCRIBE as_product;

-- 验证索引是否创建成功
SHOW INDEX FROM as_order;
```

### 2. 功能验证
- 售后申请接口支持新字段
- 用户角色验证逻辑
- 订单关联查询功能
- 金额计算逻辑

## 风险评估

### 1. 低风险
- 字段新增不影响现有功能
- 有完整的回滚方案
- 充分的测试覆盖

### 2. 注意事项
- 新字段的业务逻辑需要逐步完善
- 历史数据需要后续补充
- 需要更新相关文档和培训

## 回滚方案

如需回滚，执行以下SQL：

```sql
-- 删除新增字段
ALTER TABLE `as_order` 
DROP COLUMN `user_role`,
DROP COLUMN `cashop_order_no`, 
DROP COLUMN `sku_order_no`,
DROP COLUMN `actual_paid_amount`,
DROP COLUMN `discount_amount`;

ALTER TABLE `as_product` 
DROP COLUMN `sale_price`;

-- 删除新增索引
DROP INDEX `idx_as_order_user_role` ON `as_order`;
DROP INDEX `idx_as_order_cashop_order_no` ON `as_order`;
DROP INDEX `idx_as_order_sku_order_no` ON `as_order`;
```

## 后续计划

1. **业务逻辑完善**: 基于新字段完善售后业务逻辑
2. **数据补充**: 逐步补充历史订单的新字段数据
3. **报表优化**: 利用新字段优化售后数据分析
4. **权限控制**: 基于用户角色实现精细化权限控制
