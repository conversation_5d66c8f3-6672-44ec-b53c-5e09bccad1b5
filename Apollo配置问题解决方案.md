# Apollo配置问题解决方案

## 问题分析

根据您提供的启动参数，发现以下配置问题：

```bash
-javaagent:/Users/<USER>/Documents/hades-agent-dist/hades-agent-dist/hades-agent.jar 
-Dhades.env.identity=stable 
-Denv=LPT 
-Dapollo.cluster=stable 
-Dspring.profiles.active=stable
```

## 主要问题

1. **缺少stable环境配置文件** - 已解决 ✅
2. **Apollo配置不完整** - 已解决 ✅
3. **数据库连接配置问题** - 需要处理 ⚠️
4. **启动类缺少Apollo注解** - 已解决 ✅

## 已完成的修复

### 1. 创建了stable环境配置文件
- 文件：`cashop-aftersale-web/src/main/resources/application-stable.yml`
- 包含Apollo配置、数据库配置、MyBatis配置等

### 2. 修正了Apollo配置
- 在`application.yml`中添加了`cluster`配置支持
- 在启动类中添加了`@EnableApolloConfig`注解

### 3. 创建了启动脚本
- 文件：`start-stable.sh`
- 包含完整的JVM参数和Apollo配置

## 需要解决的问题

### 1. 数据库连接问题

**问题**：MySQL数据库`cashop_aftersale`不存在或连接失败

**解决方案**：

#### 方案A：手动创建数据库
```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS cashop_aftersale CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行初始化脚本
USE cashop_aftersale;
SOURCE /Users/<USER>/Documents/cashop/cashop-aftersale/sql/init_database.sql;
```

#### 方案B：使用Docker MySQL（推荐）
```bash
# 启动MySQL容器
docker run -d \
  --name mysql-cashop \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=123456 \
  -e MYSQL_DATABASE=cashop_aftersale \
  -e MYSQL_CHARACTER_SET_SERVER=utf8mb4 \
  -e MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci \
  mysql:8.0

# 等待MySQL启动完成
sleep 30

# 导入初始化脚本
docker exec -i mysql-cashop mysql -u root -p123456 cashop_aftersale < sql/init_database.sql
```

### 2. Apollo配置中心连接

**当前配置**：`http://apollo.castable.hk:8080`

**检查步骤**：
1. 确认Apollo服务器是否可访问
2. 确认应用ID `cashop-aftersale` 在Apollo中是否已配置
3. 确认stable集群配置是否存在

**测试命令**：
```bash
# 测试Apollo连接
curl -I http://apollo.castable.hk:8080

# 检查应用配置
curl "http://apollo.castable.hk:8080/configs/cashop-aftersale/stable/application"
```

## 启动步骤

### 1. 准备数据库
```bash
# 如果使用本地MySQL
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS cashop_aftersale CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -u root -p cashop_aftersale < sql/init_database.sql

# 或使用Docker（推荐）
docker run -d --name mysql-cashop -p 3306:3306 -e MYSQL_ROOT_PASSWORD=123456 -e MYSQL_DATABASE=cashop_aftersale mysql:8.0
```

### 2. 启动应用
```bash
# 使用提供的启动脚本
./start-stable.sh

# 或直接使用Maven
cd cashop-aftersale-web
mvn spring-boot:run -Dspring-boot.run.jvmArguments="-javaagent:/Users/<USER>/Documents/hades-agent-dist/hades-agent-dist/hades-agent.jar -Dhades.env.identity=stable -Denv=LPT -Dapollo.cluster=stable -Dspring.profiles.active=stable"
```

## 配置验证

### 1. 检查Apollo连接
启动后查看日志中是否有Apollo连接成功的信息：
```
Apollo Config Service connected successfully
```

### 2. 检查数据库连接
启动后查看日志中是否有数据库连接成功的信息：
```
HikariPool-1 - Start completed
```

### 3. 健康检查
```bash
curl http://localhost:8080/slb/health
# 应该返回: ok
```

## 故障排除

### 如果Apollo连接失败
1. 检查网络连接：`ping apollo.castable.hk`
2. 检查端口：`telnet apollo.castable.hk 8080`
3. 临时禁用Apollo：在`application-stable.yml`中设置
   ```yaml
   apollo:
     bootstrap:
       enabled: false
   ```

### 如果数据库连接失败
1. 检查MySQL服务：`brew services list | grep mysql`
2. 检查端口：`lsof -i :3306`
3. 修改数据库配置：在`application-stable.yml`中更新数据库连接信息

## 配置文件说明

### application.yml（主配置）
- Apollo基础配置
- 应用名称和ID
- 本地环境数据库配置（fallback）

### application-stable.yml（stable环境）
- stable环境专用配置
- Apollo cluster配置
- 数据库连接配置
- MyBatis配置
- 日志配置

### 启动类注解
- `@EnableApolloConfig`：启用Apollo配置
- `@MapperScan`：扫描MyBatis Mapper
- `@SpringBootApplication`：Spring Boot应用配置
