# Java项目初始化规则

## 执行要求
**重要**：严格按照以下规则生成详细执行计划（md格式），包含项目初始化、启动、健康检测，每条计划需要完成情况标记，然后根据计划执行任务，每完成一步更新完成标记。

## 项目概述
基于Maven的Java多模块项目，使用Spring Boot 3.2.6 + OpenJDK 17，包含完整微服务架构配置。

## 初始化步骤
1. **项目结构规划** - 创建多模块Maven结构
2. **创建.gitignore文件** - 配置版本控制忽略规则
3. **包名规则设置** - 设置标准包名规范
4. **技术栈版本确认** - 确认所有依赖版本
5. **Maven配置** - 父POM和子模块配置
6. **模块依赖配置** - 配置各模块间依赖关系
7. **核心配置文件创建** - 创建application.yml等配置
8. **Spring Boot启动类** - web模块启动类配置
9. **健康检查控制器** - 创建/slb/health接口
10. **服务注册配置** - Eureka客户端配置
11. **日志配置** - Log4j2多环境配置
12. **验证步骤** - 编译、启动、健康检查验证
13. **启动服务** - 通过启动类启动web服务并进行健康检查

## 关键约束
- **避免循环依赖**：严格控制模块依赖关系
- **使用原生MyBatis**：禁止MyBatis Plus和mybatis-spring-boot-starter
- **Feign使用fallbackFactory**：便于异常处理和日志记录
- **日志统一管理**：所有日志配置在log4j2.xml中管理
- **Swagger注释必须**：所有控制器和接口添加完整注释

## 项目结构

### 目录结构
```
{项目名}/
├── {项目名}-common      # 公共模块：工具类、共享组件
├── {项目名}-dao         # 数据访问层：MyBatis配置、数据访问对象
├── {项目名}-facade      # 外观层：对外接口定义、Feign客户端
├── {项目名}-service     # 业务服务层：业务逻辑实现
└── {项目名}-web         # Web控制层：Spring Boot启动类、控制器
```

### 标准化文档结构
在项目初始化时，必须创建以下文档目录结构，用于管理项目的全生命周期文档：
```
{项目名}/
├── 01_需求文档/                     # 项目的目标与功能需求的完整描述
│   ├── 项目基础信息.md              # 名称、人员、周期、工时、功能概述、架构、依赖项、工具信息等
│   └── 功能需求说明.md              # 各功能点的详细需求说明
├── 02_设计文档/                    # 依据需求的详细系统设计
│   └── 系统研发设计.md              # 前后端架构、组件关系,API 接口、输入输出参数说明,表结构设计、ER图等,可放置低保真原型图或设计草图,安全开发规范、静态分析、安全审查记录.
├── 03_测试用例/                    # 项目的功能与安全测试记录
│   └── 单元测试用例.md              # 根据需求逐项验证功能是否实现,测试时尤其注意功能安全性。
├── 04_开发任务计划/                 # 项目实施、开发与上线相关安排
│   ├── 研发任务计划.md              # 开发过程中的任务分解、进度跟踪、变更日志、技术实现、路由、权限等说明
│   └── 发布计划.md                 # 发布周期与时间表
```

### 包名规则
- **基础包名**：`com.cashop.{项目名}`
- **特殊处理**：项目名中的"-"替换为"."
- **示例**：`base-template-project` → `com.cashop.base.template.project`

## 技术栈配置

### 环境要求
- **JDK**：OpenJDK 17（验证：`java -version`显示17.x）

### 核心依赖
- **Spring Boot**：3.2.6
- **Spring Cloud**：2023.0.4
- **数据库**：MySQL 8驱动 + HikariCP连接池
- **ORM**：原生MyBatis 3.5.14（禁止MyBatis Plus/JPA/Hibernate）
- **服务发现**：Eureka
- **配置中心**：Apollo 2.1.0
- **日志**：Log4j2 2.21.1 + SLF4J 2.0.13
- **缓存**：Jedis 4.4.3（禁止Lettuce）
- **API文档**：OpenAPI 3.0 (SpringDoc 2.3.0)
- **服务调用**：Spring Cloud OpenFeign 4.1.4
- **工具库**：Apache Commons Lang3

## 依赖管理规则

### 版本管理策略
- **父POM统一管理**：所有依赖版本在`<properties>`和`<dependencyManagement>`中管理
- **子模块不写版本**：由父POM统一控制版本
- **可选依赖**：Redis、RocketMQ、XXL-Job等只在父POM管理版本，按需添加

### 模块依赖规范

#### common模块
- **允许**：Spring Boot基础依赖、Apache Commons Lang3、Jackson、SLF4J、Validation
- **禁止**：Spring Web、数据库、业务相关依赖

#### dao模块
- **允许**：SLF4J、MySQL驱动、MyBatis、HikariCP
- **禁止**：common模块、Spring Web、业务逻辑、外部服务调用

#### service模块
- **允许**：common模块、dao模块、Spring IOC基础包、工具包、SLF4J
- **禁止**：Spring Web、数据库驱动（dao层提供）、外部服务调用（facade层提供）

#### facade模块
- **允许**：Spring Cloud OpenFeign、SLF4J
- **禁止**：common模块、数据库、业务逻辑

#### web模块
- **允许**：service模块、Spring Boot Web、Actuator、Eureka Client、Apollo、日志
- **禁止**：common模块、facade模块、dao模块

### 可选依赖（父POM管理版本，按需添加）
- **缓存**：Redis客户端（Jedis）
- **消息队列**：RocketMQ客户端
- **任务调度**：XXL-Job客户端
- **其他中间件**：Elasticsearch、MongoDB等第三方服务客户端

### 依赖排除规则
- **排除logback**：所有模块排除Spring Boot默认logback依赖
- **避免冲突**：防止传递依赖版本冲突
- **清理无用依赖**：及时清理不再使用的依赖

## 核心模块配置

### web模块配置

#### Spring Boot启动类
- **路径**：`com.cashop.{项目名}.web.Application.java`
- **注解**：`@SpringBootApplication` + `@EnableDiscoveryClient`

#### 健康检查控制器
- **路径**：`com.cashop.{项目名}.web.controller.HealthCheckController.java`
- **接口**：`GET /slb/health` 返回"ok"
- **必须添加Swagger注解**：`@Tag` + `@Operation`

```java
@RestController
@Tag(name = "健康检查", description = "系统健康检查相关接口")
public class HealthCheckController {
    @Operation(summary = "健康检查", description = "检查系统健康状态，返回ok表示系统正常")
    @GetMapping(value = "/slb/health")
    public String health() {
        return "ok";
    }
}
```

#### Swagger配置
- **依赖**：`springdoc-openapi-starter-webmvc-ui`
- **默认开启**：无需额外Java类和YAML配置
- **访问地址**：`http://localhost:8080/swagger-ui/index.html`
- **API文档**：`http://localhost:8080/v3/api-docs`

### 配置文件（不使用bootstrap.yml）

#### application.yml主配置
```yaml
app:
  id: {项目名}
apollo:
  bootstrap:
    enabled: true
    namespaces: application

spring:
  profiles:
    active: local
  application:
    name: {项目名}

server:
  port: 8080
  connection-timeout: 30000
  server-header: unknown
  error:
    include-stacktrace: never
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 2048
  http2:
    enabled: true
  tomcat:
    protocol: org.apache.coyote.http11.Http11Nio2Protocol
    max-connections: 10000
    max-threads: 500
    min-spare-threads: 50
    accept-count: 200
    keep-alive-timeout: 30000
    max-keep-alive-requests: 1000
    uri-encoding: UTF-8

logging:
  config: classpath:log4j2-${spring.profiles.active}.xml
```

#### 环境配置文件
- `application-local.yml` - 本机环境
- `application-stable.yml` - 开发环境
- `application-release.yml` - 测试环境
- `application-prod.yml` - 生产环境

#### Apollo环境配置
```yaml
# local/stable环境
apollo:
  cluster: stable
  meta: http://apollo.akcrelease.com:8080

# release环境
apollo:
  cluster: release
  meta: http://apollo.akcrelease.com:8080

# 生产环境
apollo:
  cluster: default
  meta: http://apollo.aikucun.com:8080
```

### facade模块配置

#### Feign客户端
- **功能**：对外暴露接口，供其他服务调用
- **依赖**：`spring-cloud-starter-openfeign`
- **包路径**：`com.cashop.{项目名}.facade.client`

#### Feign FallbackFactory规则
- **必须使用fallbackFactory**：不使用fallback，便于获取异常信息
- **内部类实现**：使用内部类实现FallbackFactory接口

```java
@FeignClient(name = "service-name", path = "/api/path",
             fallbackFactory = XxxFeignClient.XxxFeignFallbackFactory.class)
public interface XxxFeignClient {
    @GetMapping("/method")
    String method();

    @Component
    class XxxFeignFallbackFactory implements FallbackFactory<XxxFeignClient> {
        private static final Logger logger = LoggerFactory.getLogger(XxxFeignFallbackFactory.class);

        @Override
        public XxxFeignClient create(Throwable cause) {
            return new XxxFeignClient() {
                @Override
                public String method() {
                    logger.error("Feign call failed", cause);
                    throw new RuntimeException("Feign call failed", cause);
                }
            };
        }
    }
}
```

**关键要点**：
1. 使用`fallbackFactory`属性，不使用`fallback`
2. 内部类使用`@Component`注解
3. 通过SLF4J记录异常栈信息
4. 重新抛出异常，保持异常传播链

## 服务注册与日志配置

### Eureka配置
- **服务URL**：`http://merchant:<EMAIL>:8080/eureka/`
- **实例ID格式**：`ip:port`

### Log4j2日志配置
- **多环境配置文件**：
  - `log4j2-local.xml` - 本地环境（控制台输出）
  - `log4j2-stable.xml` - 开发环境
  - `log4j2-release.xml` - 测试环境
  - `log4j2-prod.xml` - 生产环境
- **日志输出路径**：`/usr/local/tomcat/logs/catalina.out`
- **日志输出格式**：`%d{yyyy-MM-dd HH:mm:ss.SSS}[%-5level][%t][%logger{36}][%L] - %msg%n`
- **滚动策略**：按日期滚动，文件格式`catalina-{yyyy-MM-dd}.out`，保留15个历史文件
- **日志等级**：
  - `com.cashop`、`com.mengxiang`、`com.aikucun`包：INFO级别
  - 其他包：WARN级别
- **配置原则**：所有日志配置统一在log4j2-{环境}.xml中管理，不在application.yml中重复配置

### 日志输出策略
- **local环境**：只输出到控制台，注释文件输出
- **其他环境**：只输出到文件，注释控制台输出

### SLF4J配置
- **日志门面**：SLF4J
- **日志实现**：Log4j2
- **依赖排除**：排除Spring Boot默认logback依赖



## Maven配置规则

### groupId和artifactId命名规则
- **groupId** 将{项目名}中的“-”替换为“.”，去掉最后一段，并在前面加上“com.cashop.”作为groupId。如：base-template-project，groupId为com.cashop.base.template
- **artifactId：** 使用{项目名}


### 父POM管理
- **版本管理：** 父POM统一管理所有依赖版本号
- **子模块：** 不写版本号，由父POM统一管理
- **依赖管理：** 使用`<dependencyManagement>`统一管理

### 子pom管理
-- **父pom引用：** 需要添加<relativePath>../pom.xml</relativePath>

### 公共参数
```xml
<properties>
  <skipTests>true</skipTests> <!-- 默认跳过测试 -->
</properties>
```

### 测试插件配置
```xml
<maven-surefire-plugin>
  <configuration>
    <skipTests>${skipTests}</skipTests>
  </configuration>
</maven-surefire-plugin>
```

### web子模块 spring-boot 打jar包
jar包名称使用项目名.jar，不含有版本号 要添加<finalName>${project.artifactId}</finalName>

```xml
    <build>
        <!-- jar包名称使用项目名.jar，不含有版本号 -->
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <mainClass>com.mx.ai.code.mcp.McpServerApplication</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

```

## 项目启动验证

### 验证步骤
```bash
# 1. 设置JDK环境
export JAVA_HOME=/path/to/jdk-17
export PATH=$JAVA_HOME/bin:$PATH

# 2. 编译项目
mvn clean compile -DskipTests

# 3. 启动应用
cd {项目名}-web
mvn spring-boot:run -Dspring-boot.run.profiles=stable

# 4. 健康检查
curl -X GET http://localhost:8080/slb/health
# 预期响应: "ok"
```

## 关键要点与最佳实践

### 配置要点
1. **Java环境**：确保JAVA_HOME指向JDK 17
2. **依赖管理**：父POM统一版本管理，避免冲突
3. **日志配置**：排除logback，使用log4j2统一管理
4. **ORM选择**：使用原生MyBatis，禁止MyBatis Plus/JPA/Hibernate
5. **Feign配置**：使用fallbackFactory获取异常信息
6. **模块职责**：严格控制依赖，避免循环依赖
7. **Swagger配置**：SpringDoc OpenAPI 3.0默认开启
8. **配置分离**：日志配置在log4j2.xml，不在application.yml重复

### 常见问题
1. **Java版本不匹配**：检查JAVA_HOME和PATH环境变量
2. **依赖冲突**：移除JPA/Hibernate依赖
3. **编译失败**：确保包名"-"正确替换为"."
4. **启动失败**：检查配置文件和环境变量
5. **Feign异常**：使用fallbackFactory记录异常日志
6. **循环依赖**：检查模块依赖关系，确保单向依赖
7. **日志冲突**：统一使用log4j2.xml配置
8. **权限问题**：确保日志目录写入权限

### 最佳实践
1. **模块化设计**：清晰的职责分离
2. **环境隔离**：独立配置文件
3. **版本管理**：统一依赖版本
4. **健康检查**：标准接口实现
5. **日志规范**：合理级别配置
6. **异常处理**：Feign使用fallbackFactory
7. **依赖控制**：避免不必要依赖
8. **API文档**：完整Swagger注释
9. **配置分离**：避免配置冲突
10. **权限管理**：正确目录权限

## 技术栈版本清单
- **JDK**：OpenJDK 17.0.2
- **Spring Boot**：3.2.6
- **Spring Cloud**：2023.0.4
- **MyBatis**：3.5.14（原生，禁止mybatis-spring-boot-starter）
- **MySQL驱动**：mysql-connector-j 8.0.33
- **HikariCP**：5.0.1
- **Log4j2**：2.21.1
- **SLF4J**：2.0.13
- **Jedis**：4.4.3
- **Apollo**：2.1.0
- **OpenAPI**：2.3.0
- **Hibernate Validator**：8.0.1.Final
- **Spring Cloud OpenFeign**：4.1.4

## .gitignore文件内容
```
**/target/
!.mvn/wrapper/maven-wrapper.jar
ai-summary
ai-summary/

### IDE ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
.vscode
.cursor
.cluade
.mcp.json
mcp.json
.idea
*.iws
*.iml
*.ipr

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
/build/

config-cache
config-cache/

### OS ###
.DS_Store
```