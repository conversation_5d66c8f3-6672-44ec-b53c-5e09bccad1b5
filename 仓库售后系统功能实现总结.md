# 仓库售后系统功能实现总结

## 1. 需求概述

根据用户需求，本次实现了以下仓库相关的售后系统功能：

1. **仓库次品自动申请售后**：商家货物寄到转运仓，仓库发现次品时自动申请售后
2. **一审同意后物流信息管理**：第三方供货平台一审同意退货后，推送信息到仓库，仓库填写物流信息
3. **仓库出库售后检查**：仓库出库时检查是否有在途售后，计算可出库件数
4. **MQ消息通知机制**：售后创建、退款、拒绝、关闭等状态变更通知仓库
5. **超时自动关闭**：仓库超时(7天)未回传物流信息，售后单自动关闭

## 2. 设计文档更新

### 2.1 核心业务场景新增
- 仓库次品自动申请售后流程
- 仓库物流信息管理流程  
- 仓库出库售后状态检查流程

### 2.2 时序图新增
- 仓库次品申请售后流程时序图
- 仓库物流信息管理流程时序图

### 2.3 状态管理扩展
- 仓库次品售后状态
- 仓库物流管理状态
- 售后申请人类型（用户申请/仓库申请）

### 2.4 数据库设计扩展
- `as_order`表新增仓库相关字段
- 新增`as_mq_message`表（MQ消息通知记录）


## 3. 系统代码实现

### 3.1 新增DTO/VO对象

#### 请求DTO
- `WarehouseAftersaleCreateRequest`：仓库创建售后单请求
- `WarehouseLogisticsInfoRequest`：仓库填写物流信息请求  
- `WarehouseOutboundCheckRequest`：仓库出库售后检查请求

#### 响应VO
- `WarehouseAftersaleCreateVO`：仓库创建售后单响应
- `WarehouseOutboundCheckVO`：仓库出库售后检查响应

### 3.2 新增Service接口和实现

#### WarehouseAftersaleService
- `createDefectAftersale()`：仓库创建次品售后申请
- `submitLogisticsInfo()`：仓库填写物流信息
- `checkOutboundAftersale()`：仓库出库售后检查
- `processTimeoutShippingOrders()`：处理超时未发货订单

#### AftersaleMqService  
- `sendAftersaleCreatedNotification()`：发送售后创建通知
- `sendAftersaleRefundNotification()`：发送售后退款通知
- `sendAftersaleRejectedNotification()`：发送售后拒绝通知
- `sendAftersaleClosedNotification()`：发送售后关闭通知
- `sendWarehouseShippingNotification()`：发送仓库发货通知
- `sendLogisticsInfoSubmittedNotification()`：发送物流信息已提交通知

### 3.3 新增Controller接口

#### WarehouseController
- `POST /api/warehouse/aftersale/create`：仓库创建次品售后申请
- `POST /api/warehouse/aftersale/logistics`：仓库填写物流信息
- `POST /api/warehouse/outbound/check`：仓库出库售后检查

### 3.4 新增数据访问层

#### 实体类
- `AsMqMessage`：MQ消息通知记录实体

#### Mapper接口
- `AsMqMessageMapper`：MQ消息记录数据访问
- `AsOrderMapper`新增方法：
  - `selectOngoingBySubOrderNo()`：查询在途售后订单
  - `selectTimeoutShippingOrders()`：查询超时未发货订单
  - `selectByWarehouseOperator()`：根据仓库操作员查询
  - `selectPendingWarehouseShipping()`：查询待仓库发货订单

### 3.5 工具类和定时任务

#### 工具类
- `ServiceNumberGenerator`：售后单号生成器

#### 定时任务
- `WarehouseTimeoutTask`：处理超时未发货订单（每天凌晨2点执行）

## 4. 核心业务流程

### 4.1 仓库次品申请售后流程
1. 仓库操作员发现次品，记录次品信息
2. 调用创建售后接口，系统验证权限和商品信息
3. 创建售后订单（申请人为仓库操作员）
4. 提交给第三方供货平台审核
5. 发送MQ消息通知仓库售后已创建
6. 供货平台审核通过后自动退款
7. 发送MQ消息通知仓库退款完成

### 4.2 仓库物流信息管理流程
1. 第三方供货平台一审同意退货
2. 系统推送信息到仓库，设置7天发货截止时间
3. 仓库按要求寄货给商家并填写物流信息
4. 系统将物流信息推送给第三方供货平台
5. 如超时未填写，系统自动关闭售后单并同步供货平台

### 4.3 仓库出库售后检查流程
1. 仓库执行出库操作前调用检查接口
2. 系统查询每个SKU的在途售后情况
3. 计算已退款数量和可出库数量
4. 返回检查结果，如有在途售后则不允许出库
5. 记录检查结果到数据库

## 5. MQ消息通知机制

### 5.1 消息类型
- 售后创建通知
- 售后退款通知  
- 售后拒绝通知
- 售后关闭通知
- 仓库发货通知
- 物流信息已提交通知

### 5.2 目标系统
- warehouse：仓库系统
- order：订单系统
- payment：支付系统

### 5.3 消息格式
```json
{
    "serviceNumber": "AS202312010001",
    "eventType": "aftersale_created",
    "applicantType": 2,
    "timestamp": "2023-12-01T10:30:00"
}
```

## 6. API接口示例

### 6.1 仓库创建售后单
```http
POST /api/warehouse/aftersale/create
{
    "subOrderNo": "SO202312010001",
    "skuId": 123456,
    "defectQuantity": 2,
    "defectReason": "商品破损",
    "defectDescription": "包装破损，商品受损",
    "defectImages": ["url1", "url2"],
    "operatorId": 789,
    "operatorName": "张三",
    "warehouseCode": "WH001"
}
```

### 6.2 仓库出库检查
```http
POST /api/warehouse/outbound/check
{
    "outboundOrderNo": "OUT202312010001",
    "items": [
        {
            "subOrderNo": "SO202312010001",
            "skuId": 123456,
            "requestQuantity": 10
        }
    ],
    "operatorId": 789,
    "operatorName": "王五"
}
```

## 7. 数据库表结构变更

### 7.1 as_order表新增字段
```sql
-- 申请人信息
applicant_type TINYINT DEFAULT 1 COMMENT '申请人类型:1-用户申请,2-仓库申请',
applicant_id BIGINT COMMENT '申请人ID(用户ID或仓库操作员ID)',
applicant_name VARCHAR(100) COMMENT '申请人姓名',

-- 仓库物流信息
warehouse_shipping_deadline DATETIME COMMENT '仓库发货截止时间',
warehouse_shipping_time DATETIME COMMENT '仓库实际发货时间',
warehouse_logistics_company VARCHAR(100) COMMENT '仓库使用的物流公司',
warehouse_tracking_number VARCHAR(100) COMMENT '仓库物流单号',
warehouse_logistics_info TEXT COMMENT '仓库物流详细信息(JSON格式)',
```

### 7.2 新增as_mq_message表
用于记录MQ消息发送状态和重试机制。



## 8. 注意事项

### 8.1 待完善功能
- 第三方供货平台接口对接（标记为TODO）
- 实际MQ中间件集成（标记为TODO）
- 仓库权限验证机制
- 异常情况处理和回滚机制

### 8.2 配置要求
- 需要配置定时任务执行
- 需要配置MQ连接信息
- 需要配置第三方供货平台接口地址

### 8.3 监控告警
- MQ消息发送失败告警
- 超时未发货订单告警
- 仓库出库检查异常告警

## 9. 部署说明

1. **数据库升级**：执行SQL脚本创建新表和字段
2. **应用部署**：部署包含新功能的应用版本
3. **配置更新**：更新MQ和定时任务相关配置
4. **接口测试**：验证仓库相关接口功能
5. **监控配置**：配置相关监控和告警

---

**实现完成时间**：2025年1月21日  
**实现人员**：AI助手  
**版本**：v1.0
