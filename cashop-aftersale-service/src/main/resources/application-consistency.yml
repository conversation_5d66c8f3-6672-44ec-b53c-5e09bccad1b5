# 事务一致性框架配置
mybatis:
  mapper-locations: classpath:com/mengxiang/transaction/framework/mapper/*.xml
  type-aliases-package: com.mengxiang.transaction.framework.dao

# 启用一致性任务
com.mengxiang.transaction.task.enable: true

# 任务配置
com.mengxiang.transaction.task.enableImmediatelyDelete: true
com.mengxiang.transaction.task.taskExpireTime: 7
com.mengxiang.transaction.task.deleteTaskStatus: SUCCESS

# 努力确保型任务线程池配置
com.mengxiang.transaction.task.insureCorePoolSize: 2
com.mengxiang.transaction.task.insureMaxPoolSize: 10
com.mengxiang.transaction.task.insureQueueCapacity: 1000

# 异常冲正型任务线程池配置
com.mengxiang.transaction.task.reversalCorePoolSize: 2
com.mengxiang.transaction.task.reversalMaxPoolSize: 10
com.mengxiang.transaction.task.reversalQueueCapacity: 1000

# 定时任务重试配置
com.mengxiang.transaction.task.retryFetchSize: 50
com.mengxiang.transaction.task.errorLogBeginTimes: 1

# 线程拒绝策略配置
com.mengxiang.transaction.task.insureRejectHandlerClass: java.util.concurrent.ThreadPoolExecutor$CallerRunsPolicy
com.mengxiang.transaction.task.reversalRejectHandlerClass: java.util.concurrent.ThreadPoolExecutor$AbortPolicy

# 重复key报错日志级别
com.mengxiang.transaction.task.duplicateKeyLogLevel: ERROR

# 分片调度数量
com.mengxiang.transaction.task.useShardingNum: 1
