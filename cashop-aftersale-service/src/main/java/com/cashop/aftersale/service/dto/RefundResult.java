package com.cashop.aftersale.service.dto;

import lombok.Data;

/**
 * 退款结果DTO
 * Service层 - 支付系统集成数据传输对象
 */
@Data
public class RefundResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 退款交易ID
     */
    private String refundTransactionId;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 退款状态
     */
    private String refundStatus;

    public boolean isSuccess() {
        return success;
    }
}
