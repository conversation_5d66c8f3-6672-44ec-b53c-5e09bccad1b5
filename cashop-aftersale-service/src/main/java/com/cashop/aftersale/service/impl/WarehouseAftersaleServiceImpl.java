package com.cashop.aftersale.service.impl;

import com.cashop.aftersale.common.exception.BusinessException;
import com.cashop.aftersale.common.utils.ServiceNumberGenerator;
import com.cashop.aftersale.dao.entity.AsOrder;
import com.cashop.aftersale.dao.entity.AsProduct;
import com.cashop.aftersale.dao.mapper.AsOrderMapper;
import com.cashop.aftersale.dao.mapper.AsProductMapper;
import com.cashop.aftersale.facade.dto.WarehouseAftersaleCreateRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsInfoRequest;
import com.cashop.aftersale.facade.dto.WarehouseOutboundCheckRequest;
import com.cashop.aftersale.facade.vo.WarehouseAftersaleCreateVO;
import com.cashop.aftersale.facade.vo.WarehouseOutboundCheckVO;
import com.cashop.aftersale.service.AftersaleMqService;
import com.cashop.aftersale.service.WarehouseAftersaleService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 仓库售后接口服务实现
 * 售后系统对仓库提供的接口服务实现，负责接收仓库请求并通知相关系统
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WarehouseAftersaleServiceImpl implements WarehouseAftersaleService {

    private final AsOrderMapper asOrderMapper;
    private final AsProductMapper asProductMapper;
    private final AftersaleMqService aftersaleMqService;
    private final ObjectMapper objectMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WarehouseAftersaleCreateVO createDefectAftersale(WarehouseAftersaleCreateRequest request) {
        log.info("接收仓库次品售后申请: subOrderNo={}, skuId={}, defectQuantity={}, operatorId={}", 
                request.getSubOrderNo(), request.getSkuId(), request.getDefectQuantity(), request.getOperatorId());

        try {
            // 1. 基础验证
            AsProduct product = asProductMapper.selectBySubOrderNoAndSkuId(request.getSubOrderNo(), request.getSkuId());
            if (product == null) {
                throw new BusinessException("未找到对应的商品信息");
            }

            // 2. 创建售后记录
            AsOrder asOrder = buildWarehouseAftersaleOrder(request, product);
            asOrderMapper.insert(asOrder);

            // 3. 通知第三方供货平台
            // TODO: 调用第三方供货平台接口
            // supplierIntegrationService.submitAftersaleApplication(asOrder);

            // 4. 发送MQ通知相关系统
            aftersaleMqService.sendAftersaleCreatedNotification(asOrder.getServiceNumber(), 2); // 2-仓库申请

            log.info("仓库次品售后申请接收成功: serviceNumber={}", asOrder.getServiceNumber());

            return WarehouseAftersaleCreateVO.builder()
                    .serviceNumber(asOrder.getServiceNumber())
                    .status("申请已接收")
                    .success(true)
                    .build();

        } catch (Exception e) {
            log.error("接收仓库次品售后申请失败: subOrderNo={}, skuId={}", 
                    request.getSubOrderNo(), request.getSkuId(), e);
            return WarehouseAftersaleCreateVO.builder()
                    .success(false)
                    .errorMessage(e.getMessage())
                    .build();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitLogisticsInfo(WarehouseLogisticsInfoRequest request) {
        log.info("接收仓库物流信息: serviceNumber={}, logisticsCompany={}, trackingNumber={}", 
                request.getServiceNumber(), request.getLogisticsCompany(), request.getTrackingNumber());

        try {
            // 1. 查询售后订单
            AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getServiceNumber());
            if (asOrder == null) {
                throw new BusinessException("售后订单不存在");
            }

            // 2. 更新物流信息
            asOrder.setWarehouseShippingTime(request.getShippingTime());
            asOrder.setWarehouseLogisticsCompany(request.getLogisticsCompany());
            asOrder.setWarehouseTrackingNumber(request.getTrackingNumber());
            asOrder.setWarehouseLogisticsInfo(objectMapper.writeValueAsString(request.getLogisticsInfo()));
            asOrder.setAsStatus(getShippedStatus()); // 更新为已发货状态
            asOrder.setUpdatedBy(request.getOperatorName());

            asOrderMapper.updateById(asOrder);

            // 3. 通知第三方供货平台
            // TODO: 推送物流信息给第三方供货平台
            // supplierIntegrationService.submitLogisticsInfo(asOrder, request);

            // 4. 发送MQ通知相关系统
            aftersaleMqService.sendLogisticsInfoSubmittedNotification(
                    request.getServiceNumber(), 
                    request.getLogisticsCompany(), 
                    request.getTrackingNumber()
            );

            log.info("仓库物流信息接收成功: serviceNumber={}", request.getServiceNumber());
            return true;

        } catch (Exception e) {
            log.error("接收仓库物流信息失败: serviceNumber={}", request.getServiceNumber(), e);
            throw new BusinessException("接收物流信息失败: " + e.getMessage());
        }
    }

    @Override
    public WarehouseOutboundCheckVO checkOutboundAftersale(WarehouseOutboundCheckRequest request) {
        log.info("提供售后状态查询服务: outboundOrderNo={}, itemCount={}", 
                request.getOutboundOrderNo(), request.getItems().size());

        List<WarehouseOutboundCheckVO.OutboundCheckResult> results = new ArrayList<>();

        // 只查询和返回售后状态信息，不记录检查结果
        for (WarehouseOutboundCheckRequest.OutboundCheckItem item : request.getItems()) {
            WarehouseOutboundCheckVO.OutboundCheckResult result = queryItemAftersaleStatus(item);
            results.add(result);
        }

        log.info("售后状态查询完成: outboundOrderNo={}, 仓库可根据返回信息自行决定出库", request.getOutboundOrderNo());

        return WarehouseOutboundCheckVO.builder()
                .canOutbound(true) // 售后系统只提供信息，不做出库决策
                .items(results)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processTimeoutShippingOrders() {
        log.info("开始处理超时售后单");

        try {
            // 1. 查询超时订单(7天未填写物流信息)
            LocalDateTime timeoutDeadline = LocalDateTime.now().minusDays(7);
            List<AsOrder> timeoutOrders = asOrderMapper.selectTimeoutShippingOrders(timeoutDeadline);

            if (CollectionUtils.isEmpty(timeoutOrders)) {
                log.info("没有发现超时售后单");
                return;
            }

            log.info("发现{}个超时售后单", timeoutOrders.size());

            // 2. 批量处理超时订单
            for (AsOrder order : timeoutOrders) {
                try {
                    // 更新状态为超时关闭
                    order.setAsStatus(getTimeoutClosedStatus());
                    order.setUpdatedBy("SYSTEM");
                    asOrderMapper.updateById(order);

                    // 通知第三方供货平台
                    // TODO: 同步第三方供货平台关单状态
                    // supplierIntegrationService.closeAftersaleOrder(order, "超时关闭");

                    // 发送MQ通知相关系统
                    aftersaleMqService.sendAftersaleClosedNotification(order.getServiceNumber(), "超时关闭");

                    log.info("处理超时售后单成功: serviceNumber={}", order.getServiceNumber());

                } catch (Exception e) {
                    log.error("处理超时售后单失败: serviceNumber={}", order.getServiceNumber(), e);
                }
            }

        } catch (Exception e) {
            log.error("处理超时售后单异常", e);
        }
    }

    /**
     * 构建仓库售后订单
     */
    private AsOrder buildWarehouseAftersaleOrder(WarehouseAftersaleCreateRequest request, AsProduct product) {
        AsOrder asOrder = new AsOrder();
        
        // 基础信息
        asOrder.setServiceNumber(ServiceNumberGenerator.generate());
        asOrder.setOrderNo(product.getOrderNo());
        asOrder.setSubOrderNo(request.getSubOrderNo());
        asOrder.setUserId(0L); // 仓库申请时用户ID为0
        
        // 售后类型和状态
        asOrder.setAsType(2); // 发货后退款
        asOrder.setAsStatus(1); // 申请中
        asOrder.setApplyReason(request.getDefectReason());
        
        // 申请信息
        asOrder.setApplyQuantity(request.getDefectQuantity());
        asOrder.setApplyAmount(product.getUnitPrice().multiply(new BigDecimal(request.getDefectQuantity())));
        asOrder.setCurrencyCode(product.getCurrencyCode());
        
        // 申请人信息
        asOrder.setApplicantType(2); // 仓库申请
        asOrder.setApplicantId(request.getOperatorId());
        asOrder.setApplicantName(request.getOperatorName());
        
        // 审计字段
        asOrder.setCreatedBy(request.getOperatorName());
        asOrder.setUpdatedBy(request.getOperatorName());
        
        return asOrder;
    }

    /**
     * 查询单个商品的售后状态
     */
    private WarehouseOutboundCheckVO.OutboundCheckResult queryItemAftersaleStatus(
            WarehouseOutboundCheckRequest.OutboundCheckItem item) {
        
        // 1. 查询商品信息
        AsProduct product = asProductMapper.selectBySubOrderNoAndSkuId(item.getSubOrderNo(), item.getSkuId());
        if (product == null) {
            return WarehouseOutboundCheckVO.OutboundCheckResult.builder()
                    .subOrderNo(item.getSubOrderNo())
                    .skuId(item.getSkuId())
                    .canOutbound(false)
                    .originalQuantity(0)
                    .ongoingAftersaleQuantity(0)
                    .refundedQuantity(0)
                    .availableQuantity(0)
                    .ongoingAftersales(new ArrayList<>())
                    .build();
        }

        // 2. 查询在途售后
        List<AsOrder> ongoingOrders = asOrderMapper.selectOngoingBySubOrderNo(item.getSubOrderNo());
        
        // 3. 计算数量
        int ongoingQuantity = ongoingOrders.stream()
                .mapToInt(AsOrder::getApplyQuantity)
                .sum();
        
        int refundedQuantity = product.getRefundedQuantity() != null ? product.getRefundedQuantity() : 0;
        int availableQuantity = product.getPurchaseQuantity() - ongoingQuantity - refundedQuantity;
        
        // 4. 构建在途售后信息
        List<WarehouseOutboundCheckVO.OngoingAftersaleInfo> ongoingAftersales = ongoingOrders.stream()
                .map(order -> WarehouseOutboundCheckVO.OngoingAftersaleInfo.builder()
                        .serviceNumber(order.getServiceNumber())
                        .asType(order.getAsType())
                        .asStatus(order.getAsStatus())
                        .applyQuantity(order.getApplyQuantity())
                        .applyAmount(order.getApplyAmount())
                        .reason("有在途售后申请")
                        .build())
                .collect(Collectors.toList());

        return WarehouseOutboundCheckVO.OutboundCheckResult.builder()
                .subOrderNo(item.getSubOrderNo())
                .skuId(item.getSkuId())
                .canOutbound(true) // 售后系统只提供信息，不做出库决策
                .originalQuantity(product.getPurchaseQuantity())
                .ongoingAftersaleQuantity(ongoingQuantity)
                .refundedQuantity(refundedQuantity)
                .availableQuantity(availableQuantity)
                .ongoingAftersales(ongoingAftersales)
                .build();
    }

    /**
     * 获取已发货状态码
     */
    private Integer getShippedStatus() {
        // 假设状态码9表示已发货
        return 9;
    }

    /**
     * 获取超时关闭状态码
     */
    private Integer getTimeoutClosedStatus() {
        // 假设状态码10表示超时关闭
        return 10;
    }
}