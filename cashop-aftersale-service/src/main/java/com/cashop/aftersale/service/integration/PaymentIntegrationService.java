package com.cashop.aftersale.service.integration;

import com.cashop.aftersale.facade.dto.PrePaymentRequest;
import com.cashop.aftersale.facade.dto.PrePaymentResponse;

import java.math.BigDecimal;

/**
 * 支付集成服务接口
 * 用于与支付系统进行交互
 */
public interface PaymentIntegrationService {
    
    /**
     * 创建预支付订单
     * 调用支付系统的预支付接口
     * @param request 预支付请求
     * @return 预支付响应
     */
    PrePaymentResponse createPrePayment(PrePaymentRequest request);
    
    /**
     * 查询支付状态
     * @param paymentTransactionId 支付交易ID
     * @return 支付状态 SUCCESS/FAILED/PROCESSING
     */
    String queryPaymentStatus(String paymentTransactionId);
    
    /**
     * 退还物流费用
     * @param serviceNumber 售后单号
     * @param amount 退款金额
     * @param currencyCode 币种代码
     * @return 退款是否成功
     */
    boolean refundLogisticsFee(String serviceNumber, BigDecimal amount, String currencyCode);
    
    /**
     * 创建物流费用支付订单
     * @param serviceNumber 售后单号
     * @param amount 支付金额
     * @param currencyCode 币种代码
     * @return 支付交易ID
     */
    String createLogisticsPayment(String serviceNumber, BigDecimal amount, String currencyCode);
}