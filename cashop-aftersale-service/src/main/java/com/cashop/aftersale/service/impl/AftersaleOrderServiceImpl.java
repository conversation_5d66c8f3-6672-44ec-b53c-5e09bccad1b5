package com.cashop.aftersale.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cashop.aftersale.common.exception.BusinessException;
import com.cashop.aftersale.common.utils.ServiceNumberGenerator;
import com.cashop.aftersale.dao.entity.AsOrder;
import com.cashop.aftersale.dao.entity.AsProduct;
import com.cashop.aftersale.dao.entity.AsRefundDetail;
import com.cashop.aftersale.dao.mapper.AsOrderMapper;
import com.cashop.aftersale.dao.mapper.AsProductMapper;
import com.cashop.aftersale.facade.dto.AftersaleApplyRequest;
import com.cashop.aftersale.facade.dto.LogisticsPaymentRequest;
import com.cashop.aftersale.facade.dto.PrePaymentRequest;
import com.cashop.aftersale.facade.dto.PrePaymentResponse;
import com.cashop.aftersale.facade.dto.PaymentCallbackRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsRequest;
import com.cashop.aftersale.facade.dto.ReturnServiceFeeRefundRequest;
import com.cashop.aftersale.facade.constants.AftersaleApiConstants;
import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.AftersaleOrderDetailVO;
import com.cashop.aftersale.facade.vo.RefundableProductVO;
import com.cashop.aftersale.facade.vo.ShippingFeeDetailVO;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;
import com.cashop.aftersale.service.AftersaleOrderService;
import com.cashop.aftersale.service.AsRefundDetailService;
import com.cashop.aftersale.service.dto.RefundDetailDTO;
import com.cashop.aftersale.common.constants.RefundDetailConstants;
import com.cashop.aftersale.common.constants.RefundTypeConstants;
import com.cashop.aftersale.service.integration.PaymentIntegrationService;
import com.cashop.aftersale.service.task.AftersaleApplySubmitTask;
import com.cashop.aftersale.service.task.PaymentCallbackProcessTask;
import com.mengxiang.transaction.framework.executor.InsurableTaskExecutor;
import com.mengxiang.transaction.framework.executor.ReversibleTaskExecutor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后订单业务服务实现
 * 业务层 - 核心业务逻辑实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class AftersaleOrderServiceImpl implements AftersaleOrderService {

    private final AsOrderMapper asOrderMapper;
    private final AsProductMapper asProductMapper;
    private final AsRefundDetailService asRefundDetailService;
    private final PaymentIntegrationService paymentIntegrationService;
    private final InsurableTaskExecutor insurableTaskExecutor;
    private final ReversibleTaskExecutor reversibleTaskExecutor;

    @Override
    public List<RefundableProductVO> getRefundableProducts(String subOrderNo, String currencyCode) {
        // TODO: 调用订单系统获取子订单商品信息
        // List<OrderProduct> orderProducts = orderService.getOrderProducts(subOrderNo);
        // 目前从本地as_product表查询
        
        List<AsProduct> products = asProductMapper.selectBySubOrderNo(subOrderNo);
        
        if (CollectionUtils.isEmpty(products)) {
            // TODO: 如果本地没有数据，从订单系统同步
            // products = syncProductsFromOrderSystem(subOrderNo);
            throw new BusinessException("未找到子订单商品信息，请先同步订单数据");
        }

        return products.stream()
                .filter(p -> p.getPurchaseQuantity() > p.getAppliedQuantity()) // 过滤可申请的商品
                .map(product -> convertToRefundableProductVO(product, currencyCode))
                .collect(Collectors.toList());
    }

    @Override
    public String createAftersaleOrder(AftersaleApplyRequest request) {
        log.info("创建售后申请: userId={}, orderNo={}, asType={}, applyQuantity={}", 
            request.getUserId(), request.getOrderNo(), request.getAsType(), request.getApplyQuantity());

        // 1. 验证售后申请条件
        validateAftersaleApply(request);

        // 2. 查询子订单商品信息
        List<AsProduct> products = asProductMapper.selectBySubOrderNo(request.getSubOrderNo());
        if (CollectionUtils.isEmpty(products)) {
            throw new BusinessException("未找到子订单商品信息");
        }

        // 3. 验证申请数量
        validateApplyQuantity(products, request.getApplyQuantity());

        // 4. 计算申请金额
        BigDecimal applyAmount = calculateApplyAmount(products, request.getApplyQuantity());

        // 5. 创建售后订单
        AsOrder asOrder = buildAsOrder(request, products, applyAmount);

        // 6. 如果是退货退款，计算物流费用
        if (request.getAsType() == 3) { // 发货后退货退款
            calculateAndSetLogisticsFee(asOrder, products, request.getApplyQuantity());
            asOrder.setAsStatus(2); // 待支付物流费用
        } else {
            asOrder.setAsStatus(1); // 申请中
        }

        asOrderMapper.insert(asOrder);

        // 7. 更新商品已申请数量
        updateProductAppliedQuantity(products, request.getApplyQuantity());

        // 8. TODO: 记录流程日志
        // asProcessLogService.addApplyLog(asOrder.getId(), request.getUserId().toString(), 
        //         "用户申请" + getAsTypeText(request.getAsType()));

        // 9. TODO: 发送申请成功通知
        // notificationService.sendApplySuccessNotification(asOrder);

        // 10. 使用一致性框架确保提交给供货平台
        if (request.getAsType() != 3) {
            // 使用努力确保型任务提交给供货平台
            AftersaleApplySubmitTask submitTask = new AftersaleApplySubmitTask(asOrder.getServiceNumber());
            insurableTaskExecutor.execute(submitTask);
            log.info("已提交售后申请任务到一致性框架: serviceNumber={}", asOrder.getServiceNumber());
        }

        log.info("售后申请创建成功: serviceNumber={}", asOrder.getServiceNumber());
        return asOrder.getServiceNumber();
    }

    @Override
    public ShippingFeeDetailVO getShippingFeeDetail(String serviceNumber) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        ShippingFeeDetailVO vo = new ShippingFeeDetailVO();
        BeanUtils.copyProperties(asOrder, vo);
        
        // 计算剩余支付时间
        if (asOrder.getLogisticsExpireTime() != null) {
            long remainingMinutes = java.time.temporal.ChronoUnit.MINUTES
                .between(LocalDateTime.now(), asOrder.getLogisticsExpireTime());
            vo.setRemainingMinutes(Math.max(0, (int) remainingMinutes));
        }

        // 设置支付状态文本
        vo.setPaidStatusText(getLogisticsPaidText(asOrder.getLogisticsPaid()));

        return vo;
    }

    @Override
    public PrePaymentResponse createPrePayment(PrePaymentRequest request) {
        log.info("创建预支付订单: serviceNumber={}, userId={}, amount={}, paymentMethod={}", 
                request.getServiceNumber(), request.getUserId(), request.getAmount(), request.getPaymentMethod());

        // 1. 验证售后订单状态
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getServiceNumber());
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        // 验证订单状态是否允许支付
        if (asOrder.getAsStatus() != 2) { // 2-待支付物流费用
            throw new BusinessException("当前订单状态不允许支付，状态：" + getAsStatusText(asOrder.getAsStatus()));
        }

        // 验证用户权限
        if (!asOrder.getUserId().equals(request.getUserId())) {
            throw new BusinessException("无权限操作此订单");
        }

        // 2. 验证支付金额
        if (request.getAmount().compareTo(asOrder.getTotalLogisticsFee()) != 0) {
            throw new BusinessException("支付金额不正确，应支付：" + asOrder.getTotalLogisticsFee() + " " + asOrder.getCurrencyCode());
        }

        // 验证币种
        if (!request.getCurrencyCode().equals(asOrder.getCurrencyCode())) {
            throw new BusinessException("币种不匹配，应使用：" + asOrder.getCurrencyCode());
        }

        // 3. 调用支付系统创建预支付订单
        try {
            PrePaymentResponse response = paymentIntegrationService.createPrePayment(request);
            
            // 4. 更新订单的预支付信息（可选，根据业务需要）
            // asOrder.setPrepayId(response.getPrepayId());
            // asOrder.setUpdatedBy(request.getUserId().toString());
            // asOrderMapper.updateById(asOrder);

            log.info("预支付订单创建成功: serviceNumber={}, prepayId={}, transactionId={}", 
                    request.getServiceNumber(), response.getPrepayId(), response.getTransactionId());

            return response;

        } catch (Exception e) {
            log.error("创建预支付订单失败: serviceNumber={}", request.getServiceNumber(), e);
            throw new BusinessException("创建预支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public boolean handlePaymentCallback(PaymentCallbackRequest request) {
        log.info("处理支付回调: businessOrderNo={}, paymentTradeNo={}, status={}", 
                request.getBusinessOrderNo(), request.getPaymentTradeNo(), request.getStatus());

        // 1. 验证售后订单存在 (businessOrderNo应该是售后单号)
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getBusinessOrderNo());
        if (asOrder == null) {
            log.error("支付回调处理失败: 售后订单不存在, businessOrderNo={}", request.getBusinessOrderNo());
            return false;
        }

        // 验证用户权限
        if (!asOrder.getUserId().equals(request.getUserId())) {
            log.error("支付回调处理失败: 用户权限不匹配, businessOrderNo={}, userId={}", 
                    request.getBusinessOrderNo(), request.getUserId());
            return false;
        }

        // 验证订单状态
        if (asOrder.getAsStatus() != 2) { // 2-待支付物流费用
            log.warn("支付回调处理: 订单状态不是待支付状态, businessOrderNo={}, status={}", 
                    request.getBusinessOrderNo(), asOrder.getAsStatus());
            return true; // 返回true避免重复回调
        }

        // 验证支付金额
        if (request.getPaidAmount() != null && asOrder.getTotalLogisticsFee() != null) {
            if (request.getPaidAmount().compareTo(asOrder.getTotalLogisticsFee()) != 0) {
                log.error("支付回调处理失败: 支付金额不匹配, businessOrderNo={}, paidAmount={}, expectedAmount={}", 
                        request.getBusinessOrderNo(), request.getPaidAmount(), asOrder.getTotalLogisticsFee());
                return false;
            }
        }

        // 2. 根据支付状态处理
        try {
            if ("SUCCESS".equals(request.getStatus()) || "PAID".equals(request.getStatus())) {
                // 支付成功处理
                return handlePaymentSuccess(asOrder, request);
            } else if ("FAILED".equals(request.getStatus()) || "FAIL".equals(request.getStatus())) {
                // 支付失败处理
                return handlePaymentFailed(asOrder, request);
            } else {
                log.warn("未知的支付状态: {}, businessOrderNo={}", request.getStatus(), request.getBusinessOrderNo());
                return false;
            }
        } catch (Exception e) {
            log.error("支付回调处理异常: businessOrderNo={}", request.getBusinessOrderNo(), e);
            return false;
        }
    }

    /**
     * 处理支付成功
     */
    private boolean handlePaymentSuccess(AsOrder asOrder, PaymentCallbackRequest request) {
        log.info("处理支付成功: serviceNumber={}, paymentTradeNo={}", 
                asOrder.getServiceNumber(), request.getPaymentTradeNo());

        // 更新订单支付状态
        asOrder.setLogisticsPaid(1); // 1-已支付
        asOrder.setPaymentTransactionId(request.getPaymentTradeNo());
        asOrder.setAsStatus(3); // 3-已支付，待审核
        asOrder.setUpdatedBy("payment_system");
        
        // 记录支付完成时间
        if (request.getFinishTime() != null) {
            asOrder.setLogisticsPayTime(request.getFinishTime());
        }
        
        asOrderMapper.updateById(asOrder);

        // TODO: 记录流程日志
        // asProcessLogService.addPaymentLog(asOrder.getId(), "payment_system", 
        //         "物流费用支付成功，交易ID：" + request.getPaymentTradeNo() + 
        //         "，支付金额：" + request.getPaidAmount() + " " + request.getCurrency());

        // 使用一致性框架提交给供货平台
        try {
            AftersaleApplySubmitTask submitTask = new AftersaleApplySubmitTask(asOrder.getServiceNumber());
            insurableTaskExecutor.execute(submitTask);
            log.info("已提交售后申请任务到一致性框架: serviceNumber={}", asOrder.getServiceNumber());
        } catch (Exception e) {
            log.error("提交一致性任务失败: serviceNumber={}", asOrder.getServiceNumber(), e);
            // 不抛异常，避免影响支付状态更新
        }

        // TODO: 发送支付成功通知
        // notificationService.sendPaymentSuccessNotification(asOrder);

        log.info("支付成功处理完成: serviceNumber={}", asOrder.getServiceNumber());
        return true;
    }

    /**
     * 处理支付失败
     */
    private boolean handlePaymentFailed(AsOrder asOrder, PaymentCallbackRequest request) {
        log.info("处理支付失败: serviceNumber={}, paymentTradeNo={}, status={}", 
                asOrder.getServiceNumber(), request.getPaymentTradeNo(), request.getStatus());

        // 订单状态保持不变，用户可以重新支付
        // 只记录支付失败信息
        // TODO: 记录流程日志
        // asProcessLogService.addPaymentLog(asOrder.getId(), "payment_system", 
        //         "物流费用支付失败，状态：" + request.getStatus() + 
        //         "，交易ID：''' + request.getPaymentTradeNo());

        // TODO: 发送支付失败通知
        // notificationService.sendPaymentFailedNotification(asOrder, "支付失败，状态：" + request.getStatus());

        log.info("支付失败处理完成: serviceNumber={}", asOrder.getServiceNumber());
        return true;
    }

    @Override
    public boolean updateWarehouseLogistics(WarehouseLogisticsRequest request) {
        log.info("仓库填写物流信息: serviceNumber={}, trackingNumber={}, valueAddedServiceFee={}", 
                request.getServiceNumber(), request.getWarehouseTrackingNumber(), request.getValueAddedServiceFee());

        // 1. 验证售后订单存在
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getServiceNumber());
        if (asOrder == null) {
            log.error("仓库填写物流信息失败: 售后订单不存在, serviceNumber={}", request.getServiceNumber());
            throw new BusinessException("售后订单不存在");
        }

        // 验证订单状态（应该是已支付状态）
        if (asOrder.getAsStatus() != 3) { // 3-已支付，待审核
            log.error("仓库填写物流信息失败: 订单状态不正确, serviceNumber={}, status={}", 
                    request.getServiceNumber(), asOrder.getAsStatus());
            throw new BusinessException("订单状态不允许填写物流信息，当前状态：" + getAsStatusText(asOrder.getAsStatus()));
        }

        try {
            // 2. 更新物流信息
            asOrder.setWarehouseLogisticsCompany(request.getWarehouseLogisticsCompany());
            asOrder.setWarehouseTrackingNumber(request.getWarehouseTrackingNumber());
            asOrder.setWarehouseShippingTime(request.getWarehouseShippingTime());
            asOrder.setWarehouseLogisticsInfo(request.getWarehouseLogisticsInfo());
            
            // 3. 更新增值服务费退款金额
            asOrder.setValueAddedServiceRefundAmount(request.getValueAddedServiceFee());
            
            // 4. 更新订单状态为仓库已发货
            asOrder.setAsStatus(4); // 4-供货平台处理中
            asOrder.setUpdatedBy(request.getOperatorId());
            
            asOrderMapper.updateById(asOrder);

            // 5. 记录增值服务费退款明细
            RefundDetailDTO refundDetail = RefundDetailDTO.builder()
                    .asOrderId(asOrder.getId())
                    .serviceNumber(request.getServiceNumber())
                    .refundType(RefundTypeConstants.VALUE_ADDED_SERVICE_REFUND)
                    .refundAmount(request.getValueAddedServiceFee())
                    .currencyCode(request.getCurrencyCode())
                    .refundReason("仓库作业完成，退还增值服务费")
                    .refundDescription(request.getValueAddedServiceDescription())
                    .createdBy(request.getOperatorId())
                    .build();
            
            asRefundDetailService.addRefundDetail(refundDetail);

            log.info("仓库物流信息填写成功: serviceNumber={}, valueAddedServiceFee={}", 
                    request.getServiceNumber(), request.getValueAddedServiceFee());
            
            return true;

        } catch (Exception e) {
            log.error("仓库填写物流信息异常: serviceNumber={}", request.getServiceNumber(), e);
            throw new BusinessException("填写物流信息失败: " + e.getMessage());
        }
    }

    @Override
    public boolean refundReturnServiceFee(ReturnServiceFeeRefundRequest request) {
        log.info("退还退货服务费: serviceNumber={}, userId={}, refundReasonType={}", 
                request.getServiceNumber(), request.getUserId(), request.getRefundReasonType());

        // 1. 验证售后订单存在
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getServiceNumber());
        if (asOrder == null) {
            log.error("退还退货服务费失败: 售后订单不存在, serviceNumber={}", request.getServiceNumber());
            throw new BusinessException("售后订单不存在");
        }

        // 验证用户权限
        if (!asOrder.getUserId().equals(request.getUserId())) {
            log.error("退还退货服务费失败: 用户权限不匹配, serviceNumber={}, userId={}", 
                    request.getServiceNumber(), request.getUserId());
            throw new BusinessException("无权限操作此订单");
        }

        // 验证是否已经退过服务费
        if (asOrder.getReturnServiceFeeRefundStatus() != null && asOrder.getReturnServiceFeeRefundStatus() == 1) {
            log.warn("退货服务费已退还: serviceNumber={}", request.getServiceNumber());
            return true; // 已退还，直接返回成功
        }

        // 验证是否有退货服务费可退
        if (asOrder.getReturnServiceFee() == null || asOrder.getReturnServiceFee().compareTo(BigDecimal.ZERO) <= 0) {
            log.error("退还退货服务费失败: 没有可退的服务费, serviceNumber={}", request.getServiceNumber());
            throw new BusinessException("没有可退的退货服务费");
        }

        try {
            // 2. 更新退货服务费退款状态
            asOrder.setReturnServiceFeeRefundStatus(1); // 1-已退款
            asOrder.setUpdatedBy(request.getOperatorId());
            asOrderMapper.updateById(asOrder);

            // 3. 记录退货服务费退款明细
            RefundDetailDTO refundDetail = RefundDetailDTO.builder()
                    .asOrderId(asOrder.getId())
                    .serviceNumber(request.getServiceNumber())
                    .refundType(RefundTypeConstants.RETURN_SERVICE_FEE_REFUND)
                    .refundAmount(asOrder.getReturnServiceFee())
                    .currencyCode(asOrder.getCurrencyCode())
                    .refundReason(request.getRefundReason())
                    .refundDescription("退还退货服务费：" + getRefundReasonTypeText(request.getRefundReasonType()))
                    .createdBy(request.getOperatorId())
                    .build();
            
            asRefundDetailService.addRefundDetail(refundDetail);

            // 4. 调用支付系统进行退款
            // TODO: 调用支付系统退款接口
            // paymentIntegrationService.refund(asOrder.getReturnServiceFee(), asOrder.getCurrencyCode(), 
            //                                 "退货服务费退款", request.getServiceNumber());

            log.info("退货服务费退还成功: serviceNumber={}, refundAmount={}", 
                    request.getServiceNumber(), asOrder.getReturnServiceFee());
            
            return true;

        } catch (Exception e) {
            log.error("退还退货服务费异常: serviceNumber={}", request.getServiceNumber(), e);
            throw new BusinessException("退还退货服务费失败: " + e.getMessage());
        }
    }

    /**
     * 获取退款原因类型文本
     */
    private String getRefundReasonTypeText(Integer refundReasonType) {
        if (refundReasonType == null) {
            return "未知原因";
        }
        switch (refundReasonType) {
            case 1:
                return "用户撤销售后申请";
            case 2:
                return "商家拒绝退货申请";
            default:
                return "未知原因";
        }
    }

    @Override
    public boolean payLogisticsFee(LogisticsPaymentRequest request) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(request.getServiceNumber());
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        // 验证订单状态
        if (asOrder.getAsStatus() != 2) { // 待支付物流费用
            throw new BusinessException("订单状态不正确，无法支付");
        }

        // 验证支付金额
        if (request.getPaymentAmount().compareTo(asOrder.getTotalLogisticsFee()) != 0) {
            throw new BusinessException("支付金额不正确");
        }

        // 验证支付时效
        if (asOrder.getLogisticsExpireTime() != null && 
            LocalDateTime.now().isAfter(asOrder.getLogisticsExpireTime())) {
            throw new BusinessException("支付已超时");
        }

        // TODO: 调用支付系统处理物流费用支付
        // PaymentRequest paymentRequest = buildPaymentRequest(request, asOrder);
        // PaymentResult paymentResult = paymentService.processLogisticsPayment(paymentRequest);
        // if (!paymentResult.isSuccess()) {
        //     throw new BusinessException("支付失败: " + paymentResult.getErrorMessage());
        // }
        // 模拟支付成功
        log.info("模拟支付物流费用成功: {}, 金额: {}", request.getServiceNumber(), request.getPaymentAmount());

        // 更新支付状态
        asOrder.setLogisticsPaid(1); // 已支付
        asOrder.setLogisticsPayTime(LocalDateTime.now());
        asOrder.setAsStatus(3); // 已提交供货平台
        asOrder.setUpdatedBy(request.getUserId().toString());
        asOrderMapper.updateById(asOrder);

        // TODO: 记录流程日志
        // asProcessLogService.addPaymentLog(asOrder.getId(), request.getUserId().toString(), 
        //         "用户支付物流费用：" + request.getPaymentAmount());

        // TODO: 发送支付成功通知
        // notificationService.sendPaymentSuccessNotification(asOrder);

        // 使用异常冲正型任务处理支付回调
        PaymentCallbackProcessTask callbackTask = new PaymentCallbackProcessTask(
            request.getServiceNumber(), 
            request.getPaymentTransactionId()
        );
        
        try {
            // 同步执行支付回调处理任务
            reversibleTaskExecutor.execute(callbackTask);
            log.info("支付回调处理任务执行成功: serviceNumber={}", request.getServiceNumber());
            
            // 使用努力确保型任务提交给供货平台
            AftersaleApplySubmitTask submitTask = new AftersaleApplySubmitTask(asOrder.getServiceNumber());
            insurableTaskExecutor.execute(submitTask);
            log.info("已提交售后申请任务到一致性框架: serviceNumber={}", asOrder.getServiceNumber());
            
        } catch (Exception e) {
            log.error("一致性任务执行失败: serviceNumber={}", request.getServiceNumber(), e);
            throw new BusinessException("支付处理失败: " + e.getMessage());
        }

        return true;
    }

    @Override
    public AftersaleOrderDetailVO getOrderDetail(String serviceNumber) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        AftersaleOrderDetailVO vo = new AftersaleOrderDetailVO();
        BeanUtils.copyProperties(asOrder, vo);
        
        // 设置枚举文本
        vo.setAsTypeText(getAsTypeText(asOrder.getAsType()));
        vo.setAsStatusText(getAsStatusText(asOrder.getAsStatus()));

        // TODO: 查询商品信息
        // List<AsProduct> products = asProductMapper.selectBySubOrderNo(asOrder.getSubOrderNo());
        // vo.setProducts(products.stream().map(this::convertToRefundableProductVO).collect(Collectors.toList()));

        // TODO: 查询流程记录
        // vo.setProcessLogs(asProcessLogService.getProcessLogs(asOrder.getId()));

        return vo;
    }

    @Override
    public IPage<AftersaleOrderDetailVO> getUserOrders(Long userId, Integer asStatus, Integer page, Integer size) {
        Page<AsOrder> pageParam = new Page<>(page, size);
        IPage<AsOrder> orderPage = asOrderMapper.selectPageByUserId(pageParam, userId, asStatus);
        
        return orderPage.convert(order -> {
            AftersaleOrderDetailVO vo = new AftersaleOrderDetailVO();
            BeanUtils.copyProperties(order, vo);
            
            // 设置状态文本
            vo.setAsStatusText(getAsStatusText(order.getAsStatus()));
            vo.setAsTypeText(getAsTypeText(order.getAsType()));
            
            return vo;
        });
    }

    @Override
    public boolean cancelAftersaleOrder(String serviceNumber, Long userId) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        // 验证用户权限
        if (!asOrder.getUserId().equals(userId)) {
            throw new BusinessException("无权限操作此订单");
        }

        // 验证订单状态
        if (asOrder.getAsStatus() != 1 && asOrder.getAsStatus() != 2) { // 申请中或待支付
            throw new BusinessException("当前状态不允许取消");
        }

        // 更新订单状态
        asOrder.setAsStatus(7); // 超时撤销
        asOrder.setUpdatedBy(userId.toString());
        asOrderMapper.updateById(asOrder);

        // TODO: 恢复商品可申请数量
        // restoreProductAppliedQuantity(asOrder.getSubOrderNo(), asOrder.getApplyQuantity());

        // TODO: 记录流程日志
        // asProcessLogService.addCancelLog(asOrder.getId(), userId.toString(), "用户取消申请");

        return true;
    }

    @Override
    public boolean processSupplierApproval(String serviceNumber, Integer approvedQuantity) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        // 更新批准信息
        asOrder.setApprovedQuantity(approvedQuantity);
        asOrder.setAsStatus(4); // 供货平台处理中
        asOrder.setUpdatedBy("system");
        asOrderMapper.updateById(asOrder);

        // TODO: 记录流程日志
        // asProcessLogService.addApprovalLog(asOrder.getId(), "supplier", 
        //         "供货平台审核通过，批准数量：" + approvedQuantity);

        return true;
    }

    @Override
    public boolean processActualRefund(String serviceNumber, Integer refundQuantity) {
        log.info("处理实际退款: serviceNumber={}, refundQuantity={}", serviceNumber, refundQuantity);

        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        // 1. 计算商品退款金额
        BigDecimal productRefundAmount = asOrder.getApplyAmount()
                .multiply(new BigDecimal(refundQuantity))
                .divide(new BigDecimal(asOrder.getApplyQuantity()), 2, BigDecimal.ROUND_HALF_UP);

        // 2. 计算服务费退款金额
        BigDecimal serviceFeeRefundAmount = BigDecimal.ZERO;
        if (asOrder.getUnitServiceFee() != null && asOrder.getUnitServiceFee().compareTo(BigDecimal.ZERO) > 0) {
            serviceFeeRefundAmount = asOrder.getUnitServiceFee().multiply(new BigDecimal(refundQuantity));
        }

        // 3. 总退款金额
        BigDecimal totalRefundAmount = productRefundAmount.add(serviceFeeRefundAmount);

        // 4. 创建商品退款明细
        RefundDetailDTO productRefundDetail = RefundDetailDTO.builder()
                .asOrderId(asOrder.getId())
                .serviceNumber(serviceNumber)
                .refundType(RefundDetailConstants.RefundType.PRODUCT_REFUND)
                .refundAmount(productRefundAmount)
                .currencyCode(asOrder.getCurrencyCode())
                .refundReason(RefundDetailConstants.DefaultRefundReason.PRODUCT_REFUND_REASON)
                .refundDescription(String.format("商品退款: 退款件数: %d, 退款金额: %s %s",
                        refundQuantity, productRefundAmount, asOrder.getCurrencyCode()))
                .refundQuantity(refundQuantity)
                .unitPrice(asOrder.getApplyAmount().divide(new BigDecimal(asOrder.getApplyQuantity()), 2, BigDecimal.ROUND_HALF_UP))
                .createdBy("system")
                .build();

        Long productRefundDetailId = asRefundDetailService.createProductRefundDetail(productRefundDetail);
        if (productRefundDetailId == null) {
            throw new BusinessException("创建商品退款明细失败");
        }

        // 5. 如果有服务费，创建服务费退款明细
        Long serviceFeeRefundDetailId = null;
        if (serviceFeeRefundAmount.compareTo(BigDecimal.ZERO) > 0) {
            RefundDetailDTO serviceFeeRefundDetail = RefundDetailDTO.builder()
                    .asOrderId(asOrder.getId())
                    .serviceNumber(serviceNumber)
                    .refundType(RefundDetailConstants.RefundType.SERVICE_FEE_REFUND)
                    .refundAmount(serviceFeeRefundAmount)
                    .currencyCode(asOrder.getCurrencyCode())
                    .refundReason(RefundDetailConstants.DefaultRefundReason.SERVICE_FEE_REFUND_REASON)
                    .refundDescription(String.format("服务费退款: 退款件数: %d, 单件服务费: %s %s",
                            refundQuantity, asOrder.getUnitServiceFee(), asOrder.getCurrencyCode()))
                    .serviceFeeQuantity(refundQuantity)
                    .unitServiceFee(asOrder.getUnitServiceFee())
                    .createdBy("system")
                    .build();

            serviceFeeRefundDetailId = asRefundDetailService.createServiceFeeRefundDetail(serviceFeeRefundDetail);
            if (serviceFeeRefundDetailId == null) {
                throw new BusinessException("创建服务费退款明细失败");
            }
        }

        // 6. 更新售后订单信息
        asOrder.setActualRefundQuantity(refundQuantity);
        asOrder.setActualRefundAmount(totalRefundAmount);
        asOrder.setAsStatus(5); // 已完成
        asOrder.setUpdatedBy("system");
        asOrderMapper.updateById(asOrder);

        // 7. TODO: 调用支付系统执行退款
        // 这里应该分别处理商品退款和服务费退款
        log.info("模拟退款成功: 用户ID={}, 商品退款={} {}, 服务费退款={} {}, 总退款={} {}, 售后单号={}",
            asOrder.getUserId(), productRefundAmount, asOrder.getCurrencyCode(),
            serviceFeeRefundAmount, asOrder.getCurrencyCode(),
            totalRefundAmount, asOrder.getCurrencyCode(), serviceNumber);

        // 8. 更新退款明细状态为成功
        asRefundDetailService.processRefundSuccess(productRefundDetailId, "MOCK_TXN_" + System.currentTimeMillis());
        if (serviceFeeRefundDetailId != null) {
            asRefundDetailService.processRefundSuccess(serviceFeeRefundDetailId, "MOCK_TXN_" + System.currentTimeMillis());
        }

        log.info("退款处理完成: serviceNumber={}, 商品退款明细ID={}, 服务费退款明细ID={}",
                serviceNumber, productRefundDetailId, serviceFeeRefundDetailId);

        return true;
    }

    @Override
    public void processTimeoutCancellation() {
        // TODO: 查询超时未支付的订单
        // List<AsOrder> expiredOrders = asOrderMapper.selectExpiredLogisticsPayment(
        //         2, // 待支付物流费用
        //         LocalDateTime.now(), 
        //         100);

        // for (AsOrder order : expiredOrders) {
        //     try {
        //         // 更新状态为超时撤销
        //         order.setAsStatus(7);
        //         order.setUpdatedBy("system");
        //         asOrderMapper.updateById(order);

        //         // 恢复商品可申请数量
        //         restoreProductAppliedQuantity(order.getSubOrderNo(), order.getApplyQuantity());

        //         // 记录流程日志
        //         asProcessLogService.addTimeoutLog(order.getId(), "system", "运费支付超时，系统自动撤销");

        //         log.info("处理超时撤销成功: {}", order.getServiceNumber());
        //     } catch (Exception e) {
        //         log.error("处理超时撤销失败: {}", order.getServiceNumber(), e);
        //     }
        // }
        
        log.info("TODO: 处理超时撤销任务");
    }

    @Override
    public boolean resendToSupplier(String serviceNumber) {
        AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
        if (asOrder == null) {
            throw new BusinessException("售后订单不存在");
        }

        try {
            // TODO: 重新提交给供货平台
            // submitToSupplier(asOrder);
            // asProcessLogService.addResendLog(asOrder.getId(), "admin", "管理员重新发送到供货平台");
            
            log.info("TODO: 重新发送到供货平台: {}", serviceNumber);
            return true;
        } catch (Exception e) {
            log.error("重新发送到供货平台失败: {}", serviceNumber, e);
            return false;
        }
    }

    // 私有辅助方法
    private RefundableProductVO convertToRefundableProductVO(AsProduct product, String currencyCode) {
        RefundableProductVO vo = new RefundableProductVO();
        BeanUtils.copyProperties(product, vo);
        
        // 设置可申请数量
        vo.setAvailableQuantity(product.getPurchaseQuantity() - product.getAppliedQuantity());
        
        // 格式化价格显示
        vo.setDisplayPrice(vo.getCurrencyCode() + " " + vo.getUnitPrice());
        
        // 设置状态文本
        vo.setStatusText(getProductStatusText(product.getProductStatus()));
        
        return vo;
    }

    private void validateAftersaleApply(AftersaleApplyRequest request) {
        // TODO: 调用订单系统验证订单状态和发货状态
        // OrderInfo orderInfo = orderService.getOrderInfo(request.getOrderNo());
        // validateOrderStatus(orderInfo, request.getAsType());
        
        log.info("TODO: 验证售后申请条件: orderNo={}, asType={}", request.getOrderNo(), request.getAsType());
    }

    private void validateApplyQuantity(List<AsProduct> products, Integer applyQuantity) {
        int totalPurchaseQuantity = products.stream()
                .mapToInt(AsProduct::getPurchaseQuantity)
                .sum();
        int totalAppliedQuantity = products.stream()
                .mapToInt(AsProduct::getAppliedQuantity)
                .sum();
        int availableQuantity = totalPurchaseQuantity - totalAppliedQuantity;

        if (applyQuantity > availableQuantity) {
            throw new BusinessException("申请数量超过可申请数量，可申请数量：" + availableQuantity);
        }
    }

    private BigDecimal calculateApplyAmount(List<AsProduct> products, Integer applyQuantity) {
        // 简化处理：假设同一子订单商品单价相同
        if (!products.isEmpty()) {
            return products.get(0).getUnitPrice().multiply(new BigDecimal(applyQuantity));
        }
        return BigDecimal.ZERO;
    }

    private AsOrder buildAsOrder(AftersaleApplyRequest request, List<AsProduct> products, BigDecimal applyAmount) {
        AsOrder asOrder = new AsOrder();
        asOrder.setServiceNumber(ServiceNumberGenerator.generate());
        asOrder.setOrderNo(request.getOrderNo());
        asOrder.setSubOrderNo(request.getSubOrderNo());
        asOrder.setUserId(request.getUserId());
        asOrder.setAsType(request.getAsType());
        asOrder.setApplyReason(request.getApplyReason());
        asOrder.setApplyQuantity(request.getApplyQuantity());
        asOrder.setApplyAmount(applyAmount);
        asOrder.setCurrencyCode(products.get(0).getCurrencyCode());

        // 计算订单服务费(按件收取)
        BigDecimal unitServiceFee = calculateUnitServiceFee(products);
        BigDecimal orderServiceFee = unitServiceFee.multiply(new BigDecimal(request.getApplyQuantity()));
        asOrder.setUnitServiceFee(unitServiceFee);
        asOrder.setOrderServiceFee(orderServiceFee);

        asOrder.setCreatedBy(request.getUserId().toString());
        return asOrder;
    }

    /**
     * 计算单件服务费
     * TODO: 实际应该从配置或订单系统获取服务费标准
     */
    private BigDecimal calculateUnitServiceFee(List<AsProduct> products) {
        // 这里可以根据商品类型、价格等因素计算服务费
        // 目前使用固定值，实际应该从配置中获取
        return new BigDecimal("2.00"); // 假设每件商品服务费为2.00
    }

    private void calculateAndSetLogisticsFee(AsOrder asOrder, List<AsProduct> products, Integer applyQuantity) {
        // TODO: 计算物流信息和运费
        // LogisticsInfo logisticsInfo = logisticsCalculationService
        //         .calculateLogisticsInfo(products, applyQuantity);
        // asOrder.setReturnVolume(logisticsInfo.getVolume());
        // asOrder.setReturnWeight(logisticsInfo.getWeight());

        // ShippingFeeInfo feeInfo = logisticsCalculationService
        //         .calculateShippingFee(logisticsInfo.getVolume(), logisticsInfo.getWeight());
        // asOrder.setShippingFee(feeInfo.getShippingFee());
        // asOrder.setServiceFee(feeInfo.getServiceFee());
        // asOrder.setTotalLogisticsFee(feeInfo.getTotalFee());
        
        // 模拟计算运费
        BigDecimal mockShippingFee = new BigDecimal("22.10");
        BigDecimal mockServiceFee = new BigDecimal("2.21");
        asOrder.setShippingFee(mockShippingFee);
        asOrder.setServiceFee(mockServiceFee);
        asOrder.setTotalLogisticsFee(mockShippingFee.add(mockServiceFee));
        asOrder.setLogisticsPaid(0); // 未支付
        
        // 设置支付截止时间(30分钟)
        asOrder.setLogisticsExpireTime(LocalDateTime.now().plusMinutes(30));
    }

    private void updateProductAppliedQuantity(List<AsProduct> products, Integer applyQuantity) {
        // 简化处理：平均分配到各商品
        int quantityPerProduct = applyQuantity / products.size();
        int remainder = applyQuantity % products.size();

        for (int i = 0; i < products.size(); i++) {
            AsProduct product = products.get(i);
            int addQuantity = quantityPerProduct + (i < remainder ? 1 : 0);
            product.setAppliedQuantity(product.getAppliedQuantity() + addQuantity);
            asProductMapper.updateById(product);
        }
    }

    private String getAsTypeText(Integer asType) {
        if (asType == null) return "未知";
        switch (asType) {
            case 1: return "发货前退款";
            case 2: return "发货后退款";
            case 3: return "发货后退货退款";
            default: return "未知";
        }
    }

    private String getAsStatusText(Integer asStatus) {
        if (asStatus == null) return "未知";
        switch (asStatus) {
            case 1: return "申请中";
            case 2: return "待支付物流费用";
            case 3: return "已提交供货平台";
            case 4: return "供货平台处理中";
            case 5: return "已完成";
            case 6: return "已拒绝";
            case 7: return "超时撤销";
            default: return "未知";
        }
    }

    private String getLogisticsPaidText(Integer logisticsPaid) {
        if (logisticsPaid == null) return "未知";
        switch (logisticsPaid) {
            case 0: return "未支付";
            case 1: return "已支付";
            case 2: return "已退还";
            default: return "未知";
        }
    }

    private String getProductStatusText(Integer status) {
        if (status == null) return "未知";
        switch (status) {
            case 1: return "正常";
            case 2: return "已下架";
            case 3: return "已删除";
            default: return "未知";
        }
    }

    @Override
    public SkuAftersaleSupportVO checkSkuAftersaleSupport(SkuAftersaleSupportRequest request) {
        log.info("批量检查SKU售后支持: subOrderNos={}, userId={}", 
                request.getSubOrderNos(), request.getUserId());

        List<SkuAftersaleSupportVO.SkuAftersaleSupportItem> items = new ArrayList<>();

        for (String subOrderNo : request.getSubOrderNos()) {
            try {
                SkuAftersaleSupportVO.SkuAftersaleSupportItem item = checkSingleSkuAftersaleSupport(subOrderNo, request.getUserId());
                items.add(item);
            } catch (Exception e) {
                log.error("检查SKU售后支持异常: subOrderNo={}", subOrderNo, e);
                items.add(buildErrorItem(subOrderNo, e.getMessage()));
            }
        }

        return SkuAftersaleSupportVO.builder()
                .items(items)
                .build();
    }

    /**
     * 检查单个SKU单的售后支持状态
     */
    private SkuAftersaleSupportVO.SkuAftersaleSupportItem checkSingleSkuAftersaleSupport(String subOrderNo, Long userId) {
        // 1. 查询子订单的商品信息（一个子订单对应一个SKU）
        List<AsProduct> products = asProductMapper.selectBySubOrderNo(subOrderNo);
        if (CollectionUtils.isEmpty(products)) {
            return buildNotFoundItem(subOrderNo);
        }

        // 取第一个商品（子订单通常只有一个SKU）
        AsProduct product = products.get(0);

        // 2. 查询该子订单的所有售后记录
        List<AsOrder> aftersaleOrders = asOrderMapper.selectBySubOrderNo(subOrderNo);

        // 3. 计算退款统计信息
        SkuAftersaleSupportVO.AftersaleStatistics statistics = calculateAftersaleStatistics(product, aftersaleOrders);

        // 4. 查询在途售后订单
        List<AsOrder> ongoingOrders = filterOngoingAftersales(aftersaleOrders);

        // 5. 判断售后支持状态
        Integer supportStatus = determineSupportStatus(product, statistics, ongoingOrders);

        // 6. 构建响应数据
        return buildAftersaleSupportItem(subOrderNo, product, statistics, ongoingOrders, supportStatus);
    }





    /**
     * 计算售后统计信息
     */
    private SkuAftersaleSupportVO.AftersaleStatistics calculateAftersaleStatistics(AsProduct product, List<AsOrder> aftersaleOrders) {
        // 计算已申请数量和已退款数量
        int appliedQuantity = product.getAppliedQuantity() != null ? product.getAppliedQuantity() : 0;
        int refundedQuantity = product.getRefundedQuantity() != null ? product.getRefundedQuantity() : 0;
        int availableQuantity = product.getPurchaseQuantity() - appliedQuantity;

        // 计算已退款总金额
        BigDecimal totalRefundedAmount = BigDecimal.ZERO;
        for (AsOrder order : aftersaleOrders) {
            if (order.getAsStatus() == 5) { // 已完成的售后
                List<AsRefundDetail> refundDetails = asRefundDetailService.getRefundDetailsByServiceNumber(order.getServiceNumber());
                for (AsRefundDetail detail : refundDetails) {
                    if (detail.getRefundStatus() == 3) { // 退款成功
                        totalRefundedAmount = totalRefundedAmount.add(detail.getRefundAmount());
                    }
                }
            }
        }

        // 计算可退款金额
        BigDecimal availableRefundAmount = product.getUnitPrice()
                .multiply(new BigDecimal(availableQuantity));

        return SkuAftersaleSupportVO.AftersaleStatistics.builder()
                .appliedQuantity(appliedQuantity)
                .refundedQuantity(refundedQuantity)
                .availableQuantity(availableQuantity)
                .totalRefundedAmount(totalRefundedAmount)
                .availableRefundAmount(availableRefundAmount)
                .build();
    }

    /**
     * 筛选在途售后订单
     */
    private List<AsOrder> filterOngoingAftersales(List<AsOrder> aftersaleOrders) {
        // 在途状态：1-申请中, 2-待支付物流费用, 3-已提交供货平台, 4-供货平台处理中
        List<Integer> ongoingStatuses = Arrays.asList(1, 2, 3, 4);
        return aftersaleOrders.stream()
                .filter(order -> ongoingStatuses.contains(order.getAsStatus()))
                .collect(Collectors.toList());
    }

    /**
     * 判断售后支持状态
     */
    private Integer determineSupportStatus(AsProduct product, SkuAftersaleSupportVO.AftersaleStatistics statistics, List<AsOrder> ongoingOrders) {
        // 有在途售后，不能继续申请
        if (!ongoingOrders.isEmpty()) {
            return AftersaleApiConstants.SupportStatus.HAS_ONGOING_AFTERSALE;
        }

        // 已全部退完，不支持申请
        if (statistics.getAvailableQuantity() <= 0) {
            return AftersaleApiConstants.SupportStatus.FULLY_REFUNDED;
        }

        // 已有售后申请（显示售后进度）- 有已完成的售后记录
        if (statistics.getAppliedQuantity() > 0) {
            return AftersaleApiConstants.SupportStatus.HAS_AFTERSALE_RECORD;
        }

        // 支持申请售后
        return AftersaleApiConstants.SupportStatus.SUPPORT_APPLY;
    }

    /**
     * 构建售后支持响应项
     */
    private SkuAftersaleSupportVO.SkuAftersaleSupportItem buildAftersaleSupportItem(String subOrderNo,
                                                                                   AsProduct product, 
                                                                                   SkuAftersaleSupportVO.AftersaleStatistics statistics,
                                                                                   List<AsOrder> ongoingOrders, 
                                                                                   Integer supportStatus) {
        // 构建商品信息
        SkuAftersaleSupportVO.ProductInfo productInfo = SkuAftersaleSupportVO.ProductInfo.builder()
                .skuId(product.getSkuId())
                .productName(product.getProductName())
                .skuName(product.getSkuName())
                .purchaseQuantity(product.getPurchaseQuantity())
                .unitPrice(product.getUnitPrice())
                .currencyCode(product.getCurrencyCode())
                .totalAmount(product.getTotalAmount())
                .build();

        // 构建在途售后信息
        List<SkuAftersaleSupportVO.OngoingAftersaleInfo> ongoingAftersales = ongoingOrders.stream()
                .map(order -> SkuAftersaleSupportVO.OngoingAftersaleInfo.builder()
                        .serviceNumber(order.getServiceNumber())
                        .asType(order.getAsType())
                        .asTypeDesc(getAsTypeText(order.getAsType()))
                        .asStatus(order.getAsStatus())
                        .asStatusDesc(getAsStatusText(order.getAsStatus()))
                        .applyQuantity(order.getApplyQuantity())
                        .applyAmount(order.getApplyAmount())
                        .build())
                .collect(Collectors.toList());

        boolean supportAftersale = supportStatus.equals(AftersaleApiConstants.SupportStatus.SUPPORT_APPLY);
        String unsupportedReason = supportAftersale ? null : getUnsupportedReason(supportStatus);

        return SkuAftersaleSupportVO.SkuAftersaleSupportItem.builder()
                .subOrderNo(subOrderNo)
                .supportAftersale(supportAftersale)
                .supportStatus(supportStatus)
                .statusMessage(getSupportStatusMessage(supportStatus))
                .unsupportedReason(unsupportedReason)
                .productInfo(productInfo)
                .aftersaleStatistics(statistics)
                .ongoingAftersales(ongoingAftersales)
                .build();
    }

    /**
     * 构建商品不存在响应项
     */
    private SkuAftersaleSupportVO.SkuAftersaleSupportItem buildNotFoundItem(String subOrderNo) {
        return SkuAftersaleSupportVO.SkuAftersaleSupportItem.builder()
                .subOrderNo(subOrderNo)
                .supportAftersale(false)
                .supportStatus(AftersaleApiConstants.SupportStatus.SYSTEM_ERROR)
                .statusMessage(AftersaleApiConstants.SupportStatusMessage.PRODUCT_NOT_FOUND)
                .unsupportedReason(AftersaleApiConstants.UnsupportedReason.PRODUCT_NOT_FOUND)
                .build();
    }

    /**
     * 构建错误响应项
     */
    private SkuAftersaleSupportVO.SkuAftersaleSupportItem buildErrorItem(String subOrderNo, String errorMessage) {
        return SkuAftersaleSupportVO.SkuAftersaleSupportItem.builder()
                .subOrderNo(subOrderNo)
                .supportAftersale(false)
                .supportStatus(AftersaleApiConstants.SupportStatus.SYSTEM_ERROR)
                .statusMessage(AftersaleApiConstants.SupportStatusMessage.SYSTEM_ERROR)
                .unsupportedReason(AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR + ": " + errorMessage)
                .build();
    }

    /**
     * 获取支持状态描述
     */
    private String getSupportStatusMessage(Integer supportStatus) {
        if (AftersaleApiConstants.SupportStatus.SUPPORT_APPLY.equals(supportStatus)) {
            return AftersaleApiConstants.SupportStatusMessage.SUPPORT_APPLY;
        } else if (AftersaleApiConstants.SupportStatus.HAS_AFTERSALE_RECORD.equals(supportStatus)) {
            return AftersaleApiConstants.SupportStatusMessage.HAS_AFTERSALE_RECORD;
        } else if (AftersaleApiConstants.SupportStatus.HAS_ONGOING_AFTERSALE.equals(supportStatus)) {
            return AftersaleApiConstants.SupportStatusMessage.HAS_ONGOING_AFTERSALE;
        } else if (AftersaleApiConstants.SupportStatus.FULLY_REFUNDED.equals(supportStatus)) {
            return AftersaleApiConstants.SupportStatusMessage.FULLY_REFUNDED;
        } else if (AftersaleApiConstants.SupportStatus.SYSTEM_ERROR.equals(supportStatus)) {
            return AftersaleApiConstants.SupportStatusMessage.SYSTEM_ERROR;
        } else {
            return "未知状态";
        }
    }

    /**
     * 获取不支持申请售后的原因说明
     */
    private String getUnsupportedReason(Integer supportStatus) {
        if (AftersaleApiConstants.SupportStatus.HAS_ONGOING_AFTERSALE.equals(supportStatus)) {
            return AftersaleApiConstants.UnsupportedReason.HAS_ONGOING_AFTERSALE;
        } else if (AftersaleApiConstants.SupportStatus.FULLY_REFUNDED.equals(supportStatus)) {
            return AftersaleApiConstants.UnsupportedReason.FULLY_REFUNDED;
        } else if (AftersaleApiConstants.SupportStatus.SYSTEM_ERROR.equals(supportStatus)) {
            return AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR;
        } else if (AftersaleApiConstants.SupportStatus.HAS_AFTERSALE_RECORD.equals(supportStatus)) {
            return null; // 有售后记录但可以查看进度，不算不支持
        } else {
            return AftersaleApiConstants.UnsupportedReason.SYSTEM_ERROR;
        }
    }
}
