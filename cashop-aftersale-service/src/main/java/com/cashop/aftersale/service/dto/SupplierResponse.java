package com.cashop.aftersale.service.dto;

import lombok.Data;

/**
 * 供货平台响应DTO
 * Service层 - 供货平台集成数据传输对象
 */
@Data
public class SupplierResponse {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 供货平台售后单号
     */
    private String supplierAsNo;

    /**
     * 处理状态
     */
    private String status;

    /**
     * 批准数量
     */
    private Integer approvedQuantity;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 备注信息
     */
    private String remark;

    public boolean isSuccess() {
        return success;
    }
}
