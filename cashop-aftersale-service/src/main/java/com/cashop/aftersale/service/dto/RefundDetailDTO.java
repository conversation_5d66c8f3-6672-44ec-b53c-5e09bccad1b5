package com.cashop.aftersale.service.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款明细DTO
 * Service层 - 退款明细数据传输对象
 */
@Data
@Builder
public class RefundDetailDTO {

    /**
     * 售后订单ID
     */
    private Long asOrderId;

    /**
     * 售后单号
     */
    private String serviceNumber;

    /**
     * 退款类型:1-商品退款,2-服务费退款,3-运费退款
     */
    private Integer refundType;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款明细描述
     */
    private String refundDescription;

    /**
     * 商品相关信息(仅商品退款时填写)
     */
    private Long productId;
    private Long skuId;
    private String productName;
    private Integer refundQuantity;
    private BigDecimal unitPrice;

    /**
     * 服务费相关信息(仅服务费退款时填写)
     */
    private Integer serviceFeeQuantity;
    private BigDecimal unitServiceFee;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
