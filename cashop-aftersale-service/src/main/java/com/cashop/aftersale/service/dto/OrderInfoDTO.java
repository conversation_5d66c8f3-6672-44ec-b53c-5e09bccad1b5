package com.cashop.aftersale.service.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 订单信息DTO
 * Service层 - 订单系统集成数据传输对象
 */
@Data
public class OrderInfoDTO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 发货状态
     */
    private Integer deliveryStatus;

    /**
     * 签收状态
     */
    private Integer receiveStatus;

    /**
     * 订单创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 发货时间
     */
    private LocalDateTime deliveryTime;
}
