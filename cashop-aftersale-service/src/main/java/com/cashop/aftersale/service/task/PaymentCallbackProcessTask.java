package com.cashop.aftersale.service.task;

import com.mengxiang.transaction.framework.task.ReversibleTask;
import com.mengxiang.transaction.framework.task.TaskExecuteResult;
import com.mengxiang.transaction.framework.enums.TaskExecuteStatusEnum;
import com.mengxiang.transaction.framework.enums.TaskRetryStrategyEnum;
import com.mengxiang.transaction.framework.enums.TransactionStatusEnum;
import com.cashop.aftersale.service.integration.PaymentIntegrationService;
import com.cashop.aftersale.dao.mapper.AsOrderMapper;
import com.cashop.aftersale.dao.entity.AsOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 支付回调处理任务
 * 异常冲正型任务 - 处理支付回调，失败时进行冲正
 * 注意：此类不应该被Spring管理，因为需要动态传入参数
 */
@Slf4j
public class PaymentCallbackProcessTask extends ReversibleTask<TaskExecuteResult> {
    
    private final String serviceNumber;
    private final String paymentTransactionId;
    
    @Autowired
    private PaymentIntegrationService paymentIntegrationService;
    
    @Autowired
    private AsOrderMapper asOrderMapper;
    
    public PaymentCallbackProcessTask(String serviceNumber, String paymentTransactionId) {
        this.serviceNumber = serviceNumber;
        this.paymentTransactionId = paymentTransactionId;
    }
    
    @Override
    public String getTaskType() {
        return "PAYMENT_CALLBACK_PROCESS";
    }
    
    @Override
    public String getTaskId() {
        return serviceNumber + "_" + paymentTransactionId;
    }
    
    @Override
    public TaskRetryStrategyEnum getRetryStrategy() {
        return TaskRetryStrategyEnum.INCREASING_INTERVAL;
    }
    
    @Override
    public TaskExecuteResult doExecute() {
        log.info("开始处理支付回调: serviceNumber={}, paymentTransactionId={}", 
            serviceNumber, paymentTransactionId);
        
        try {
            // 1. 查询支付状态
            String paymentStatus = paymentIntegrationService.queryPaymentStatus(paymentTransactionId);
            
            // 2. 更新售后订单状态
            AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
            if (asOrder == null) {
                throw new RuntimeException("售后订单不存在: " + serviceNumber);
            }
            
            TaskExecuteResult result = new TaskExecuteResult();
            
            if ("SUCCESS".equals(paymentStatus)) {
                // 支付成功，更新订单状态
                asOrder.setAsStatus(3); // 已支付，待处理
                asOrder.setPaymentTransactionId(paymentTransactionId);
                asOrderMapper.updateById(asOrder);
                
                result.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
                result.setErrorMessage("支付回调处理成功");
                log.info("支付回调处理成功: serviceNumber={}", serviceNumber);
                
            } else if ("FAILED".equals(paymentStatus)) {
                // 支付失败
                result.setExecuteStatus(TaskExecuteStatusEnum.FAILED);
                result.setErrorMessage("支付失败");
                log.warn("支付失败: serviceNumber={}", serviceNumber);
                
            } else {
                // 支付状态未知，继续重试
                result.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
                result.setErrorMessage("支付状态未知: " + paymentStatus);
                log.warn("支付状态未知: serviceNumber={}, status={}", serviceNumber, paymentStatus);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("支付回调处理异常: serviceNumber={}", serviceNumber, e);
            
            TaskExecuteResult result = new TaskExecuteResult();
            result.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
            result.setErrorMessage("处理异常: " + e.getMessage());
            
            return result;
        }
    }
    
    @Override
    public TaskExecuteResult doReversal() {
        log.info("开始执行支付回调冲正: serviceNumber={}, paymentTransactionId={}", 
            serviceNumber, paymentTransactionId);
        
        try {
            // 1. 恢复订单状态
            AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
            if (asOrder != null) {
                asOrder.setAsStatus(2); // 恢复到待支付状态
                asOrder.setPaymentTransactionId(null);
                asOrderMapper.updateById(asOrder);
            }
            
            // 2. 如果支付成功了，需要退款
            String paymentStatus = paymentIntegrationService.queryPaymentStatus(paymentTransactionId);
            if ("SUCCESS".equals(paymentStatus)) {
                boolean refundSuccess = paymentIntegrationService.refundLogisticsFee(
                    serviceNumber, asOrder.getLogisticsFeeAmount(), asOrder.getCurrencyCode());
                
                if (!refundSuccess) {
                    log.error("冲正退款失败: serviceNumber={}, paymentTransactionId={}", 
                        serviceNumber, paymentTransactionId);
                }
            }
            
            TaskExecuteResult result = new TaskExecuteResult();
            result.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
            result.setErrorMessage("支付回调冲正成功");
            
            log.info("支付回调冲正成功: serviceNumber={}", serviceNumber);
            return result;
            
        } catch (Exception e) {
            log.error("支付回调冲正异常: serviceNumber={}", serviceNumber, e);
            
            TaskExecuteResult result = new TaskExecuteResult();
            result.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
            result.setErrorMessage("冲正异常: " + e.getMessage());
            
            return result;
        }
    }
    
    @Override
    public TransactionStatusEnum queryBizStatus() {
        try {
            // 查询业务状态，判断是否需要提交或回滚
            AsOrder asOrder = asOrderMapper.selectByServiceNumber(serviceNumber);
            if (asOrder == null) {
                return TransactionStatusEnum.ROLLBACK;
            }
            
            // 如果订单状态是已支付，则提交
            if (asOrder.getAsStatus() == 3) {
                return TransactionStatusEnum.COMMIT;
            }
            
            // 其他情况回滚
            return TransactionStatusEnum.ROLLBACK;
            
        } catch (Exception e) {
            log.error("查询业务状态异常: serviceNumber={}", serviceNumber, e);
            return TransactionStatusEnum.ROLLBACK;
        }
    }
    
    @Override
    public void rebuild(com.mengxiang.transaction.framework.dao.TransactionTaskLogDO taskLogDO) {
        // 从任务日志重建任务状态
        // 这里可以根据需要实现具体的重建逻辑
        log.info("重建支付回调处理任务: serviceNumber={}, paymentTransactionId={}, taskId={}", 
            serviceNumber, paymentTransactionId, taskLogDO.getTaskId());
    }
}
