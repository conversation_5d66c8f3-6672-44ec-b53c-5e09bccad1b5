package com.cashop.aftersale.service.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单商品DTO
 * Service层 - 订单系统集成数据传输对象
 */
@Data
public class OrderProductDTO {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long skuId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品描述
     */
    private String productDescription;

    /**
     * 商品图片URL
     */
    private String productImageUrl;

    /**
     * SKU名称
     */
    private String skuName;

    /**
     * 规格属性(JSON格式)
     */
    private String skuAttributes;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 购买数量
     */
    private Integer purchaseQuantity;

    /**
     * 商品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 单件体积
     */
    private BigDecimal unitVolume;

    /**
     * 单件重量
     */
    private BigDecimal unitWeight;
}
