package com.cashop.aftersale.service;

/**
 * 第三方消息处理服务接口
 * 业务层 - 消息处理业务逻辑
 */
public interface ThirdPartyMessageService {

    /**
     * 接收第三方消息
     * 
     * @param rawMessage 原始消息
     */
    void receiveMessage(String rawMessage);

    /**
     * 异步消费消息
     * 
     * @param messageId 消息ID
     */
    void consumeMessageAsync(Long messageId);

    /**
     * 重试失败消息
     */
    void retryFailedMessages();

    /**
     * 手动重试消息
     * 
     * @param messageId 消息ID
     * @return 重试结果
     */
    boolean retryMessage(Long messageId);
}
