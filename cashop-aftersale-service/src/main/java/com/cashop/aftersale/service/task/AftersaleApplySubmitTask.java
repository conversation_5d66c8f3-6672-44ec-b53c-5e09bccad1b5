package com.cashop.aftersale.service.task;

import com.mengxiang.transaction.framework.task.InsurableTask;
import com.mengxiang.transaction.framework.task.TaskExecuteResult;
import com.mengxiang.transaction.framework.enums.TaskExecuteStatusEnum;
import com.cashop.aftersale.service.integration.SupplierIntegrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 售后申请提交任务
 * 努力确保型任务 - 确保售后申请成功提交到供货平台
 * 注意：此类不应该被Spring管理，因为需要动态传入serviceNumber参数
 */
@Slf4j
public class AftersaleApplySubmitTask extends InsurableTask<TaskExecuteResult> {
    
    private final String serviceNumber;
    
    @Autowired
    private SupplierIntegrationService supplierIntegrationService;
    
    public AftersaleApplySubmitTask(String serviceNumber) {
        this.serviceNumber = serviceNumber;
    }
    
    @Override
    public String getTaskType() {
        return "AFTERSALE_APPLY_SUBMIT";
    }
    
    @Override
    public String getTaskId() {
        return serviceNumber;
    }
    
    @Override
    public TaskExecuteResult doExecute() {
        log.info("开始执行售后申请提交任务: serviceNumber={}", serviceNumber);
        
        try {
            // 调用供货平台接口提交售后申请
            boolean success = supplierIntegrationService.submitAftersaleApply(serviceNumber);
            
            TaskExecuteResult result = new TaskExecuteResult();
            if (success) {
                result.setExecuteStatus(TaskExecuteStatusEnum.SUCCESS);
                result.setErrorMessage("售后申请提交成功");
                log.info("售后申请提交成功: serviceNumber={}", serviceNumber);
            } else {
                result.setExecuteStatus(TaskExecuteStatusEnum.FAILED);
                result.setErrorMessage("供货平台返回失败");
                log.warn("售后申请提交失败: serviceNumber={}", serviceNumber);
            }
            
            return result;
            
        } catch (Exception e) {
            log.error("售后申请提交异常: serviceNumber={}", serviceNumber, e);
            
            TaskExecuteResult result = new TaskExecuteResult();
            result.setExecuteStatus(TaskExecuteStatusEnum.EXCEPTION);
            result.setErrorMessage("提交异常: " + e.getMessage());
            
            return result;
        }
    }
    
    @Override
    public void rebuild(com.mengxiang.transaction.framework.dao.TransactionTaskLogDO taskLogDO) {
        // 从任务日志重建任务状态
        // 这里可以根据需要实现具体的重建逻辑
        log.info("重建售后申请提交任务: serviceNumber={}, taskId={}", serviceNumber, taskLogDO.getTaskId());
    }
}
