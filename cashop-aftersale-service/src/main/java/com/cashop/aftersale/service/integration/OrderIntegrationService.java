package com.cashop.aftersale.service.integration;

import com.cashop.aftersale.service.dto.OrderInfoDTO;
import com.cashop.aftersale.service.dto.OrderProductDTO;

import java.util.List;

/**
 * 订单系统集成服务
 * 业务层 - 第三方服务调用
 */
public interface OrderIntegrationService {

    /**
     * TODO: 获取订单信息
     * 待订单系统提供接口后实现
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    OrderInfoDTO getOrderInfo(String orderNo);

    /**
     * TODO: 获取订单商品信息
     * 待订单系统提供接口后实现
     * 
     * @param subOrderNo 子订单号
     * @return 商品信息列表
     */
    List<OrderProductDTO> getOrderProducts(String subOrderNo);

    /**
     * TODO: 验证订单状态
     * 待订单系统提供接口后实现
     * 
     * @param orderNo 订单号
     * @param asType 售后类型
     * @return 验证结果
     */
    boolean validateOrderStatus(String orderNo, Integer asType);

    /**
     * TODO: 获取用户订单权限
     * 待订单系统提供接口后实现
     * 
     * @param userId 用户ID
     * @param orderNo 订单号
     * @return 权限验证结果
     */
    boolean validateUserOrderPermission(Long userId, String orderNo);
}
