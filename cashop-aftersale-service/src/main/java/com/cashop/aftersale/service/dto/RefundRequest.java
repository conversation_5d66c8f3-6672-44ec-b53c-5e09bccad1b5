package com.cashop.aftersale.service.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 退款请求DTO
 * Service层 - 支付系统集成数据传输对象
 */
@Data
@Builder
public class RefundRequest {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 退款金额
     */
    private BigDecimal amount;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 业务单号(售后单号)
     */
    private String serviceNumber;

    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 原交易ID
     */
    private String originalTransactionId;
}
