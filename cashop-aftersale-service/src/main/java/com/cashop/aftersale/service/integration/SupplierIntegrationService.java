package com.cashop.aftersale.service.integration;

/**
 * 供货平台集成服务接口
 * 用于与第三方供货平台进行交互
 */
public interface SupplierIntegrationService {
    
    /**
     * 提交售后申请到供货平台
     * @param serviceNumber 售后单号
     * @return 提交是否成功
     */
    boolean submitAftersaleApply(String serviceNumber);
    
    /**
     * 查询供货平台售后处理状态
     * @param serviceNumber 售后单号
     * @return 处理状态
     */
    String queryAftersaleStatus(String serviceNumber);
    
    /**
     * 同步供货平台状态
     * @param serviceNumber 售后单号
     * @param status 状态
     * @return 同步是否成功
     */
    boolean syncSupplierStatus(String serviceNumber, String status);
}