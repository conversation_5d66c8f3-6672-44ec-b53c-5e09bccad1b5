package com.cashop.aftersale.service.impl;

import com.cashop.aftersale.dao.entity.AsRefundDetail;
import com.cashop.aftersale.dao.mapper.AsRefundDetailMapper;
import com.cashop.aftersale.service.AsRefundDetailService;
import com.cashop.aftersale.service.dto.RefundDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 售后退款明细Service实现类
 * Service层 - 退款明细业务逻辑实现
 */
@Slf4j
@Service
public class AsRefundDetailServiceImpl implements AsRefundDetailService {

    @Autowired
    private AsRefundDetailMapper asRefundDetailMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addRefundDetail(RefundDetailDTO refundDetailDTO) {
        log.info("添加退款明细, serviceNumber: {}, refundType: {}, refundAmount: {}", 
                refundDetailDTO.getServiceNumber(), refundDetailDTO.getRefundType(), refundDetailDTO.getRefundAmount());
        
        AsRefundDetail refundDetail = new AsRefundDetail();
        BeanUtils.copyProperties(refundDetailDTO, refundDetail);
        refundDetail.setRefundStatus(1); // 默认待退款状态
        refundDetail.setCreatedTime(LocalDateTime.now());
        
        int result = asRefundDetailMapper.insert(refundDetail);
        if (result > 0) {
            log.info("退款明细创建成功, id: {}", refundDetail.getId());
            return refundDetail.getId();
        }
        
        log.error("退款明细创建失败");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createProductRefundDetail(RefundDetailDTO refundDetailDTO) {
        log.info("创建商品退款明细, serviceNumber: {}, productId: {}, refundAmount: {}", 
                refundDetailDTO.getServiceNumber(), refundDetailDTO.getProductId(), refundDetailDTO.getRefundAmount());
        
        AsRefundDetail refundDetail = new AsRefundDetail();
        BeanUtils.copyProperties(refundDetailDTO, refundDetail);
        refundDetail.setRefundType(1); // 商品退款
        refundDetail.setRefundStatus(1); // 待退款
        refundDetail.setCreatedBy(refundDetailDTO.getCreatedBy());
        
        int result = asRefundDetailMapper.insert(refundDetail);
        if (result > 0) {
            log.info("商品退款明细创建成功, id: {}", refundDetail.getId());
            return refundDetail.getId();
        }
        
        log.error("商品退款明细创建失败");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createServiceFeeRefundDetail(RefundDetailDTO refundDetailDTO) {
        log.info("创建服务费退款明细, serviceNumber: {}, serviceFeeQuantity: {}, refundAmount: {}", 
                refundDetailDTO.getServiceNumber(), refundDetailDTO.getServiceFeeQuantity(), refundDetailDTO.getRefundAmount());
        
        AsRefundDetail refundDetail = new AsRefundDetail();
        BeanUtils.copyProperties(refundDetailDTO, refundDetail);
        refundDetail.setRefundType(2); // 服务费退款
        refundDetail.setRefundStatus(1); // 待退款
        refundDetail.setCreatedBy(refundDetailDTO.getCreatedBy());
        
        int result = asRefundDetailMapper.insert(refundDetail);
        if (result > 0) {
            log.info("服务费退款明细创建成功, id: {}", refundDetail.getId());
            return refundDetail.getId();
        }
        
        log.error("服务费退款明细创建失败");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createShippingFeeRefundDetail(RefundDetailDTO refundDetailDTO) {
        log.info("创建运费退款明细, serviceNumber: {}, refundAmount: {}", 
                refundDetailDTO.getServiceNumber(), refundDetailDTO.getRefundAmount());
        
        AsRefundDetail refundDetail = new AsRefundDetail();
        BeanUtils.copyProperties(refundDetailDTO, refundDetail);
        refundDetail.setRefundType(3); // 运费退款
        refundDetail.setRefundStatus(1); // 待退款
        refundDetail.setCreatedBy(refundDetailDTO.getCreatedBy());
        
        int result = asRefundDetailMapper.insert(refundDetail);
        if (result > 0) {
            log.info("运费退款明细创建成功, id: {}", refundDetail.getId());
            return refundDetail.getId();
        }
        
        log.error("运费退款明细创建失败");
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Long> batchCreateRefundDetails(List<RefundDetailDTO> refundDetailDTOs) {
        if (CollectionUtils.isEmpty(refundDetailDTOs)) {
            return new ArrayList<>();
        }
        
        log.info("批量创建退款明细, 数量: {}", refundDetailDTOs.size());
        
        List<AsRefundDetail> refundDetails = refundDetailDTOs.stream()
                .map(dto -> {
                    AsRefundDetail detail = new AsRefundDetail();
                    BeanUtils.copyProperties(dto, detail);
                    detail.setRefundStatus(1); // 待退款
                    return detail;
                })
                .collect(Collectors.toList());
        
        int result = asRefundDetailMapper.batchInsert(refundDetails);
        if (result > 0) {
            List<Long> ids = refundDetails.stream()
                    .map(AsRefundDetail::getId)
                    .collect(Collectors.toList());
            log.info("批量创建退款明细成功, ids: {}", ids);
            return ids;
        }
        
        log.error("批量创建退款明细失败");
        return new ArrayList<>();
    }

    @Override
    public List<AsRefundDetail> getRefundDetailsByAsOrderId(Long asOrderId) {
        return asRefundDetailMapper.selectByAsOrderId(asOrderId);
    }

    @Override
    public List<AsRefundDetail> getRefundDetailsByServiceNumber(String serviceNumber) {
        return asRefundDetailMapper.selectByServiceNumber(serviceNumber);
    }

    @Override
    public List<AsRefundDetail> getRefundDetailsByType(Long asOrderId, Integer refundType) {
        return asRefundDetailMapper.selectByRefundType(asOrderId, refundType);
    }

    @Override
    public BigDecimal getTotalRefundAmount(Long asOrderId) {
        BigDecimal amount = asRefundDetailMapper.selectTotalRefundAmount(asOrderId);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    public BigDecimal getRefundAmountByType(Long asOrderId, Integer refundType) {
        BigDecimal amount = asRefundDetailMapper.selectRefundAmountByType(asOrderId, refundType);
        return amount != null ? amount : BigDecimal.ZERO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefundStatus(Long id, Integer refundStatus, String refundTransactionId) {
        log.info("更新退款状态, id: {}, status: {}, transactionId: {}", id, refundStatus, refundTransactionId);
        
        int result = asRefundDetailMapper.updateRefundStatus(id, refundStatus, refundTransactionId);
        if (result > 0) {
            log.info("退款状态更新成功");
            return true;
        }
        
        log.error("退款状态更新失败");
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processRefundSuccess(Long id, String refundTransactionId) {
        log.info("处理退款成功, id: {}, transactionId: {}", id, refundTransactionId);
        
        AsRefundDetail refundDetail = asRefundDetailMapper.selectById(id);
        if (refundDetail == null) {
            log.error("退款明细不存在, id: {}", id);
            return false;
        }
        
        refundDetail.setRefundStatus(3); // 退款成功
        refundDetail.setRefundTransactionId(refundTransactionId);
        refundDetail.setRefundTime(LocalDateTime.now());
        
        int result = asRefundDetailMapper.updateById(refundDetail);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processRefundFailure(Long id, String errorMessage) {
        log.info("处理退款失败, id: {}, errorMessage: {}", id, errorMessage);
        
        AsRefundDetail refundDetail = asRefundDetailMapper.selectById(id);
        if (refundDetail == null) {
            log.error("退款明细不存在, id: {}", id);
            return false;
        }
        
        refundDetail.setRefundStatus(4); // 退款失败
        refundDetail.setRefundDescription(refundDetail.getRefundDescription() + " [失败原因: " + errorMessage + "]");
        
        int result = asRefundDetailMapper.updateById(refundDetail);
        return result > 0;
    }

    @Override
    public List<AsRefundDetail> getPendingRefunds(Integer limit) {
        return asRefundDetailMapper.selectPendingRefunds(limit);
    }

    @Override
    public List<AsRefundDetail> getFailedRefunds(Integer limit) {
        return asRefundDetailMapper.selectFailedRefunds(limit);
    }

    @Override
    public List<AsRefundDetail> getServiceFeeRefunds(Long asOrderId) {
        return asRefundDetailMapper.selectServiceFeeRefunds(asOrderId);
    }

    @Override
    public List<AsRefundDetail> getProductRefunds(Long asOrderId) {
        return asRefundDetailMapper.selectProductRefunds(asOrderId);
    }

    @Override
    public List<AsRefundDetail> getShippingFeeRefunds(Long asOrderId) {
        return asRefundDetailMapper.selectShippingFeeRefunds(asOrderId);
    }

    @Override
    public BigDecimal calculateServiceFeeRefund(Long asOrderId, Integer refundQuantity) {
        // 这里需要根据业务逻辑计算服务费退款金额
        // 假设从订单中获取单件服务费，然后乘以退款件数
        log.info("计算服务费退款金额, asOrderId: {}, refundQuantity: {}", asOrderId, refundQuantity);
        
        // TODO: 实现具体的服务费计算逻辑
        // 这里先返回一个示例值，实际应该从订单信息中获取
        BigDecimal unitServiceFee = new BigDecimal("2.00"); // 假设单件服务费为2.00
        return unitServiceFee.multiply(new BigDecimal(refundQuantity));
    }

    @Override
    public boolean validateRefundDetail(RefundDetailDTO refundDetailDTO) {
        if (refundDetailDTO == null) {
            return false;
        }
        
        if (refundDetailDTO.getAsOrderId() == null || refundDetailDTO.getRefundAmount() == null) {
            return false;
        }
        
        if (refundDetailDTO.getRefundAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRefundDetail(Long id) {
        log.info("删除退款明细, id: {}", id);
        
        int result = asRefundDetailMapper.deleteById(id);
        if (result > 0) {
            log.info("退款明细删除成功");
            return true;
        }
        
        log.error("退款明细删除失败");
        return false;
    }

    @Override
    public List<AsRefundDetail> getRefundDetailsByProductId(Long productId) {
        return asRefundDetailMapper.selectByProductId(productId);
    }
}
