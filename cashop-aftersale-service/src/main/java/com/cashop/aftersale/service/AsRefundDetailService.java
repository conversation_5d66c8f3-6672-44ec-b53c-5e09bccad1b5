package com.cashop.aftersale.service;

import com.cashop.aftersale.service.dto.RefundDetailDTO;
import com.cashop.aftersale.dao.entity.AsRefundDetail;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后退款明细Service接口
 * Service层 - 退款明细业务逻辑接口
 */
public interface AsRefundDetailService {

    /**
     * 添加退款明细（通用方法）
     */
    Long addRefundDetail(RefundDetailDTO refundDetailDTO);

    /**
     * 创建商品退款明细
     */
    Long createProductRefundDetail(RefundDetailDTO refundDetailDTO);

    /**
     * 创建服务费退款明细
     */
    Long createServiceFeeRefundDetail(RefundDetailDTO refundDetailDTO);

    /**
     * 创建运费退款明细
     */
    Long createShippingFeeRefundDetail(RefundDetailDTO refundDetailDTO);

    /**
     * 批量创建退款明细
     */
    List<Long> batchCreateRefundDetails(List<RefundDetailDTO> refundDetailDTOs);

    /**
     * 根据售后订单ID查询退款明细列表
     */
    List<AsRefundDetail> getRefundDetailsByAsOrderId(Long asOrderId);

    /**
     * 根据售后单号查询退款明细列表
     */
    List<AsRefundDetail> getRefundDetailsByServiceNumber(String serviceNumber);

    /**
     * 根据退款类型查询退款明细
     */
    List<AsRefundDetail> getRefundDetailsByType(Long asOrderId, Integer refundType);

    /**
     * 查询指定售后订单的退款总金额
     */
    BigDecimal getTotalRefundAmount(Long asOrderId);

    /**
     * 查询指定售后订单指定类型的退款总金额
     */
    BigDecimal getRefundAmountByType(Long asOrderId, Integer refundType);

    /**
     * 更新退款状态
     */
    boolean updateRefundStatus(Long id, Integer refundStatus, String refundTransactionId);

    /**
     * 处理退款成功
     */
    boolean processRefundSuccess(Long id, String refundTransactionId);

    /**
     * 处理退款失败
     */
    boolean processRefundFailure(Long id, String errorMessage);

    /**
     * 查询待退款的明细
     */
    List<AsRefundDetail> getPendingRefunds(Integer limit);

    /**
     * 查询退款失败的明细
     */
    List<AsRefundDetail> getFailedRefunds(Integer limit);

    /**
     * 查询服务费退款明细
     */
    List<AsRefundDetail> getServiceFeeRefunds(Long asOrderId);

    /**
     * 查询商品退款明细
     */
    List<AsRefundDetail> getProductRefunds(Long asOrderId);

    /**
     * 查询运费退款明细
     */
    List<AsRefundDetail> getShippingFeeRefunds(Long asOrderId);

    /**
     * 计算服务费退款金额
     */
    BigDecimal calculateServiceFeeRefund(Long asOrderId, Integer refundQuantity);

    /**
     * 验证退款明细
     */
    boolean validateRefundDetail(RefundDetailDTO refundDetailDTO);

    /**
     * 删除退款明细
     */
    boolean deleteRefundDetail(Long id);

    /**
     * 根据商品ID查询退款明细
     */
    List<AsRefundDetail> getRefundDetailsByProductId(Long productId);
}
