package com.cashop.aftersale.service.dto;

import lombok.Data;

/**
 * 支付结果DTO
 * Service层 - 支付系统集成数据传输对象
 */
@Data
public class PaymentResult {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 交易ID
     */
    private String transactionId;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 支付状态
     */
    private String paymentStatus;

    public boolean isSuccess() {
        return success;
    }
}
