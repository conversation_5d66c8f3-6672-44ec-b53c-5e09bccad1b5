package com.cashop.aftersale.service;

/**
 * 售后MQ消息服务接口
 * 业务层 - 售后相关的MQ消息发送
 */
public interface AftersaleMqService {

    /**
     * 发送售后创建通知
     * 
     * @param serviceNumber 售后单号
     * @param applicantType 申请人类型 1-用户 2-仓库
     */
    void sendAftersaleCreatedNotification(String serviceNumber, Integer applicantType);

    /**
     * 发送售后退款通知
     * 
     * @param serviceNumber 售后单号
     * @param refundAmount 退款金额
     * @param refundType 退款类型 1-商品退款 2-服务费退款 3-运费退款
     */
    void sendAftersaleRefundNotification(String serviceNumber, String refundAmount, Integer refundType);

    /**
     * 发送售后拒绝通知
     * 
     * @param serviceNumber 售后单号
     * @param rejectReason 拒绝原因
     */
    void sendAftersaleRejectedNotification(String serviceNumber, String rejectReason);

    /**
     * 发送售后关闭通知
     * 
     * @param serviceNumber 售后单号
     * @param closeReason 关闭原因
     */
    void sendAftersaleClosedNotification(String serviceNumber, String closeReason);

    /**
     * 发送仓库发货通知
     * 
     * @param serviceNumber 售后单号
     * @param shippingDeadline 发货截止时间
     */
    void sendWarehouseShippingNotification(String serviceNumber, String shippingDeadline);

    /**
     * 发送物流信息已提交通知
     * 
     * @param serviceNumber 售后单号
     * @param logisticsCompany 物流公司
     * @param trackingNumber 物流单号
     */
    void sendLogisticsInfoSubmittedNotification(String serviceNumber, String logisticsCompany, String trackingNumber);
}
