package com.cashop.aftersale.service.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付请求DTO
 * Service层 - 支付系统集成数据传输对象
 */
@Data
@Builder
public class PaymentRequest {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 业务单号(售后单号)
     */
    private String businessNo;

    /**
     * 支付方式
     */
    private String paymentMethod;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 支付描述
     */
    private String description;
}
