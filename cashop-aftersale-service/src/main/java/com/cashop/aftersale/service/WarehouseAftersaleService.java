package com.cashop.aftersale.service;

import com.cashop.aftersale.facade.dto.WarehouseAftersaleCreateRequest;
import com.cashop.aftersale.facade.dto.WarehouseLogisticsInfoRequest;
import com.cashop.aftersale.facade.dto.WarehouseOutboundCheckRequest;
import com.cashop.aftersale.facade.vo.WarehouseAftersaleCreateVO;
import com.cashop.aftersale.facade.vo.WarehouseOutboundCheckVO;

/**
 * 仓库售后接口服务
 * 售后系统对仓库提供的接口服务，负责接收仓库请求并通知相关系统
 */
public interface WarehouseAftersaleService {

    /**
     * 接收仓库次品售后申请
     * 职责：
     * 1. 接收并验证仓库的次品售后申请
     * 2. 创建售后记录
     * 3. 通知第三方供货平台
     * 4. 通知相关系统
     * 
     * @param request 仓库售后创建请求
     * @return 创建结果
     */
    WarehouseAftersaleCreateVO createDefectAftersale(WarehouseAftersaleCreateRequest request);

    /**
     * 接收仓库物流信息
     * 职责：
     * 1. 接收仓库提交的物流信息
     * 2. 更新售后单物流状态
     * 3. 通知第三方供货平台
     * 4. 通知相关系统
     * 
     * @param request 物流信息请求
     * @return 操作结果
     */
    Boolean submitLogisticsInfo(WarehouseLogisticsInfoRequest request);

    /**
     * 提供售后状态查询服务
     * 职责：
     * 1. 查询商品的售后情况
     * 2. 返回售后状态信息供仓库参考
     * 注：
     * - 仓库根据返回信息自行决定是否出库
     * - 售后系统不记录出库检查结果
     * - 售后系统不做出库决策判断
     * 
     * @param request 出库检查请求
     * @return 售后状态信息
     */
    WarehouseOutboundCheckVO checkOutboundAftersale(WarehouseOutboundCheckRequest request);

    /**
     * 处理超时售后单
     * 职责：
     * 1. 定时检查超时的售后单
     * 2. 更新超时售后单状态
     * 3. 通知第三方供货平台
     * 4. 通知相关系统
     */
    void processTimeoutShippingOrders();
}
