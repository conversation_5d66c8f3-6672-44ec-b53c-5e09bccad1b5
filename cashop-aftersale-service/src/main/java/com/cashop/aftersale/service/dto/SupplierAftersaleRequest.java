package com.cashop.aftersale.service.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 供货平台售后申请DTO
 * Service层 - 供货平台集成数据传输对象
 */
@Data
public class SupplierAftersaleRequest {

    /**
     * 我方售后单号
     */
    private String cashopServiceNumber;

    /**
     * 供货平台订单号
     */
    private String supplierOrderNo;

    /**
     * 售后类型
     */
    private Integer asType;

    /**
     * 申请原因
     */
    private String applyReason;

    /**
     * 申请数量
     */
    private Integer applyQuantity;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 币种代码
     */
    private String currencyCode;

    /**
     * 退货体积
     */
    private BigDecimal returnVolume;

    /**
     * 退货重量
     */
    private BigDecimal returnWeight;
}
