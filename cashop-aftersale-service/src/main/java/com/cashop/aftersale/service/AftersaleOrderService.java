package com.cashop.aftersale.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cashop.aftersale.facade.dto.AftersaleApplyRequest;
import com.cashop.aftersale.facade.dto.LogisticsPaymentRequest;
import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.AftersaleApplyPageVO;
import com.cashop.aftersale.facade.vo.AftersaleOrderDetailVO;
import com.cashop.aftersale.facade.vo.RefundableProductVO;
import com.cashop.aftersale.facade.vo.ShippingFeeDetailVO;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;

import java.util.List;

/**
 * 售后订单业务服务接口
 * 业务层 - 核心业务逻辑、流程控制
 */
public interface AftersaleOrderService {

    /**
     * 渲染售后申请页面所需数据
     */
    AftersaleApplyPageVO renderApplyPage(Long userId, String orderNo, String skuOrderNo);

    /**
     * 获取可退款商品列表
     */
    List<RefundableProductVO> getRefundableProducts(String subOrderNo, String currencyCode);

    /**
     * 创建售后申请
     */
    String createAftersaleOrder(AftersaleApplyRequest request);

    /**
     * 获取运费明细
     */
    ShippingFeeDetailVO getShippingFeeDetail(String serviceNumber);

    /**
     * 创建预支付订单
     */
    com.cashop.aftersale.facade.dto.PrePaymentResponse createPrePayment(com.cashop.aftersale.facade.dto.PrePaymentRequest request);

    /**
     * 处理支付回调
     */
    boolean handlePaymentCallback(com.cashop.aftersale.facade.dto.PaymentCallbackRequest request);

    /**
     * 支付物流费用
     */
    boolean payLogisticsFee(LogisticsPaymentRequest request);

    /**
     * 仓库填写物流信息
     */
    boolean updateWarehouseLogistics(com.cashop.aftersale.facade.dto.WarehouseLogisticsRequest request);

    /**
     * 退还退货服务费
     */
    boolean refundReturnServiceFee(com.cashop.aftersale.facade.dto.ReturnServiceFeeRefundRequest request);

    /**
     * 获取售后订单详情
     */
    AftersaleOrderDetailVO getOrderDetail(String serviceNumber);

    /**
     * 分页查询用户售后订单
     */
    IPage<AftersaleOrderDetailVO> getUserOrders(Long userId, Integer asStatus, Integer page, Integer size);

    /**
     * 取消售后申请
     */
    boolean cancelAftersaleOrder(String serviceNumber, Long userId);

    /**
     * 处理供货平台审核结果
     */
    boolean processSupplierApproval(String serviceNumber, Integer approvedQuantity);

    /**
     * 处理实际退款
     */
    boolean processActualRefund(String serviceNumber, Integer refundQuantity);

    /**
     * 处理超时撤销
     */
    void processTimeoutCancellation();

    /**
     * 重新发送到供货平台
     */
    boolean resendToSupplier(String serviceNumber);

    /**
     * 检查SKU是否支持申请售后
     */
    SkuAftersaleSupportVO checkSkuAftersaleSupport(SkuAftersaleSupportRequest request);
}
