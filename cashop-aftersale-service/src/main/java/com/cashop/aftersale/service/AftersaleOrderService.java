package com.cashop.aftersale.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cashop.aftersale.facade.dto.AftersaleApplyRequest;
import com.cashop.aftersale.facade.dto.LogisticsPaymentRequest;
import com.cashop.aftersale.facade.dto.SkuAftersaleSupportRequest;
import com.cashop.aftersale.facade.vo.AftersaleOrderDetailVO;
import com.cashop.aftersale.facade.vo.RefundableProductVO;
import com.cashop.aftersale.facade.vo.ShippingFeeDetailVO;
import com.cashop.aftersale.facade.vo.SkuAftersaleSupportVO;

import java.util.List;

/**
 * 售后订单业务服务接口
 * 业务层 - 核心业务逻辑、流程控制
 */
public interface AftersaleOrderService {

    /**
     * 获取可退款商品列表
     * 业务逻辑：
     * 1. 查询子订单商品信息
     * 2. 计算可申请数量
     * 3. 币种转换处理
     * 4. 构建返回数据
     */
    List<RefundableProductVO> getRefundableProducts(String subOrderNo, String currencyCode);

    /**
     * 创建售后申请
     * 业务逻辑：
     * 1. 业务规则验证
     * 2. 计算申请金额
     * 3. 物流费用计算
     * 4. 创建售后订单
     * 5. 更新商品统计
     * 6. 发送通知
     * 7. 提交第三方平台
     */
    String createAftersaleOrder(AftersaleApplyRequest request);

    /**
     * 获取运费明细
     * 业务逻辑：
     * 1. 查询售后订单
     * 2. 计算剩余支付时间
     * 3. 构建费用明细
     */
    ShippingFeeDetailVO getShippingFeeDetail(String serviceNumber);

    /**
     * 创建预支付订单
     * 业务逻辑：
     * 1. 验证售后订单状态
     * 2. 验证支付金额
     * 3. 调用支付系统创建预支付订单
     * 4. 返回支付参数给前端
     */
    com.cashop.aftersale.facade.dto.PrePaymentResponse createPrePayment(com.cashop.aftersale.facade.dto.PrePaymentRequest request);

    /**
     * 处理支付回调
     * 业务逻辑：
     * 1. 验证回调数据
     * 2. 验证订单状态
     * 3. 更新支付状态
     * 4. 提交第三方平台
     * 5. 发送通知
     */
    boolean handlePaymentCallback(com.cashop.aftersale.facade.dto.PaymentCallbackRequest request);

    /**
     * 支付物流费用
     * 业务逻辑：
     * 1. 验证支付条件
     * 2. 调用支付服务
     * 3. 更新支付状态
     * 4. 提交第三方平台
     * 5. 发送通知
     */
    boolean payLogisticsFee(LogisticsPaymentRequest request);

    /**
     * 仓库填写物流信息
     * 业务逻辑：
     * 1. 验证售后订单状态
     * 2. 更新物流信息
     * 3. 更新增值服务费
     * 4. 计算退款金额
     * 5. 更新订单状态
     */
    boolean updateWarehouseLogistics(com.cashop.aftersale.facade.dto.WarehouseLogisticsRequest request);

    /**
     * 退还退货服务费
     * 业务逻辑：
     * 1. 验证退款条件
     * 2. 计算退款金额
     * 3. 调用退款服务
     * 4. 更新退款状态
     * 5. 记录退款明细
     */
    boolean refundReturnServiceFee(com.cashop.aftersale.facade.dto.ReturnServiceFeeRefundRequest request);

    /**
     * 获取售后订单详情
     * 业务逻辑：
     * 1. 查询订单信息
     * 2. 查询商品信息
     * 3. 查询流程记录
     * 4. 构建详情数据
     */
    AftersaleOrderDetailVO getOrderDetail(String serviceNumber);

    /**
     * 分页查询用户售后订单
     */
    IPage<AftersaleOrderDetailVO> getUserOrders(Long userId, Integer asStatus, Integer page, Integer size);

    /**
     * 取消售后申请
     * 业务逻辑：
     * 1. 验证取消权限
     * 2. 更新订单状态
     * 3. 恢复商品统计
     * 4. 记录操作日志
     * 5. 发送通知
     */
    boolean cancelAftersaleOrder(String serviceNumber, Long userId);

    /**
     * 处理供货平台审核结果
     * 业务逻辑：
     * 1. 更新审核状态
     * 2. 记录批准数量
     * 3. 发送通知
     */
    boolean processSupplierApproval(String serviceNumber, Integer approvedQuantity);

    /**
     * 处理实际退款
     * 业务逻辑：
     * 1. 计算退款金额
     * 2. 调用支付服务
     * 3. 更新订单状态
     * 4. 更新商品统计
     * 5. 发送通知
     */
    boolean processActualRefund(String serviceNumber, Integer refundQuantity);

    /**
     * 处理超时撤销
     * 业务逻辑：
     * 1. 查询超时订单
     * 2. 批量更新状态
     * 3. 恢复商品统计
     * 4. 发送通知
     */
    void processTimeoutCancellation();

    /**
     * 重新发送到供货平台
     */
    boolean resendToSupplier(String serviceNumber);

    /**
     * 检查SKU是否支持申请售后
     * 业务逻辑：
     * 1. 查询商品信息和售后统计
     * 2. 查询在途售后订单
     * 3. 计算退款统计信息
     * 4. 判断售后支持状态
     * 5. 构建响应数据
     */
    SkuAftersaleSupportVO checkSkuAftersaleSupport(SkuAftersaleSupportRequest request);
}
