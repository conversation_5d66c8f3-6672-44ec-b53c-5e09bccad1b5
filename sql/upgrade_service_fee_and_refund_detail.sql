-- 售后系统服务费和退款明细功能升级SQL脚本
-- 执行时间: 2025-01-06
-- 说明: 添加订单服务费字段和退款明细表

-- 1. 修改售后订单表，添加服务费相关字段
ALTER TABLE as_order 
ADD COLUMN order_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '订单服务费总额(按件数计算)' AFTER logistics_expire_time,
ADD COLUMN unit_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '单件服务费' AFTER order_service_fee;

-- 2. 创建售后退款明细表
CREATE TABLE as_refund_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    service_number VARCHAR(32) NOT NULL COMMENT '售后单号',
    
    -- 退款类型和金额
    refund_type TINYINT NOT NULL COMMENT '退款类型:1-商品退款,2-服务费退款,3-运费退款',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退款金额',
    currency_code VARCHAR(8) NOT NULL COMMENT '币种代码',
    
    -- 退款明细说明
    refund_reason VARCHAR(200) COMMENT '退款原因',
    refund_description VARCHAR(500) COMMENT '退款明细描述',
    
    -- 商品相关信息(仅商品退款时填写)
    product_id BIGINT COMMENT '商品ID(as_product表的id)',
    sku_id BIGINT COMMENT 'SKU ID',
    product_name VARCHAR(200) COMMENT '商品名称',
    refund_quantity INT DEFAULT 0 COMMENT '退款件数',
    unit_price DECIMAL(10,2) DEFAULT 0 COMMENT '单价',
    
    -- 服务费相关信息(仅服务费退款时填写)
    service_fee_quantity INT DEFAULT 0 COMMENT '服务费对应件数',
    unit_service_fee DECIMAL(10,2) DEFAULT 0 COMMENT '单件服务费',
    
    -- 退款状态和时间
    refund_status TINYINT DEFAULT 1 COMMENT '退款状态:1-待退款,2-退款中,3-退款成功,4-退款失败',
    refund_transaction_id VARCHAR(64) COMMENT '退款交易ID',
    refund_time DATETIME COMMENT '退款完成时间',
    
    -- 通用审计字段
    is_deleted TINYINT UNSIGNED DEFAULT '0' NULL COMMENT '0 有效 1 无效',
    created_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL COMMENT '创建时间',
    created_by VARCHAR(255) NOT NULL COMMENT '创建人',
    last_modified_time TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) NOT NULL ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '最近一次修改时间',
    updated_by VARCHAR(255) NULL COMMENT '修改人',

    INDEX idx_as_order_id (as_order_id),
    INDEX idx_service_number (service_number),
    INDEX idx_refund_type (refund_type),
    INDEX idx_refund_status (refund_status),
    INDEX idx_product_id (product_id),
    INDEX idx_refund_transaction_id (refund_transaction_id),
    INDEX idx_is_deleted (is_deleted),
    INDEX idx_created_time (created_time)
) COMMENT='售后退款明细表';

-- 3. 添加外键约束(可选，根据实际情况决定是否添加)
-- ALTER TABLE as_refund_detail 
-- ADD CONSTRAINT fk_refund_detail_as_order 
-- FOREIGN KEY (as_order_id) REFERENCES as_order(id) ON DELETE CASCADE;

-- 4. 初始化现有数据的服务费字段(如果有历史数据)
-- UPDATE as_order 
-- SET unit_service_fee = 2.00, 
--     order_service_fee = unit_service_fee * apply_quantity 
-- WHERE unit_service_fee = 0 OR unit_service_fee IS NULL;

-- 5. 验证表结构
-- DESCRIBE as_order;
-- DESCRIBE as_refund_detail;

-- 6. 验证索引
-- SHOW INDEX FROM as_refund_detail;

-- 升级完成提示
SELECT 'as_order表服务费字段添加完成' as message;
SELECT 'as_refund_detail表创建完成' as message;
SELECT '售后系统服务费和退款明细功能升级完成' as message;
