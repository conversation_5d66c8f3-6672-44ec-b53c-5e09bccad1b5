-- 事务一致性框架 - 事务日志表
CREATE TABLE transaction_task_log (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  task_id VARCHAR(64) NOT NULL,
  task_type VARCHAR(64),
  task_class_name VARCHAR(128) NOT NULL,
  transaction_type VARCHAR(32) NOT NULL,
  status VARCHAR(32) NOT NULL,
  retry_status VARCHAR(32),
  reversal_status VARCHAR(32),
  error_code VARCHAR(64),
  error_message VARCHAR(256),
  times INT,
  update_time DATETIME NOT NULL,
  create_time DATETIME NOT NULL,
  next_execute_time DATETIME,
  request_additional_info VARCHAR(4000),
  result_additional_info VARCHAR(4000),
  UNIQUE KEY uk_task_id_type (task_id, task_type),
  KEY idx_update_time (update_time, transaction_type),
  KEY idx_next_execute_time (next_execute_time, transaction_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='事务一致性框架 - 事务日志表';
