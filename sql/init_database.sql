-- 创建数据库
CREATE DATABASE IF NOT EXISTS cashop_aftersale DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE cashop_aftersale;

-- 售后订单主表
CREATE TABLE as_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    service_number VARCHAR(32) NOT NULL COMMENT 'Cashop售后单号',
    order_no VARCHAR(32) NOT NULL COMMENT 'Cashop订单号',
    sub_order_no VARCHAR(32) COMMENT 'Cashop子订单号',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    
    -- 售后类型和状态
    as_type TINYINT NOT NULL COMMENT '售后类型:1-发货前退款,2-发货后退款,3-发货后退货退款',
    as_status TINYINT NOT NULL DEFAULT 1 COMMENT '售后状态:1-待审核,2-审核通过,3-审核拒绝,4-退款中,5-退款完成,6-已撤销',
    
    -- 申请信息
    apply_reason VARCHAR(500) COMMENT '申请原因',
    apply_quantity INT NOT NULL COMMENT '申请总件数',
    apply_amount DECIMAL(10,2) NOT NULL COMMENT '申请退款总金额',
    
    -- 服务费相关
    order_service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '订单服务费总额(按件收取)',
    unit_service_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '单件服务费',
    
    -- 审核信息
    approved_quantity INT DEFAULT 0 COMMENT '供货平台批准件数',
    actual_refund_quantity INT DEFAULT 0 COMMENT '实际退款件数',
    actual_refund_amount DECIMAL(10,2) DEFAULT 0.00 COMMENT '实际退款金额',
    
    -- 物流费用(仅退货退款)
    shipping_fee DECIMAL(10,2) DEFAULT 0.00 COMMENT '运费',
    return_volume DECIMAL(10,3) DEFAULT 0.000 COMMENT '退货体积(立方米)',
    return_weight DECIMAL(10,3) DEFAULT 0.000 COMMENT '退货重量(公斤)',
    logistics_payment_deadline DATETIME COMMENT '物流费支付截止时间',
    logistics_payment_status TINYINT DEFAULT 0 COMMENT '物流费支付状态:0-未支付,1-已支付,2-已退还',
    
    -- 供货平台信息
    supplier_order_no VARCHAR(64) COMMENT '供货平台订单号',
    supplier_service_no VARCHAR(64) COMMENT '供货平台售后单号',
    
    -- 时间字段
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY uk_service_number (service_number),
    KEY idx_order_no (order_no),
    KEY idx_sub_order_no (sub_order_no),
    KEY idx_user_id (user_id),
    KEY idx_as_status (as_status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='售后订单主表';

-- 子订单商品表
CREATE TABLE as_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    
    -- 订单关联信息
    order_no VARCHAR(32) NOT NULL COMMENT '主订单号',
    sub_order_no VARCHAR(32) NOT NULL COMMENT '子订单号',
    
    -- 商品基本信息
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
    product_image VARCHAR(500) COMMENT '商品图片URL',
    sku_id BIGINT NOT NULL COMMENT 'SKU ID',
    sku_name VARCHAR(255) COMMENT 'SKU名称',
    sku_attributes JSON COMMENT 'SKU属性(JSON格式)',
    
    -- 价格和数量
    unit_price DECIMAL(10,2) NOT NULL COMMENT '商品单价',
    purchase_quantity INT NOT NULL COMMENT '购买数量',
    refunded_quantity INT DEFAULT 0 COMMENT '已退款数量',
    available_quantity INT NOT NULL COMMENT '可申请退款数量',
    
    -- 体积重量信息
    unit_volume DECIMAL(10,3) DEFAULT 0.000 COMMENT '单件体积(立方米)',
    unit_weight DECIMAL(10,3) DEFAULT 0.000 COMMENT '单件重量(公斤)',
    
    -- 时间字段
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    KEY idx_order_no (order_no),
    KEY idx_sub_order_no (sub_order_no),
    KEY idx_product_id (product_id),
    KEY idx_sku_id (sku_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='子订单商品表';

-- 流程记录表
CREATE TABLE as_process_log (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    from_status TINYINT COMMENT '原状态',
    to_status TINYINT NOT NULL COMMENT '目标状态',
    operator_id BIGINT COMMENT '操作人ID',
    operator_name VARCHAR(64) COMMENT '操作人姓名',
    remark VARCHAR(500) COMMENT '备注',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    -- 索引
    KEY idx_as_order_id (as_order_id),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程记录表';

-- 退款明细表
CREATE TABLE as_refund_detail (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    as_order_id BIGINT NOT NULL COMMENT '售后订单ID',
    service_number VARCHAR(32) NOT NULL COMMENT '售后单号',
    
    -- 退款类型和金额
    refund_type TINYINT NOT NULL COMMENT '退款类型:1-商品退款,2-服务费退款,3-运费退款',
    refund_amount DECIMAL(10,2) NOT NULL COMMENT '退款金额',
    refund_quantity INT DEFAULT 0 COMMENT '退款件数(商品退款时使用)',
    
    -- 退款状态和时间
    refund_status TINYINT NOT NULL DEFAULT 1 COMMENT '退款状态:1-待退款,2-退款中,3-退款成功,4-退款失败',
    refund_transaction_id VARCHAR(64) COMMENT '退款交易ID',
    refund_time DATETIME COMMENT '退款完成时间',
    
    -- 时间字段
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    KEY idx_as_order_id (as_order_id),
    KEY idx_service_number (service_number),
    KEY idx_refund_type (refund_type),
    KEY idx_refund_status (refund_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='退款明细表';

-- 第三方消息表
CREATE TABLE third_party_message (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    message_id VARCHAR(64) NOT NULL COMMENT '消息ID(第三方提供的唯一标识)',
    business_type TINYINT NOT NULL COMMENT '业务类型:1-售后申请,2-审核结果,3-退款结果,4-状态变更',
    business_id VARCHAR(64) NOT NULL COMMENT '关联的业务单号(如售后单号)',
    
    -- 消息内容
    message_content TEXT NOT NULL COMMENT '消息内容(JSON格式)',
    source_system VARCHAR(32) NOT NULL COMMENT '来源系统',
    
    -- 处理状态
    process_status TINYINT NOT NULL DEFAULT 1 COMMENT '处理状态:1-待处理,2-处理中,3-处理成功,4-处理失败',
    retry_times INT DEFAULT 0 COMMENT '重试次数',
    error_message VARCHAR(1000) COMMENT '错误信息',
    
    -- 时间字段
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    processed_at DATETIME COMMENT '处理完成时间',
    
    -- 索引
    UNIQUE KEY uk_message_id (message_id),
    KEY idx_business_type (business_type),
    KEY idx_business_id (business_id),
    KEY idx_process_status (process_status),
    KEY idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='第三方消息表';

-- 插入一些测试数据
INSERT INTO as_order (service_number, order_no, sub_order_no, user_id, as_type, as_status, apply_reason, apply_quantity, apply_amount, order_service_fee, unit_service_fee) VALUES
('AS202412280001', 'CO202412280001', 'SUB202412280001', 12345, 1, 1, '商品质量问题', 2, 199.98, 20.00, 10.00),
('AS202412280002', 'CO202412280002', 'SUB202412280002', 12346, 2, 2, '不喜欢', 1, 99.99, 10.00, 10.00);

INSERT INTO as_product (order_no, sub_order_no, product_id, product_name, sku_id, sku_name, unit_price, purchase_quantity, available_quantity, unit_volume, unit_weight) VALUES
('CO202412280001', 'SUB202412280001', 1001, '测试商品A', 2001, '红色-L', 99.99, 3, 2, 0.001, 0.5),
('CO202412280002', 'SUB202412280002', 1002, '测试商品B', 2002, '蓝色-M', 99.99, 1, 1, 0.002, 0.8);
