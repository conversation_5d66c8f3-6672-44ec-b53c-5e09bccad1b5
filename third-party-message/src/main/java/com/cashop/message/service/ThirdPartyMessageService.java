package com.cashop.message.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cashop.message.entity.ThirdPartyMessage;
import com.cashop.message.vo.MessageStatisticsVO;

/**
 * 第三方消息处理服务接口
 * 职责：消息接收、异步消费、失败重试、状态追踪
 */
public interface ThirdPartyMessageService {

    /**
     * 接收第三方消息
     */
    void receiveMessage(String rawMessage);

    /**
     * 异步消费消息
     */
    void consumeMessageAsync(Long messageId);

    /**
     * 重试失败消息
     */
    void retryFailedMessages();

    /**
     * 手动重试消息
     */
    boolean retryMessage(Long messageId);

    /**
     * 分页查询消息记录
     */
    IPage<ThirdPartyMessage> getMessages(String businessId, Integer consumeStatus, 
                                        Integer page, Integer size);

    /**
     * 获取原始消息数据
     */
    String getRawMessage(Long messageId);

    /**
     * 获取消息统计信息
     */
    MessageStatisticsVO getMessageStatistics();

    /**
     * 清理过期消息
     */
    void cleanExpiredMessages(int retentionDays);
}
