# Cashop售后系统项目结构说明

## 1. 模块划分

按照设计文档3.1模块职责划分，项目分为5个模块：

```
cashop-aftersale/
├── cashop-aftersale-common/     # 公共模块
├── cashop-aftersale-dao/        # 数据访问层
├── cashop-aftersale-facade/     # 接口层
├── cashop-aftersale-service/    # 业务层
└── cashop-aftersale-web/        # Web层
```

## 2. 各模块职责

### 2.1 cashop-aftersale-common (公共模块)
**职责**: 工具类、共享组件、常量定义

**包含内容**:
```
src/main/java/com/cashop/aftersale/common/
├── constants/
│   └── AftersaleConstants.java      # 业务常量定义
├── enums/
│   ├── AsTypeEnum.java             # 售后类型枚举
│   ├── AsStatusEnum.java           # 售后状态枚举
│   └── LogisticsPaidEnum.java      # 支付状态枚举
├── exception/
│   └── BusinessException.java      # 业务异常
├── result/
│   └── Result.java                 # 通用返回结果
└── utils/
    ├── ServiceNumberGenerator.java # 单号生成器
    ├── DateUtils.java              # 日期工具类
    └── MoneyUtils.java             # 金额工具类
```

### 2.2 cashop-aftersale-dao (数据访问层)
**职责**: 数据库操作、MyBatis配置

**包含内容**:
```
src/main/java/com/cashop/aftersale/dao/
├── entity/
│   ├── AsOrder.java                # 售后订单实体(包含服务费字段)
│   ├── AsProduct.java              # 商品实体
│   ├── AsRefundDetail.java         # 退款明细实体
│   ├── AsProcessLog.java           # 流程记录实体
│   └── ThirdPartyMessage.java      # 第三方消息实体
├── mapper/
│   ├── AsOrderMapper.java          # 订单Mapper
│   ├── AsProductMapper.java        # 商品Mapper
│   ├── AsRefundDetailMapper.java   # 退款明细Mapper
│   ├── AsProcessLogMapper.java     # 流程记录Mapper
│   └── ThirdPartyMessageMapper.java # 消息Mapper
└── config/
    └── MybatisPlusConfig.java      # MyBatis配置
```

### 2.3 cashop-aftersale-facade (接口层)
**职责**: 对外接口实现、参数验证

**包含内容**:
```
src/main/java/com/cashop/aftersale/facade/
├── AftersaleFacade.java            # 对外接口定义
├── impl/
│   └── AftersaleFacadeImpl.java    # 接口实现(参数验证、异常处理)
├── dto/
│   ├── AftersaleApplyRequest.java  # 申请请求DTO
│   └── LogisticsPaymentRequest.java # 支付请求DTO
└── vo/
    ├── RefundableProductVO.java    # 可退款商品VO
    ├── ShippingFeeDetailVO.java    # 运费明细VO
    └── AftersaleOrderDetailVO.java # 订单详情VO
```

### 2.4 cashop-aftersale-service (业务层)
**职责**: 业务逻辑实现、流程控制

**包含内容**:
```
src/main/java/com/cashop/aftersale/service/
├── AftersaleOrderService.java      # 核心业务服务接口
├── AsRefundDetailService.java      # 退款明细服务接口
├── impl/
│   ├── AftersaleOrderServiceImpl.java # 业务逻辑实现(集成服务费处理)
│   ├── AsRefundDetailServiceImpl.java # 退款明细服务实现
│   ├── LogisticsCalculationServiceImpl.java # 物流计算实现
│   └── ThirdPartyMessageServiceImpl.java # 消息处理实现
├── integration/                    # 第三方集成服务
│   ├── OrderIntegrationService.java # 订单系统集成
│   ├── PaymentIntegrationService.java # 支付系统集成
│   └── SupplierIntegrationService.java # 供货平台集成
├── dto/                           # 内部传输对象
│   ├── RefundDetailDTO.java       # 退款明细DTO
│   ├── OrderInfoDTO.java
│   ├── PaymentRequest.java
│   └── SupplierAftersaleRequest.java
└── task/
    └── AftersaleScheduledTask.java # 定时任务
```

### 2.5 cashop-aftersale-web (Web层)
**职责**: HTTP接口、配置管理

**包含内容**:
```
src/main/java/com/cashop/aftersale/web/
├── AftersaleApplication.java       # 启动类
├── controller/
│   ├── AftersaleController.java    # 用户端接口
│   ├── RefundDetailController.java # 退款明细接口
│   ├── AdminController.java        # 管理端接口
│   └── ThirdPartyController.java   # 第三方接口
├── config/
│   ├── WebConfig.java             # Web配置
│   └── GlobalExceptionHandler.java # 全局异常处理
└── health/
    └── HealthController.java       # 健康检查

src/main/resources/
├── application.yml                 # 应用配置
├── mapper/                        # SQL映射文件
└── log4j2-local.xml               # 日志配置
```

## 3. 依赖关系

### 3.1 模块依赖图
```
cashop-aftersale-web
    ↓ 依赖
cashop-aftersale-facade
    ↓ 依赖
cashop-aftersale-service
    ↓ 依赖
cashop-aftersale-dao
    ↓ 依赖
cashop-aftersale-common
```

### 3.2 Maven依赖配置
```xml
<!-- 父POM -->
<modules>
    <module>cashop-aftersale-common</module>
    <module>cashop-aftersale-dao</module>
    <module>cashop-aftersale-facade</module>
    <module>cashop-aftersale-service</module>
    <module>cashop-aftersale-web</module>
</modules>

<!-- Web模块依赖 -->
<dependencies>
    <dependency>
        <groupId>com.cashop</groupId>
        <artifactId>cashop-aftersale-facade</artifactId>
    </dependency>
</dependencies>
```

## 4. 数据流转

### 4.1 请求处理流程
```
HTTP请求 → Web层Controller → Facade接口层 → Service业务层 → DAO数据层 → 数据库
```

### 4.2 各层职责
1. **Web层**: 接收HTTP请求，路由到对应接口
2. **Facade层**: 参数验证，异常处理，接口适配
3. **Service层**: 业务逻辑处理，流程控制，第三方调用
4. **DAO层**: 数据库操作，SQL执行
5. **Common层**: 提供通用工具和组件支持

## 5. 配置管理

### 5.1 配置文件分层
- **Web层**: application.yml (服务端口、上下文路径)
- **Service层**: 业务配置 (运费规则、超时时间)
- **DAO层**: 数据库配置 (连接池、MyBatis)
- **Common层**: 通用配置 (常量定义)

### 5.2 环境配置
- **local**: 本地开发环境
- **dev**: 开发测试环境
- **test**: 测试环境
- **prod**: 生产环境

## 6. 部署结构

### 6.1 打包方式
- 最终打包为单个可执行JAR包
- 包含所有依赖模块
- 支持Docker容器化部署

### 6.2 启动方式
```bash
# 本地启动
java -jar cashop-aftersale-web.jar --spring.profiles.active=local

# Docker启动
docker run -p 8080:8080 cashop-aftersale:latest
```

## 7. 开发规范

### 7.1 包命名规范
- 基础包名: `com.cashop.aftersale`
- 模块包名: `com.cashop.aftersale.{module}`
- 功能包名: `com.cashop.aftersale.{module}.{function}`

### 7.2 类命名规范
- 实体类: `XxxEntity` 或直接使用业务名
- 服务接口: `XxxService`
- 服务实现: `XxxServiceImpl`
- 控制器: `XxxController`
- DTO: `XxxDTO` 或 `XxxRequest/Response`
- VO: `XxxVO`

### 7.3 方法命名规范
- 查询: `get/find/query/select`
- 新增: `create/add/insert`
- 更新: `update/modify`
- 删除: `delete/remove`
- 验证: `validate/check`
- 处理: `process/handle`

## 8. TODO集成清单

### 8.1 订单系统集成 (Service层)
- [ ] `OrderIntegrationService.getOrderInfo()`
- [ ] `OrderIntegrationService.getOrderProducts()`
- [ ] `OrderIntegrationService.validateOrderStatus()`

### 8.2 支付系统集成 (Service层)
- [ ] `PaymentIntegrationService.processLogisticsPayment()`
- [ ] `PaymentIntegrationService.processRefund()`
- [ ] `PaymentIntegrationService.refundLogisticsFee()`

### 8.3 供货平台集成 (Service层)
- [ ] `SupplierIntegrationService.submitAftersaleRequest()`
- [ ] `SupplierIntegrationService.queryProcessStatus()`
- [ ] `SupplierIntegrationService.cancelSupplierRequest()`

这样的模块划分确保了：
- **职责清晰**: 每个模块有明确的职责边界
- **依赖合理**: 单向依赖，避免循环依赖
- **易于维护**: 模块化设计，便于独立开发和测试
- **扩展性好**: 新功能可以在对应模块中扩展

## 9. 新增功能说明 (v1.1)

### 9.1 服务费和退款明细功能

#### 9.1.1 订单服务费处理
- **服务费计算**: 按件收取用户服务费，在售后申请时计算总服务费
- **字段设计**:
  - `unit_service_fee`: 单件服务费
  - `order_service_fee`: 订单服务费总额(单件服务费 × 申请件数)
- **业务逻辑**: 在创建售后申请时自动计算并记录服务费信息

#### 9.1.2 退款明细管理
- **明细分类**:
  - 商品退款明细(refund_type=1)
  - 服务费退款明细(refund_type=2)
  - 运费退款明细(refund_type=3)
- **状态管理**: 待退款 → 退款中 → 退款成功/失败
- **详细记录**: 每笔退款都有完整的明细记录，包括金额、数量、原因等

#### 9.1.3 退款处理流程
1. 计算商品退款金额和服务费退款金额
2. 分别创建商品退款明细和服务费退款明细
3. 调用支付系统执行退款
4. 更新退款明细状态
5. 记录退款完成信息

#### 9.1.4 相关接口
- `GET /api/refund-detail/list/{serviceNumber}` - 查询退款明细列表
- `GET /api/refund-detail/product-refunds/{asOrderId}` - 查询商品退款明细
- `GET /api/refund-detail/service-fee-refunds/{asOrderId}` - 查询服务费退款明细
- `GET /api/refund-detail/total-amount/{asOrderId}` - 查询退款总金额

#### 9.1.5 数据库变更
- 修改 `as_order` 表，添加 `order_service_fee` 和 `unit_service_fee` 字段
- 新增 `as_refund_detail` 退款明细表
- 执行SQL脚本: `sql/upgrade_service_fee_and_refund_detail.sql`

---
**文档版本**: v1.1
**创建时间**: 2025-07-28
**更新时间**: 2025-01-06
**维护人员**: 开发团队
